<template>
  <view class="address-edit-container">
    <view class="form-section">
      <view class="form-item">
        <text class="label">收货人</text>
        <input
          class="input"
          v-model="formData.name"
          placeholder="请输入收货人姓名"
          maxlength="20"
        />
      </view>

      <view class="form-item">
        <text class="label">手机号</text>
        <input
          class="input"
          v-model="formData.phone"
          placeholder="请输入手机号码"
          type="number"
          maxlength="11"
        />
      </view>

      <picker mode="region" :value="regionArray" @change="onRegionChange">
        <view class="form-item">
          <text class="label">所在地区</text>
          <view class="region-selector">
            <text class="region-text" v-if="regionText">{{ regionText }}</text>
            <text class="placeholder" v-else>请选择省市区</text>
            <uni-icons type="right" size="14" color="#ccc"></uni-icons>
          </view>
        </view>
      </picker>

      <view class="form-item">
        <text class="label">详细地址</text>
        <textarea
          class="textarea"
          v-model="formData.detailAddress"
          placeholder="请输入详细地址，如街道、楼牌号等"
          maxlength="200"
          auto-height
        />
      </view>

      <view class="form-item">
        <text class="label">设为默认</text>
        <switch 
          :checked="formData.isDefault" 
          @change="onDefaultChange"
          color="#ff6700"
        />
      </view>
    </view>

    <view class="button-section">
      <button class="save-btn" @click="saveAddress" :disabled="!canSave">
        {{ isEdit ? '保存修改' : '保存地址' }}
      </button>
    </view>
  </view>
</template>

<script>
import { getAddressDetail, addAddress, updateAddress } from '@/api/shop.js'

export default {
  data() {
    return {
      isEdit: false,
      addressId: null,
      formData: {
        name: '',
        phone: '',
        province: '',
        city: '',
        district: '',
        detailAddress: '',
        isDefault: false
      },
      regionArray: ['', '', '']
    }
  },
  
  computed: {
    // 地区文本
    regionText() {
      if (this.formData.province && this.formData.city && this.formData.district) {
        return `${this.formData.province} ${this.formData.city} ${this.formData.district}`
      }
      return ''
    },
    
    // 是否可以保存
    canSave() {
      return this.formData.name.trim() && 
             this.formData.phone.trim() &&
             this.formData.province && 
             this.formData.city && 
             this.formData.district &&
             this.formData.detailAddress.trim()
    }
  },
  
  onLoad(options) {
    if (options.id) {
      this.isEdit = true
      this.addressId = options.id
      this.loadAddressDetail()
      
      uni.setNavigationBarTitle({
        title: '编辑地址'
      })
    } else {
      uni.setNavigationBarTitle({
        title: '添加地址'
      })
    }
  },
  
  methods: {
    // 加载地址详情
    async loadAddressDetail() {
      uni.showLoading({
        title: '加载中...'
      })

      try {
        // 调用API获取地址详情
        const res = await getAddressDetail(this.addressId)
        console.log('地址详情加载结果:', res)

        if (res.code === 200 && res.data) {
          const address = res.data
          this.formData = { ...address }
          this.regionArray = [address.province, address.city, address.district]
        } else {
          // API调用失败，尝试从本地存储获取
          this.loadLocalAddressDetail()
        }

        uni.hideLoading()
      } catch (error) {
        console.error('加载地址详情失败:', error)

        // API调用失败，尝试从本地存储获取
        this.loadLocalAddressDetail()

        uni.hideLoading()
        uni.showToast({
          title: '加载失败，显示本地数据',
          icon: 'none'
        })
      }
    },

    // 从本地存储加载地址详情
    loadLocalAddressDetail() {
      try {
        const addressList = uni.getStorageSync('addressList') || []
        const address = addressList.find(item => item.id == this.addressId)

        if (address) {
          this.formData = { ...address }
          this.regionArray = [address.province, address.city, address.district]
          console.log('从本地加载地址详情:', address)
        } else {
          uni.showToast({
            title: '地址不存在',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('从本地加载地址详情失败:', error)
      }
    },
    
    // 地区选择变化
    onRegionChange(e) {
      const regions = e.detail.value
      this.regionArray = regions
      this.formData.province = regions[0]
      this.formData.city = regions[1]
      this.formData.district = regions[2]
    },
    
    // 默认地址开关变化
    onDefaultChange(e) {
      this.formData.isDefault = e.detail.value
    },
    
    // 表单验证
    validateForm() {
      if (!this.formData.name.trim()) {
        uni.showToast({
          title: '请输入收货人姓名',
          icon: 'none'
        })
        return false
      }

      if (!this.formData.phone.trim()) {
        uni.showToast({
          title: '请输入手机号码',
          icon: 'none'
        })
        return false
      }

      // 手机号格式验证
      const phoneReg = /^1[3-9]\d{9}$/
      if (!phoneReg.test(this.formData.phone)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none'
        })
        return false
      }

      if (!this.formData.province || !this.formData.city || !this.formData.district) {
        uni.showToast({
          title: '请选择所在地区',
          icon: 'none'
        })
        return false
      }

      if (!this.formData.detailAddress.trim()) {
        uni.showToast({
          title: '请输入详细地址',
          icon: 'none'
        })
        return false
      }

      return true
    },
    
    // 保存地址
    async saveAddress() {
      if (!this.validateForm()) {
        return
      }

      uni.showLoading({
        title: '保存中...'
      })

      try {
        let result

        if (this.isEdit) {
          // 编辑地址
          console.log('更新地址:', this.addressId, this.formData)
          result = await updateAddress(this.addressId, this.formData)
        } else {
          // 添加地址
          console.log('添加地址:', this.formData)
          result = await addAddress(this.formData)
        }

        console.log('保存地址API结果:', result)

        if (result.code === 200) {
          // API调用成功
          this.saveToLocalStorage(result.data)

          uni.hideLoading()
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })

          // 返回上一页并刷新
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          throw new Error(result.msg || '保存失败')
        }

      } catch (error) {
        console.error('保存地址失败:', error)

        // API调用失败，保存到本地存储
        this.saveToLocalStorage()

        uni.hideLoading()
        uni.showToast({
          title: '保存成功（本地）',
          icon: 'success'
        })

        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },

    // 保存到本地存储
    saveToLocalStorage(serverData = null) {
      try {
        let addressList = uni.getStorageSync('addressList') || []

        if (this.isEdit) {
          // 编辑模式：更新现有地址
          const index = addressList.findIndex(item => item.id == this.addressId)
          if (index > -1) {
            addressList[index] = {
              ...addressList[index],
              ...this.formData,
              id: this.addressId
            }
          }
        } else {
          // 添加模式：新增地址
          const newAddress = {
            ...this.formData,
            id: serverData?.id || Date.now(), // 使用服务器返回的ID或时间戳
            createTime: new Date().toISOString()
          }

          // 如果设置为默认地址，取消其他地址的默认状态
          if (newAddress.isDefault) {
            addressList.forEach(item => {
              item.isDefault = false
            })
          }

          addressList.push(newAddress)
        }

        uni.setStorageSync('addressList', addressList)
        console.log('地址已保存到本地存储:', addressList)
      } catch (error) {
        console.error('保存到本地存储失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.address-edit-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.form-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.label {
  font-size: 30rpx;
  color: #333;
  width: 160rpx;
  flex-shrink: 0;
}

.input {
  flex: 1;
  font-size: 30rpx;
  color: #333;

  &::placeholder {
    color: #ccc;
  }
}

.textarea {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  min-height: 80rpx;

  &::placeholder {
    color: #ccc;
  }
}

.region-selector {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.region-text {
  font-size: 30rpx;
  color: #333;
}

.placeholder {
  font-size: 30rpx;
  color: #ccc;
}

.button-section {
  padding: 30rpx;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background-color: #ff6700;
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  
  &:disabled {
    background-color: #ccc;
  }
}
</style>
