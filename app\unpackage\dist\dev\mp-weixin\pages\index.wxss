@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-2a183b29 {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f7;
  position: relative;
}
.custom-nav.data-v-2a183b29 {
  position: fixed;
  width: 100%;
  background-color: #3c96f3;
  z-index: 999;
}
.custom-nav .nav-content.data-v-2a183b29 {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15px;
}
.custom-nav .nav-content .title-text text.data-v-2a183b29 {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
}
.content.data-v-2a183b29 {
  width: 100%;
  height: 100%;
}
.content .banner.data-v-2a183b29 {
  width: 100%;
  height: 350rpx;
}
.content .banner image.data-v-2a183b29 {
  width: 100%;
  height: 100%;
}
.content .category-section.data-v-2a183b29 {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  background-color: #fff;
  margin-bottom: 20rpx;
}
.content .category-section .category-item.data-v-2a183b29 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.content .category-section .category-item.data-v-2a183b29:active {
  background-color: #f5f5f5;
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.content .category-section .category-item image.data-v-2a183b29 {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}
.content .category-section .category-item text.data-v-2a183b29 {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  line-height: 1.2;
}
.content .plant-section.data-v-2a183b29 {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 20rpx;
}
.content .plant-section .section-header.data-v-2a183b29 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.content .plant-section .section-header .title.data-v-2a183b29 {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d8659;
}
.content .plant-section .plant-list.data-v-2a183b29 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.content .plant-section .plant-list .plant-item.data-v-2a183b29 {
  width: 48%;
  background-color: #f9f9f9;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}
.content .plant-section .plant-list .plant-item image.data-v-2a183b29 {
  width: 100%;
  height: 320rpx;
}
.content .plant-section .plant-list .plant-item .plant-info.data-v-2a183b29 {
  padding: 15rpx;
}
.content .plant-section .plant-list .plant-item .plant-info .plant-name.data-v-2a183b29 {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.content .plant-section .plant-list .plant-item .plant-info .plant-desc.data-v-2a183b29 {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}
