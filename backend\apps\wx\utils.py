import requests
import json
import logging
from django.conf import settings
from django.utils import timezone
from dvadmin.utils.validator import CustomValidationError

logger = logging.getLogger(__name__)


class WxApiClient:
    """微信API客户端"""
    
    def __init__(self):
        # 从设置中获取微信小程序配置
        self.app_id = getattr(settings, 'WX_MINIPROGRAM_APP_ID', '')
        self.app_secret = getattr(settings, 'WX_MINIPROGRAM_APP_SECRET', '')
        
        if not self.app_id or not self.app_secret:
            logger.warning("微信小程序配置未设置，请在settings中配置WX_MINIPROGRAM_APP_ID和WX_MINIPROGRAM_APP_SECRET")
    
    def code2session(self, code):
        """
        通过code获取session_key和openid
        
        Args:
            code: 微信登录凭证
            
        Returns:
            dict: 包含openid, session_key等信息
            
        Raises:
            CustomValidationError: 当API调用失败时
        """
        url = 'https://api.weixin.qq.com/sns/jscode2session'
        params = {
            'appid': self.app_id,
            'secret': self.app_secret,
            'js_code': code,
            'grant_type': 'authorization_code'
        }
        
        try:
            logger.info(f"调用微信code2session接口，code: {code}")
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"微信API返回数据: {data}")
            
            # 检查是否有错误
            if 'errcode' in data:
                error_msg = self._get_error_message(data['errcode'])
                logger.error(f"微信API调用失败: {error_msg}, errcode: {data['errcode']}")
                raise CustomValidationError(f"微信登录失败: {error_msg}")
            
            # 检查必要字段
            if 'openid' not in data:
                logger.error(f"微信API返回数据缺少openid: {data}")
                raise CustomValidationError("微信登录失败: 获取用户标识失败")
            
            return data
            
        except requests.RequestException as e:
            logger.error(f"微信API请求异常: {str(e)}")
            raise CustomValidationError("微信登录失败: 网络请求异常")
        except json.JSONDecodeError as e:
            logger.error(f"微信API返回数据解析失败: {str(e)}")
            raise CustomValidationError("微信登录失败: 数据解析异常")
    
    def _get_error_message(self, errcode):
        """获取错误码对应的错误信息"""
        error_messages = {
            -1: '系统繁忙，此时请开发者稍候再试',
            0: '请求成功',
            40013: '不合法的 AppID',
            40014: '不合法的 access_token',
            40029: '不合法的 code',
            45011: 'API 调用太频繁，请稍候再试',
            40226: '高风险等级用户，小程序登录拦截',
        }
        return error_messages.get(errcode, f'未知错误 (errcode: {errcode})')


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def get_user_agent(request):
    """获取用户代理信息"""
    return request.META.get('HTTP_USER_AGENT', '')


def generate_username_from_openid(openid):
    """根据openid生成用户名"""
    # 使用wx_前缀 + openid后8位作为用户名
    return f"wx_{openid[-8:]}"


def create_default_user_info(openid, user_info=None):
    """创建默认用户信息"""
    default_info = {
        'nickname': user_info.get('nickName', '微信用户') if user_info else '微信用户',
        'avatar_url': user_info.get('avatarUrl', '') if user_info else '',
        'gender': user_info.get('gender', 0) if user_info else 0,
        'country': user_info.get('country', '') if user_info else '',
        'province': user_info.get('province', '') if user_info else '',
        'city': user_info.get('city', '') if user_info else '',
    }
    return default_info
