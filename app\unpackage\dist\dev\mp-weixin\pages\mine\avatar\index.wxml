<view class="container data-v-05deedaf"><view class="page-body uni-content-info data-v-05deedaf"><view class="cropper-content data-v-05deedaf"><block wx:if="{{isShowImg}}"><view class="uni-corpper data-v-05deedaf" style="{{('width:'+cropperInitW+'px;height:'+cropperInitH+'px;background:#000')}}"><view class="uni-corpper-content data-v-05deedaf" style="{{('width:'+cropperW+'px;height:'+cropperH+'px;left:'+cropperL+'px;top:'+cropperT+'px')}}"><image style="{{('width:'+cropperW+'px;height:'+cropperH+'px')}}" src="{{imageSrc}}" class="data-v-05deedaf"></image><view data-event-opts="{{[['touchstart',[['contentStartMove',['$event']]]],['touchmove',[['contentMoveing',['$event']]]],['touchend',[['contentTouchEnd',['$event']]]]]}}" class="uni-corpper-crop-box data-v-05deedaf" style="{{('left:'+cutL+'px;top:'+cutT+'px;right:'+cutR+'px;bottom:'+cutB+'px')}}" catchtouchstart="__e" catchtouchmove="__e" catchtouchend="__e"><view class="uni-cropper-view-box data-v-05deedaf"><view class="uni-cropper-dashed-h data-v-05deedaf"></view><view class="uni-cropper-dashed-v data-v-05deedaf"></view><view class="uni-cropper-line-t data-v-05deedaf" data-drag="top" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-line-r data-v-05deedaf" data-drag="right" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-line-b data-v-05deedaf" data-drag="bottom" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-line-l data-v-05deedaf" data-drag="left" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-point point-t data-v-05deedaf" data-drag="top" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-point point-tr data-v-05deedaf" data-drag="topTight"></view><view class="uni-cropper-point point-r data-v-05deedaf" data-drag="right" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-point point-rb data-v-05deedaf" data-drag="rightBottom" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-point point-b data-v-05deedaf" data-drag="bottom" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]],['touchend',[['dragEnd',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e" catchtouchend="__e"></view><view class="uni-cropper-point point-bl data-v-05deedaf" data-drag="bottomLeft"></view><view class="uni-cropper-point point-l data-v-05deedaf" data-drag="left" data-event-opts="{{[['touchstart',[['dragStart',['$event']]]],['touchmove',[['dragMove',['$event']]]]]}}" catchtouchstart="__e" catchtouchmove="__e"></view><view class="uni-cropper-point point-lt data-v-05deedaf" data-drag="leftTop"></view></view></view></view></view></block></view><view class="cropper-config data-v-05deedaf"><button style="margin-top:30rpx;" type="primary reverse" data-event-opts="{{[['tap',[['getImage',['$event']]]]]}}" bindtap="__e" class="data-v-05deedaf">选择头像</button><button style="margin-top:30rpx;" type="warn" data-event-opts="{{[['tap',[['getImageInfo',['$event']]]]]}}" bindtap="__e" class="data-v-05deedaf">提交</button></view><canvas style="{{('position:absolute;border: 1px solid red; width:'+imageW+'px;height:'+imageH+'px;top:-9999px;left:-9999px;')}}" canvas-id="myCanvas" class="data-v-05deedaf"></canvas></view></view>