import request from '@/utils/request'

// 商品相关API

// 获取轮播图
export function getBanners() {
  return request({
    url: '/api/shop/banner/list',
    method: 'get'
  })
}

// 获取商品分类
export function getCategories(params) {
  return request({
    url: '/api/shop/category/list',
    method: 'get',
    params
  })
}

// 获取商品分类树
export function getCategoryTree() {
  return request({
    url: '/api/shop/category/tree',
    method: 'get'
  })
}

// 获取商品列表
export function getGoodsList(params) {
  return request({
    url: '/api/shop/goods/list',
    method: 'get',
    params
  })
}

// 获取商品详情
export function getGoodsDetail(goodsId) {
  return request({
    url: `/api/shop/goods/detail/${goodsId}`,
    method: 'get'
  })
}

// 获取商品SKU列表
export function getGoodsSkus(goodsId) {
  return request({
    url: `/api/shop/goods/detail/${goodsId}/skus`,
    method: 'get'
  })
}

// 搜索商品
export function searchGoods(params) {
  return request({
    url: '/api/shop/goods/search',
    method: 'get',
    params
  })
}

// 获取热门搜索关键词
export function getHotKeywords() {
  return request({
    url: '/api/shop/goods/search/hot',
    method: 'get'
  })
}

// 获取推荐商品
export function getRecommendGoods(params) {
  return request({
    url: '/api/shop/goods/recommend',
    method: 'get',
    params
  })
}

// 获取商品评价
export function getGoodsReviews(goodsId, params) {
  return request({
    url: `/api/shop/goods/detail/${goodsId}/reviews`,
    method: 'get',
    params
  })
}

// 收藏商品
export function favoriteGoods(goodsId) {
  return request({
    url: `/api/shop/goods/${goodsId}/favorite`,
    method: 'post'
  })
}

// 取消收藏
export function unfavoriteGoods(goodsId) {
  return request({
    url: `/api/shop/goods/${goodsId}/favorite`,
    method: 'delete'
  })
}

// 添加到购物车
export function addToCart(data) {
  return request({
    url: '/api/shop/cart/add',
    method: 'post',
    data
  })
}

// 获取购物车列表
export function getCartList() {
  return request({
    url: '/api/shop/cart/list',
    method: 'get'
  })
}

// 更新购物车商品数量
export function updateCartQuantity(cartId, quantity) {
  return request({
    url: `/api/shop/cart/${cartId}`,
    method: 'put',
    data: { quantity }
  })
}

// 删除购物车商品
export function removeFromCart(cartIds) {
  return request({
    url: '/api/shop/cart/remove',
    method: 'delete',
    data: { cartIds }
  })
}

// 清空购物车
export function clearCart() {
  return request({
    url: '/api/shop/cart/clear',
    method: 'delete'
  })
}

// 获取购物车商品数量
export function getCartCount() {
  return request({
    url: '/api/shop/cart/count',
    method: 'get'
  })
}

// 获取商品库存
export function getGoodsStock(skuId) {
  return request({
    url: `/api/shop/sku/${skuId}/stock`,
    method: 'get'
  })
}

// ==================== 地址管理相关 ====================

// 获取地址列表
export function getAddressList() {
  return request({
    url: '/api/shop/address/list',
    method: 'get'
  })
}

// 获取地址详情
export function getAddressDetail(addressId) {
  return request({
    url: `/api/shop/address/${addressId}`,
    method: 'get'
  })
}

// 添加地址
export function addAddress(data) {
  return request({
    url: '/api/shop/address/add',
    method: 'post',
    data
  })
}

// 更新地址
export function updateAddress(addressId, data) {
  return request({
    url: `/api/shop/address/${addressId}`,
    method: 'put',
    data
  })
}

// 删除地址
export function deleteAddress(addressId) {
  return request({
    url: `/api/shop/address/${addressId}`,
    method: 'delete'
  })
}

// 设置默认地址
export function setDefaultAddress(addressId) {
  return request({
    url: `/api/shop/address/${addressId}/default`,
    method: 'put'
  })
}



// ==================== 订单相关 ====================

// 创建订单
export function createOrder(data) {
  return request({
    url: '/api/shop/order/create',
    method: 'post',
    data
  })
}

// 提交订单（别名，与createOrder功能相同）
export function submitOrder(data) {
  return createOrder(data)
}

// 获取订单列表
export function getOrderList(params) {
  return request({
    url: '/api/shop/order/list',
    method: 'get',
    params
  })
}

// 获取订单详情
export function getOrderInfo(orderId) {
  return request({
    url: `/api/shop/order/${orderId}`,
    method: 'get'
  })
}

// 取消订单
export function cancelOrder(orderId) {
  return request({
    url: `/api/shop/order/cancel/${orderId}`,
    method: 'put'
  })
}

// 确认收货
export function confirmOrder(orderId) {
  return request({
    url: `/api/shop/order/confirm/${orderId}`,
    method: 'put'
  })
}

// 删除订单
export function deleteOrder(orderId) {
  return request({
    url: `/api/shop/order/delete/${orderId}`,
    method: 'put'
  })
}

// 获取订单统计
export function getOrderStatistics() {
  return request({
    url: '/api/shop/order/statistics',
    method: 'get'
  })
}

// 申请退款
export function applyRefund(orderId, data) {
  return request({
    url: `/api/shop/order/refund/${orderId}`,
    method: 'post',
    data
  })
}

// ==================== 支付相关 ====================

// 请求微信支付
export function requestWechatPay(data) {
  return request({
    url: '/api/shop/order/pay/wechat',
    method: 'post',
    data
  })
}

// 更新支付状态
export function updatePaymentStatus(data) {
  return request({
    url: '/api/shop/order/pay/status',
    method: 'post',
    data
  })
}

// 查询支付状态
export function queryPaymentStatus(orderId) {
  return request({
    url: `/api/shop/order/pay/status/${orderId}`,
    method: 'get'
  })
}
