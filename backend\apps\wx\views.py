import logging
from django.utils import timezone
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken

from dvadmin.utils.json_response import DetailResponse, ErrorResponse
from dvadmin.system.models import Users
from .models import WxUser, WxLoginLog
from .serializers import WxLoginSerializer, WxUserSerializer
from .utils import (
    WxApiClient, 
    get_client_ip, 
    get_user_agent, 
    generate_username_from_openid,
    create_default_user_info
)

logger = logging.getLogger(__name__)
User = get_user_model()


@api_view(['POST'])
@permission_classes([AllowAny])
def wx_login(request):
    """
    微信小程序登录接口
    
    接收前端传来的code和用户信息，完成微信登录流程
    """
    serializer = WxLoginSerializer(data=request.data)
    if not serializer.is_valid():
        return ErrorResponse(msg="参数验证失败", data=serializer.errors)
    
    code = serializer.validated_data['code']
    user_info = serializer.validated_data.get('userInfo', {})
    
    # 获取客户端信息
    ip_address = get_client_ip(request)
    user_agent = get_user_agent(request)
    
    wx_user = None
    login_success = False
    error_message = ""
    
    try:
        # 1. 调用微信API获取openid和session_key
        wx_client = WxApiClient()
        wx_data = wx_client.code2session(code)
        
        openid = wx_data['openid']
        session_key = wx_data.get('session_key', '')
        unionid = wx_data.get('unionid', '')
        
        logger.info(f"微信登录，openid: {openid}")
        
        # 2. 查找或创建微信用户
        try:
            wx_user = WxUser.objects.get(openid=openid)
            created = False
        except WxUser.DoesNotExist:
            # 生成用户名
            username = generate_username_from_openid(openid)

            # 创建系统用户（只设置基本字段）
            system_user = Users.objects.create(
                username=username,
                name=user_info.get('nickName', '微信用户') if user_info else '微信用户',
                user_type=1,  # 前台用户
                is_active=True,
                avatar=user_info.get('avatarUrl', '') if user_info else '',
                gender=user_info.get('gender', 0) if user_info else 0,
                # 微信相关字段
                openid=openid,
                unionid=unionid,
                session_key=session_key,
                nickname=user_info.get('nickName', '微信用户') if user_info else '微信用户',
                avatar_url=user_info.get('avatarUrl', '') if user_info else ''
            )

            # 创建微信用户关联记录
            wx_user = WxUser.objects.create(
                user=system_user,
                openid=openid,
                session_key=session_key,
                unionid=unionid,
                nickname=user_info.get('nickName', '微信用户') if user_info else '微信用户',
                avatar_url=user_info.get('avatarUrl', '') if user_info else '',
                gender=user_info.get('gender', 0) if user_info else 0
            )
            created = True
            
            logger.info(f"创建新用户: {username}, openid: {openid}")
        else:
            # 更新session_key和用户信息
            wx_user.session_key = session_key
            if unionid:
                wx_user.unionid = unionid
            
            # 更新用户信息（如果前端传了的话）
            if user_info:
                default_info = create_default_user_info(openid, user_info)
                for key, value in default_info.items():
                    if value:  # 只更新非空值
                        setattr(wx_user, key, value)
            
            wx_user.last_login_time = timezone.now()
            wx_user.save()
            
            logger.info(f"用户登录: {wx_user.user.username}, openid: {openid}")
        
        # 4. 生成JWT token
        refresh = RefreshToken.for_user(wx_user.user)
        access_token = str(refresh.access_token)
        refresh_token = str(refresh)
        
        # 5. 准备返回数据
        user_serializer = WxUserSerializer(wx_user)
        response_data = {
            'token': access_token,
            'refresh_token': refresh_token,
            'user': user_serializer.data,
            'is_new_user': created
        }
        
        login_success = True
        
        # 记录登录日志
        WxLoginLog.objects.create(
            wx_user=wx_user,
            ip_address=ip_address,
            user_agent=user_agent,
            login_result=True
        )
        
        return DetailResponse(data=response_data, msg="登录成功")
        
    except Exception as e:
        error_message = str(e)
        logger.error(f"微信登录失败: {error_message}")
        
        # 记录失败日志
        if wx_user:
            WxLoginLog.objects.create(
                wx_user=wx_user,
                ip_address=ip_address,
                user_agent=user_agent,
                login_result=False,
                error_message=error_message
            )
        
        return ErrorResponse(msg=f"登录失败: {error_message}")


@api_view(['GET'])
def get_user_info(request):
    """获取当前用户信息"""
    try:
        # 获取当前用户的微信信息
        wx_user = WxUser.objects.get(user=request.user)
        serializer = WxUserSerializer(wx_user)
        return DetailResponse(data=serializer.data, msg="获取成功")
    except WxUser.DoesNotExist:
        return ErrorResponse(msg="用户信息不存在")


@api_view(['POST'])
def update_user_info(request):
    """更新用户信息"""
    try:
        wx_user = WxUser.objects.get(user=request.user)
        
        # 更新微信用户信息
        allowed_fields = ['nickname', 'avatar_url', 'gender']
        for field in allowed_fields:
            if field in request.data:
                setattr(wx_user, field, request.data[field])
        
        wx_user.save()
        
        # 同步更新系统用户信息
        if 'nickname' in request.data:
            wx_user.user.name = request.data['nickname']
        if 'avatar_url' in request.data:
            wx_user.user.avatar = request.data['avatar_url']
        if 'gender' in request.data:
            wx_user.user.gender = request.data['gender']
        
        wx_user.user.save()
        
        serializer = WxUserSerializer(wx_user)
        return DetailResponse(data=serializer.data, msg="更新成功")
        
    except WxUser.DoesNotExist:
        return ErrorResponse(msg="用户信息不存在")
    except Exception as e:
        logger.error(f"更新用户信息失败: {str(e)}")
        return ErrorResponse(msg=f"更新失败: {str(e)}")


@api_view(['POST'])
def logout(request):
    """退出登录"""
    return DetailResponse(msg="退出成功")
