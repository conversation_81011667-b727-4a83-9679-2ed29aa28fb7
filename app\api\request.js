/**
 * HTTP请求工具
 */
import { API_CONFIG, HTTP_STATUS, ERROR_MESSAGES } from './config.js'

class HttpRequest {
	constructor() {
		this.baseUrl = API_CONFIG.BASE_URL
		this.timeout = API_CONFIG.TIMEOUT
		this.interceptors = {
			request: [],
			response: []
		}
		
		// 添加默认拦截器
		this.addDefaultInterceptors()
	}
	
	// 添加默认拦截器
	addDefaultInterceptors() {
		// 请求拦截器
		this.interceptors.request.push((config) => {
			// 添加认证token
			const token = uni.getStorageSync('token')
			if (token) {
				config.header = config.header || {}
				config.header.Authorization = `JWT ${token}`
			}
			
			// 添加默认请求头
			config.header = {
				'Content-Type': 'application/json',
				...config.header
			}
			
			console.log('请求发送:', config)
			return config
		})
		
		// 响应拦截器
		this.interceptors.response.push(
			(response) => {
				console.log('响应接收:', response)
				
				// 处理业务逻辑
				if (response.statusCode === HTTP_STATUS.SUCCESS) {
					const data = response.data
					
					// 如果后端返回统一格式
					if (data && typeof data === 'object' && 'code' in data) {
						if (data.code === 2000) {
							return data.data || data
						} else {
							throw new Error(data.message || '请求失败')
						}
					}
					
					return data
				} else {
					throw new Error(ERROR_MESSAGES[response.statusCode] || '请求失败')
				}
			},
			(error) => {
				console.error('请求错误:', error)
				return this.handleError(error)
			}
		)
	}
	
	// 处理错误
	handleError(error) {
		let message = '请求失败'
		
		if (error.errMsg) {
			if (error.errMsg.includes('timeout')) {
				message = ERROR_MESSAGES.timeout
			} else if (error.errMsg.includes('fail')) {
				message = ERROR_MESSAGES.network
			}
		} else if (error.message) {
			message = error.message
		}
		
		// 显示错误提示
		uni.showToast({
			title: message,
			icon: 'none',
			duration: 2000
		})
		
		// 如果是401错误，跳转到登录页
		if (error.statusCode === HTTP_STATUS.UNAUTHORIZED) {
			uni.removeStorageSync('token')
			uni.removeStorageSync('userInfo')
			uni.reLaunch({
				url: '/pages/login/login'
			})
		}
		
		throw error
	}
	
	// 执行请求拦截器
	executeRequestInterceptors(config) {
		return this.interceptors.request.reduce((config, interceptor) => {
			return interceptor(config)
		}, config)
	}
	
	// 执行响应拦截器
	executeResponseInterceptors(response) {
		return this.interceptors.response[0](response)
	}
	
	// 基础请求方法
	request(options) {
		return new Promise((resolve, reject) => {
			// 处理URL
			let url = options.url
			if (!url.startsWith('http')) {
				url = this.baseUrl + url
			}
			
			// 构建请求配置
			let config = {
				url,
				method: options.method || 'GET',
				data: options.data,
				header: options.header || {},
				timeout: options.timeout || this.timeout
			}
			
			try {
				// 执行请求拦截器
				config = this.executeRequestInterceptors(config)
				
				// 发送请求
				uni.request({
					...config,
					success: (response) => {
						try {
							const result = this.executeResponseInterceptors(response)
							resolve(result)
						} catch (error) {
							reject(error)
						}
					},
					fail: (error) => {
						try {
							this.handleError(error)
						} catch (handledError) {
							reject(handledError)
						}
					}
				})
			} catch (error) {
				reject(error)
			}
		})
	}
	
	// GET请求
	get(url, params = {}, options = {}) {
		// 处理查询参数
		if (Object.keys(params).length > 0) {
			const queryString = Object.keys(params)
				.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
				.join('&')
			url += (url.includes('?') ? '&' : '?') + queryString
		}
		
		return this.request({
			url,
			method: 'GET',
			...options
		})
	}
	
	// POST请求
	post(url, data = {}, options = {}) {
		return this.request({
			url,
			method: 'POST',
			data,
			...options
		})
	}
	
	// PUT请求
	put(url, data = {}, options = {}) {
		return this.request({
			url,
			method: 'PUT',
			data,
			...options
		})
	}
	
	// DELETE请求
	delete(url, options = {}) {
		return this.request({
			url,
			method: 'DELETE',
			...options
		})
	}
	
	// 文件上传
	upload(url, filePath, formData = {}, options = {}) {
		return new Promise((resolve, reject) => {
			// 添加认证token
			const token = uni.getStorageSync('token')
			const header = {
				...options.header
			}
			if (token) {
				header.Authorization = `JWT ${token}`
			}
			
			// 处理URL
			if (!url.startsWith('http')) {
				url = this.baseUrl + url
			}
			
			uni.uploadFile({
				url,
				filePath,
				name: options.name || 'file',
				formData,
				header,
				success: (response) => {
					try {
						const data = JSON.parse(response.data)
						if (response.statusCode === HTTP_STATUS.SUCCESS) {
							resolve(data)
						} else {
							throw new Error(data.message || '上传失败')
						}
					} catch (error) {
						reject(error)
					}
				},
				fail: (error) => {
					this.handleError(error)
					reject(error)
				}
			})
		})
	}
}

// 创建实例
const http = new HttpRequest()

export default http
