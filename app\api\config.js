/**
 * API配置文件
 */

// 开发环境和生产环境的API地址
const API_BASE_URL = {
	// 开发环境
	development: 'http://localhost:8000',
	// 生产环境
	production: 'https://your-domain.com'
}

// 获取当前环境的API地址
const getBaseUrl = () => {
	// 在小程序中，可以通过版本类型判断环境
	const accountInfo = uni.getAccountInfoSync()
	const envVersion = accountInfo.miniProgram.envVersion
	
	switch (envVersion) {
		case 'develop':
		case 'trial':
			return API_BASE_URL.development
		case 'release':
			return API_BASE_URL.production
		default:
			return API_BASE_URL.development
	}
}

// API配置
export const API_CONFIG = {
	BASE_URL: getBaseUrl(),
	TIMEOUT: 10000, // 请求超时时间
	
	// API路径
	ENDPOINTS: {
		// 用户相关
		USER: {
			WECHAT_LOGIN: '/api/user/wechat-login/',
			PHONE_LOGIN: '/api/user/phone-login/',
			SEND_SMS: '/api/user/send-sms/',
			GET_PROFILE: '/api/user/profile/',
			UPDATE_PROFILE: '/api/user/profile/',
			LOGOUT: '/api/user/logout/'
		},
		
		// 植物百科相关
		PLANT: {
			LIST: '/api/plant-encyclopedia/',
			DETAIL: '/api/plant-encyclopedia/{id}/',
			SEARCH: '/api/plant-encyclopedia/search_plants/',
			RECOMMEND: '/api/plant-encyclopedia/recommend/',
			CATEGORIES: '/api/plant-encyclopedia/categories/'
		},
		
		// 植物识别相关
		IDENTIFY: {
			UPLOAD: '/api/plant-identify/upload/',
			RESULT: '/api/plant-identify/result/{id}/',
			HISTORY: '/api/plant-identify/history/'
		},
		
		// 用户植物相关
		USER_PLANT: {
			LIST: '/api/user-plants/',
			CREATE: '/api/user-plants/',
			DETAIL: '/api/user-plants/{id}/',
			UPDATE: '/api/user-plants/{id}/',
			DELETE: '/api/user-plants/{id}/'
		},
		
		// 提醒相关
		REMINDER: {
			LIST: '/api/reminders/',
			CREATE: '/api/reminders/',
			UPDATE: '/api/reminders/{id}/',
			DELETE: '/api/reminders/{id}/'
		},
		
		// 社区相关
		COMMUNITY: {
			POSTS: '/api/community/posts/',
			POST_DETAIL: '/api/community/posts/{id}/',
			CREATE_POST: '/api/community/posts/',
			LIKE_POST: '/api/community/posts/{id}/like/',
			COMMENTS: '/api/community/posts/{id}/comments/',
			CREATE_COMMENT: '/api/community/posts/{id}/comments/'
		},
		
		// 文件上传
		UPLOAD: {
			IMAGE: '/api/upload/image/',
			FILE: '/api/upload/file/'
		}
	}
}

// 请求状态码
export const HTTP_STATUS = {
	SUCCESS: 200,
	CREATED: 201,
	NO_CONTENT: 204,
	BAD_REQUEST: 400,
	UNAUTHORIZED: 401,
	FORBIDDEN: 403,
	NOT_FOUND: 404,
	METHOD_NOT_ALLOWED: 405,
	INTERNAL_SERVER_ERROR: 500,
	BAD_GATEWAY: 502,
	SERVICE_UNAVAILABLE: 503
}

// 业务状态码
export const BUSINESS_CODE = {
	SUCCESS: 2000,
	ERROR: 4000,
	UNAUTHORIZED: 4001,
	FORBIDDEN: 4003,
	NOT_FOUND: 4004,
	VALIDATION_ERROR: 4005,
	SERVER_ERROR: 5000
}

// 错误消息映射
export const ERROR_MESSAGES = {
	[HTTP_STATUS.BAD_REQUEST]: '请求参数错误',
	[HTTP_STATUS.UNAUTHORIZED]: '未授权，请重新登录',
	[HTTP_STATUS.FORBIDDEN]: '拒绝访问',
	[HTTP_STATUS.NOT_FOUND]: '请求的资源不存在',
	[HTTP_STATUS.METHOD_NOT_ALLOWED]: '请求方法不允许',
	[HTTP_STATUS.INTERNAL_SERVER_ERROR]: '服务器内部错误',
	[HTTP_STATUS.BAD_GATEWAY]: '网关错误',
	[HTTP_STATUS.SERVICE_UNAVAILABLE]: '服务不可用',
	
	// 网络错误
	'timeout': '请求超时，请检查网络连接',
	'network': '网络连接失败，请检查网络设置'
}

export default API_CONFIG
