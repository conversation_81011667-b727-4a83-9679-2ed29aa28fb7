# 微信小程序API测试示例

## 测试环境准备

1. 启动后端服务：
```bash
cd backend
python manage.py runserver 8000
```

2. 配置微信小程序参数（在 `backend/conf/env.py` 中）：
```python
WX_MINIPROGRAM_APP_ID = 'your_actual_app_id'
WX_MINIPROGRAM_APP_SECRET = 'your_actual_app_secret'
```

## API接口测试

### 1. 微信登录接口测试

**接口地址**: `POST http://localhost:8000/api/auth/login/`

**测试用例1: 正常登录**
```bash
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "code": "valid_wx_code_from_miniprogram",
    "userInfo": {
      "nickName": "测试用户",
      "avatarUrl": "https://example.com/avatar.jpg",
      "gender": 1,
      "country": "中国",
      "province": "广东",
      "city": "深圳"
    }
  }'
```

**预期响应**:
```json
{
  "code": 2000,
  "msg": "登录成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "openid": "wx_openid_123456",
      "nickname": "测试用户",
      "avatar_url": "https://example.com/avatar.jpg",
      "gender": 1,
      "country": "中国",
      "province": "广东",
      "city": "深圳",
      "is_active": true,
      "username": "wx_123456",
      "name": "测试用户"
    },
    "is_new_user": true
  }
}
```

**测试用例2: 参数错误**
```bash
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "userInfo": {
      "nickName": "测试用户"
    }
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "msg": "参数验证失败",
  "data": {
    "code": ["This field is required."]
  }
}
```

### 2. 获取用户信息接口测试

**接口地址**: `GET http://localhost:8000/api/auth/user/`

```bash
curl -X GET http://localhost:8000/api/auth/user/ \
  -H "Authorization: JWT your_access_token_here"
```

**预期响应**:
```json
{
  "code": 2000,
  "msg": "获取成功",
  "data": {
    "id": 1,
    "openid": "wx_openid_123456",
    "nickname": "测试用户",
    "avatar_url": "https://example.com/avatar.jpg",
    "gender": 1,
    "country": "中国",
    "province": "广东",
    "city": "深圳",
    "is_active": true,
    "username": "wx_123456",
    "name": "测试用户",
    "create_datetime": "2025-07-04T15:30:00Z"
  }
}
```

### 3. 更新用户信息接口测试

**接口地址**: `POST http://localhost:8000/api/auth/update/`

```bash
curl -X POST http://localhost:8000/api/auth/update/ \
  -H "Authorization: JWT your_access_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "nickname": "更新后的昵称",
    "avatar_url": "https://example.com/new_avatar.jpg",
    "gender": 2
  }'
```

**预期响应**:
```json
{
  "code": 2000,
  "msg": "更新成功",
  "data": {
    "id": 1,
    "openid": "wx_openid_123456",
    "nickname": "更新后的昵称",
    "avatar_url": "https://example.com/new_avatar.jpg",
    "gender": 2,
    "country": "中国",
    "province": "广东",
    "city": "深圳",
    "is_active": true,
    "username": "wx_123456",
    "name": "更新后的昵称"
  }
}
```

### 4. 退出登录接口测试

**接口地址**: `POST http://localhost:8000/api/auth/logout/`

```bash
curl -X POST http://localhost:8000/api/auth/logout/ \
  -H "Authorization: JWT your_access_token_here"
```

**预期响应**:
```json
{
  "code": 2000,
  "msg": "退出成功",
  "data": null
}
```

## 前端集成测试

### 1. 在微信开发者工具中测试

1. 在 `app/config.js` 中配置正确的后端地址
2. 在登录页面点击微信登录按钮
3. 查看控制台输出和网络请求

### 2. 调试步骤

1. **检查code获取**:
```javascript
uni.login({
  provider: 'weixin',
  success: (res) => {
    console.log('微信登录code:', res.code)
    // 确保code不为空
  }
})
```

2. **检查API调用**:
```javascript
wxLogin(code, userInfo).then(res => {
  console.log('登录响应:', JSON.stringify(res))
  // 检查响应格式和数据
})
```

3. **检查token保存**:
```javascript
import { getToken } from '@/utils/auth'
console.log('当前token:', getToken())
```

## 常见问题排查

### 1. 微信API调用失败

**错误**: `微信登录失败: 不合法的 code`
**解决**: 
- 检查微信小程序AppID和AppSecret配置
- 确保code是从微信小程序获取的有效code
- code只能使用一次，不能重复使用

### 2. 数据库连接错误

**错误**: `django.db.utils.OperationalError`
**解决**:
- 检查数据库配置
- 确保数据库服务正在运行
- 运行数据库迁移: `python manage.py migrate`

### 3. JWT Token错误

**错误**: `Token is invalid or expired`
**解决**:
- 检查token格式是否正确
- 确保请求头格式: `Authorization: JWT your_token`
- 检查token是否过期

### 4. CORS跨域错误

**错误**: `Access to XMLHttpRequest has been blocked by CORS policy`
**解决**:
- 检查Django CORS配置
- 确保前端域名在CORS白名单中

## 性能测试

### 1. 并发登录测试

使用Apache Bench进行简单的并发测试：

```bash
# 创建测试数据文件 test_data.json
echo '{"code":"test_code","userInfo":{"nickName":"测试用户"}}' > test_data.json

# 进行并发测试（需要先mock微信API）
ab -n 100 -c 10 -p test_data.json -T application/json http://localhost:8000/api/auth/login/
```

### 2. 监控指标

- 响应时间
- 并发处理能力
- 数据库连接数
- 内存使用情况

## 安全测试

### 1. SQL注入测试

尝试在参数中注入SQL代码，确保系统能正确处理。

### 2. XSS测试

在用户信息字段中尝试注入脚本代码。

### 3. 认证绕过测试

尝试不提供token访问需要认证的接口。

---

**注意**: 
1. 测试时请使用测试环境，不要在生产环境进行测试
2. 确保微信小程序配置正确
3. 定期更新测试用例以覆盖新功能
