# Generated by Django 4.2.14 on 2025-07-04 22:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("system", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="users",
            name="avatar_url",
            field=models.URLField(
                blank=True, help_text="微信头像URL", null=True, verbose_name="微信头像"
            ),
        ),
        migrations.AddField(
            model_name="users",
            name="city",
            field=models.CharField(
                blank=True,
                help_text="所在城市",
                max_length=50,
                null=True,
                verbose_name="城市",
            ),
        ),
        migrations.AddField(
            model_name="users",
            name="country",
            field=models.CharField(
                blank=True,
                help_text="所在国家",
                max_length=50,
                null=True,
                verbose_name="国家",
            ),
        ),
        migrations.AddField(
            model_name="users",
            name="language",
            field=models.CharField(
                blank=True,
                help_text="语言设置",
                max_length=10,
                null=True,
                verbose_name="语言",
            ),
        ),
        migrations.AddField(
            model_name="users",
            name="last_login_ip",
            field=models.GenericIPAddressField(
                blank=True,
                help_text="最后登录IP地址",
                null=True,
                verbose_name="最后登录IP",
            ),
        ),
        migrations.AddField(
            model_name="users",
            name="level",
            field=models.IntegerField(
                default=1, help_text="用户等级", verbose_name="等级"
            ),
        ),
        migrations.AddField(
            model_name="users",
            name="nickname",
            field=models.CharField(
                blank=True,
                help_text="微信昵称",
                max_length=50,
                null=True,
                verbose_name="微信昵称",
            ),
        ),
        migrations.AddField(
            model_name="users",
            name="openid",
            field=models.CharField(
                blank=True,
                help_text="微信小程序openid",
                max_length=64,
                null=True,
                unique=True,
                verbose_name="微信openid",
            ),
        ),
        migrations.AddField(
            model_name="users",
            name="points",
            field=models.IntegerField(
                default=0, help_text="用户积分", verbose_name="积分"
            ),
        ),
        migrations.AddField(
            model_name="users",
            name="province",
            field=models.CharField(
                blank=True,
                help_text="所在省份",
                max_length=50,
                null=True,
                verbose_name="省份",
            ),
        ),
        migrations.AddField(
            model_name="users",
            name="register_source",
            field=models.CharField(
                default="web",
                help_text="注册来源：web/miniprogram/app",
                max_length=20,
                verbose_name="注册来源",
            ),
        ),
        migrations.AddField(
            model_name="users",
            name="session_key",
            field=models.CharField(
                blank=True,
                help_text="微信小程序session_key",
                max_length=64,
                null=True,
                verbose_name="会话密钥",
            ),
        ),
        migrations.AddField(
            model_name="users",
            name="unionid",
            field=models.CharField(
                blank=True,
                help_text="微信unionid",
                max_length=64,
                null=True,
                verbose_name="微信unionid",
            ),
        ),
        migrations.AlterModelTable(
            name="apiwhitelist",
            table="plant_api_white_list",
        ),
        migrations.AlterModelTable(
            name="area",
            table="plant_system_area",
        ),
        migrations.AlterModelTable(
            name="dept",
            table="plant_system_dept",
        ),
        migrations.AlterModelTable(
            name="dictionary",
            table="plant_system_dictionary",
        ),
        migrations.AlterModelTable(
            name="downloadcenter",
            table="plant_download_center",
        ),
        migrations.AlterModelTable(
            name="fieldpermission",
            table="plant_system_field_permission",
        ),
        migrations.AlterModelTable(
            name="filelist",
            table="plant_system_file_list",
        ),
        migrations.AlterModelTable(
            name="loginlog",
            table="plant_system_login_log",
        ),
        migrations.AlterModelTable(
            name="menu",
            table="plant_system_menu",
        ),
        migrations.AlterModelTable(
            name="menubutton",
            table="plant_system_menu_button",
        ),
        migrations.AlterModelTable(
            name="menufield",
            table="plant_system_menu_field",
        ),
        migrations.AlterModelTable(
            name="messagecenter",
            table="plant_message_center",
        ),
        migrations.AlterModelTable(
            name="messagecentertargetuser",
            table="plant_message_center_target_user",
        ),
        migrations.AlterModelTable(
            name="operationlog",
            table="plant_system_operation_log",
        ),
        migrations.AlterModelTable(
            name="post",
            table="plant_system_post",
        ),
        migrations.AlterModelTable(
            name="role",
            table="plant_system_role",
        ),
        migrations.AlterModelTable(
            name="rolemenubuttonpermission",
            table="plant_role_menu_button_permission",
        ),
        migrations.AlterModelTable(
            name="rolemenupermission",
            table="plant_role_menu_permission",
        ),
        migrations.AlterModelTable(
            name="systemconfig",
            table="plant_system_config",
        ),
        migrations.AlterModelTable(
            name="users",
            table="plant_system_users",
        ),
    ]
