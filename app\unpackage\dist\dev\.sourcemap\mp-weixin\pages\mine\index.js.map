{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/index.vue?a4cd", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/index.vue?cd37", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/index.vue?c57a", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/index.vue?89c8", "uni-app:///pages/mine/index.vue", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/index.vue?d444", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/index.vue?d58d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "name", "computed", "avatar", "windowHeight", "methods", "handleToInfo", "handleToProfile", "handleToSetting", "handleToLogin", "handleToAvatar", "handleHelp", "handleAbout", "handleMyPlants", "handlePlantCare", "handlePlantEncyclopedia", "handleToCollect"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2sB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+G/tB;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClKA;AAAA;AAAA;AAAA;AAAk2C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAt3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4bd6864f&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4bd6864f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4bd6864f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4bd6864f&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"mine-container\" :style=\"{height: `${windowHeight}px`}\">\r\n    <!--顶部个人信息栏-->\r\n    <view class=\"header-section\">\r\n      <view class=\"flex padding justify-between\">\r\n        <view class=\"flex align-center\">\r\n          <view v-if=\"!avatar\" class=\"cu-avatar xl round bg-white\">\r\n            <view class=\"iconfont icon-people text-gray icon\"></view>\r\n          </view>\r\n          <image v-if=\"avatar\" @click=\"handleToAvatar\" :src=\"avatar\" class=\"cu-avatar xl round\" mode=\"widthFix\">\r\n          </image>\r\n          <view v-if=\"!name\" @click=\"handleToLogin\" class=\"login-tip\">\r\n            点击登录\r\n          </view>\r\n          <view v-if=\"name\" @click=\"handleToInfo\" class=\"user-info\">\r\n            <view class=\"u_title\">\r\n              {{ name }}\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view @click=\"handleToInfo\" class=\"flex align-center\">\r\n          <text>个人信息</text>\r\n          <view class=\"iconfont icon-right\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"content-section\">\r\n      <!-- 订单管理 -->\r\n      <view class=\"order-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"title\">我的订单</text>\r\n          <view class=\"more\" @click=\"handleViewAllOrders\">\r\n            <text>查看全部</text>\r\n            <view class=\"iconfont icon-right\"></view>\r\n          </view>\r\n        </view>\r\n        <view class=\"order-actions grid col-5 text-center\">\r\n          <view class=\"action-item\" @click=\"handleOrderStatus(0)\">\r\n            <view class=\"iconfont icon-pay text-orange icon\"></view>\r\n            <text class=\"text\">待付款</text>\r\n          </view>\r\n          <view class=\"action-item\" @click=\"handleOrderStatus(1)\">\r\n            <view class=\"iconfont icon-deliver text-blue icon\"></view>\r\n            <text class=\"text\">待发货</text>\r\n          </view>\r\n          <view class=\"action-item\" @click=\"handleOrderStatus(2)\">\r\n            <view class=\"iconfont icon-send text-green icon\"></view>\r\n            <text class=\"text\">待收货</text>\r\n          </view>\r\n          <view class=\"action-item\" @click=\"handleOrderStatus(3)\">\r\n            <view class=\"iconfont icon-evaluate text-purple icon\"></view>\r\n            <text class=\"text\">待评价</text>\r\n          </view>\r\n          <view class=\"action-item\" @click=\"handleAfterSale\">\r\n            <view class=\"iconfont icon-service text-red icon\"></view>\r\n            <text class=\"text\">售后</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"menu-list\">\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"handleToProfile\">\r\n          <view class=\"menu-item-box\">\r\n            <view class=\"iconfont icon-people menu-icon\"></view>\r\n            <view>个人资料</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"handleToAddress\">\r\n          <view class=\"menu-item-box\">\r\n            <view class=\"iconfont icon-location menu-icon\"></view>\r\n            <view>收货地址</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"handleToCollect\">\r\n          <view class=\"menu-item-box\">\r\n            <view class=\"iconfont icon-favor menu-icon\"></view>\r\n            <view>我的收藏</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"handleToCoupon\">\r\n          <view class=\"menu-item-box\">\r\n            <view class=\"iconfont icon-ticket menu-icon\"></view>\r\n            <view>优惠券</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"handleHelp\">\r\n          <view class=\"menu-item-box\">\r\n            <view class=\"iconfont icon-help menu-icon\"></view>\r\n            <view>常见问题</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"handleAbout\">\r\n          <view class=\"menu-item-box\">\r\n            <view class=\"iconfont icon-info menu-icon\"></view>\r\n            <view>关于我们</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"handleToSetting\">\r\n          <view class=\"menu-item-box\">\r\n            <view class=\"iconfont icon-setting menu-icon\"></view>\r\n            <view>应用设置</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    data() {\r\n      return {\r\n        name: this.$store.state.user.name\r\n      }\r\n    },\r\n    computed: {\r\n      avatar() {\r\n        return this.$store.state.user.avatar\r\n      },\r\n      windowHeight() {\r\n        return uni.getSystemInfoSync().windowHeight - 50\r\n      }\r\n    },\r\n    methods: {\r\n      handleToInfo() {\r\n        this.$tab.navigateTo('/pages/mine/info/index')\r\n      },\r\n      handleToProfile() {\r\n        this.$tab.navigateTo('/pages/mine/profile/index')\r\n      },\r\n      handleToSetting() {\r\n        this.$tab.navigateTo('/pages/mine/setting/index')\r\n      },\r\n      handleToLogin() {\r\n        this.$tab.reLaunch('/pages/login')\r\n      },\r\n      handleToAvatar() {\r\n        this.$tab.navigateTo('/pages/mine/avatar/index')\r\n      },\r\n      handleHelp() {\r\n        this.$tab.navigateTo('/pages/mine/help/index')\r\n      },\r\n      handleAbout() {\r\n        this.$tab.navigateTo('/pages/mine/about/index')\r\n      },\r\n      // 植物相关功能\r\n      handleMyPlants() {\r\n        this.$modal.showToast('我的植物')\r\n      },\r\n      handlePlantCare() {\r\n        this.$modal.showToast('养护记录')\r\n      },\r\n      handlePlantEncyclopedia() {\r\n        this.$modal.showToast('植物百科')\r\n      },\r\n      // 收藏\r\n      handleToCollect() {\r\n        this.$modal.showToast('我的收藏')\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  page {\r\n    background-color: #f5f5f7;\r\n  }\r\n\r\n  .mine-container {\r\n    width: 100%;\r\n    height: 100%;\r\n\r\n    .header-section {\r\n      padding: 15px 15px 45px 15px;\r\n      background-color: #3c96f3;\r\n      color: white;\r\n\r\n      .login-tip {\r\n        font-size: 18px;\r\n        margin-left: 10px;\r\n      }\r\n\r\n      .cu-avatar {\r\n        border: 2px solid #eaeaea;\r\n\r\n        .icon {\r\n          font-size: 40px;\r\n        }\r\n      }\r\n\r\n      .user-info {\r\n        margin-left: 15px;\r\n\r\n        .u_title {\r\n          font-size: 18px;\r\n          line-height: 30px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .content-section {\r\n      position: relative;\r\n      top: -50px;\r\n      \r\n      .order-section {\r\n        margin: 15px 15px;\r\n        padding: 15px;\r\n        border-radius: 8px;\r\n        background-color: white;\r\n        \r\n        .section-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          padding-bottom: 15px;\r\n          \r\n          .title {\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n          }\r\n          \r\n          .more {\r\n            display: flex;\r\n            align-items: center;\r\n            font-size: 12px;\r\n            color: #999;\r\n          }\r\n        }\r\n        \r\n        .order-actions {\r\n          .action-item {\r\n            .icon {\r\n              font-size: 24px;\r\n            }\r\n\r\n            .text {\r\n              display: block;\r\n              font-size: 12px;\r\n              margin: 8px 0px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      \r\n      .menu-list {\r\n        margin: 15px;\r\n        border-radius: 8px;\r\n        background-color: white;\r\n        \r\n        .list-cell {\r\n          position: relative;\r\n          padding: 15px;\r\n          border-bottom: 1px solid #f5f5f7;\r\n          \r\n          &.list-cell-arrow::after {\r\n            content: \"\\e6a3\";\r\n            font-family: \"iconfont\";\r\n            position: absolute;\r\n            right: 15px;\r\n            color: #999;\r\n          }\r\n          \r\n          .menu-item-box {\r\n            display: flex;\r\n            align-items: center;\r\n            \r\n            .menu-icon {\r\n              margin-right: 10px;\r\n              font-size: 18px;\r\n              color: #3c96f3;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4bd6864f&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4bd6864f&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751643652880\n      var cssReload = require(\"D:/System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}