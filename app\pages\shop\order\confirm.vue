<template>
  <view class="order-confirm-container">
    <!-- 收货地址 -->
    <view class="address-section">
      <view class="section-header">
        <text class="section-title">收货信息</text>
        <text class="address-manage" @click="manageAddress">地址管理</text>
      </view>

      <view class="address-form">
        <view class="form-item">
          <text class="label">收货人</text>
          <input
            class="input"
            v-model="addressData.name"
            placeholder="请输入收货人姓名"
            maxlength="20"
          />
        </view>

        <view class="form-item">
          <text class="label">手机号</text>
          <input
            class="input"
            v-model="addressData.phone"
            placeholder="请输入手机号码"
            type="number"
            maxlength="11"
          />
        </view>

        <picker mode="region" :value="regionArray" @change="onRegionChange">
          <view class="form-item">
            <text class="label">所在地区</text>
            <view class="region-selector">
              <text class="region-text" v-if="regionText">{{ regionText }}</text>
              <text class="placeholder" v-else>请选择省市区</text>
              <uni-icons type="right" size="14" color="#ccc"></uni-icons>
            </view>
          </view>
        </picker>

        <view class="form-item">
          <text class="label">详细地址</text>
          <textarea
            class="textarea"
            v-model="addressData.detailAddress"
            placeholder="请输入详细地址，如街道、楼牌号等"
            maxlength="200"
            auto-height
          />
        </view>

        <!-- 保存地址选项 -->
        <view class="form-item" v-if="!addressData.id">
          <text class="label">保存地址</text>
          <view class="save-address-option">
            <text class="option-text">保存为常用地址</text>
            <switch
              :checked="saveAsCommonAddress"
              @change="onSaveAddressChange"
              color="#ff6700"
            />
          </view>
        </view>
      </view>
    </view>
    
    <!-- 商品列表 -->
    <view class="goods-section">
      <view class="section-header">
        <text class="section-title">商品清单</text>
        <text class="goods-count">共{{ goodsList.length }}件商品</text>
      </view>
      
      <view class="goods-item" v-for="(item, index) in goodsList" :key="index">
        <image class="goods-image" :src="getFullImageUrl(item.goodsImage)" mode="aspectFill"></image>
        <view class="goods-info">
          <text class="goods-name">{{ item.goodsName }}</text>
          <text class="goods-spec" v-if="item.skuName">{{ item.skuName }}</text>
          <view class="goods-price-qty">
            <text class="goods-price">¥{{ formatPrice(item.goodsPrice) }}</text>
            <text class="goods-qty">x{{ item.quantity }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 支付方式 -->
    <view class="payment-section">
      <view class="section-header">
        <text class="section-title">支付方式</text>
      </view>
      
      <view class="payment-item" @click="selectPayment('wechat')">
        <view class="payment-info">
          <image class="payment-icon" src="/static/images/wechat-pay.png" mode="aspectFit"></image>
          <text class="payment-name">微信支付</text>
        </view>
        <uni-icons 
          :type="paymentMethod === 'wechat' ? 'checkbox-filled' : 'circle'" 
          size="20" 
          :color="paymentMethod === 'wechat' ? '#09bb07' : '#ccc'"
        ></uni-icons>
      </view>
    </view>
    
    <!-- 订单备注 -->
    <view class="remark-section">
      <view class="section-header">
        <text class="section-title">订单备注</text>
      </view>
      <textarea 
        class="remark-input" 
        v-model="orderRemark" 
        placeholder="选填，请输入订单备注"
        maxlength="200"
      />
    </view>
    
    <!-- 费用明细 -->
    <view class="cost-section">
      <view class="cost-item">
        <text class="cost-label">商品总价</text>
        <text class="cost-value">¥{{ formatPrice(goodsAmount) }}</text>
      </view>
      <view class="cost-item">
        <text class="cost-label">运费</text>
        <text class="cost-value">¥{{ formatPrice(shippingFee) }}</text>
      </view>
      <view class="cost-item total">
        <text class="cost-label">实付款</text>
        <text class="cost-value total-amount">¥{{ formatPrice(totalAmount) }}</text>
      </view>
    </view>
    
    <!-- 提交订单 -->
    <view class="submit-section">
      <view class="total-info">
        <text class="total-text">实付：</text>
        <text class="total-price">¥{{ formatPrice(totalAmount) }}</text>
      </view>
      <button
        class="submit-btn"
        @click="submitOrder"
        :disabled="!canSubmit || loading"
        :class="{ 'loading': loading }"
      >
        {{ loading ? '提交中...' : '提交订单' }}
      </button>
    </view>


  </view>
</template>

<script>
import { createOrder, addAddress } from '@/api/shop.js'
import { formatPrice } from '@/utils/shop.js'
import config from '@/config.js'

export default {
  data() {
    return {
      goodsList: [],
      addressData: {
        name: '',
        phone: '',
        province: '',
        city: '',
        district: '',
        detailAddress: ''
      },
      regionArray: ['', '', ''],
      paymentMethod: 'wechat',
      orderRemark: '',
      shippingFee: 0,
      loading: false,
      saveAsCommonAddress: true // 默认保存为常用地址
    }
  },
  
  computed: {
    // 商品总价
    goodsAmount() {
      return this.goodsList.reduce((total, item) => {
        return total + (item.goodsPrice * item.quantity)
      }, 0)
    },

    // 总金额
    totalAmount() {
      return this.goodsAmount + this.shippingFee
    },

    // 地区文本
    regionText() {
      if (this.addressData.province && this.addressData.city && this.addressData.district) {
        return `${this.addressData.province} ${this.addressData.city} ${this.addressData.district}`
      }
      return ''
    },

    // 是否可以提交订单
    canSubmit() {
      return this.addressData.name && this.addressData.phone &&
             this.addressData.province && this.addressData.city &&
             this.addressData.district && this.addressData.detailAddress &&
             this.goodsList.length > 0 && this.paymentMethod
    }
  },
  
  onLoad(options) {
    // 从购物车传递的商品数据
    if (options.goodsData) {
      try {
        this.goodsList = JSON.parse(decodeURIComponent(options.goodsData))
      } catch (error) {
        console.error('解析商品数据失败:', error)
      }
    }
  },
  
  methods: {
    // 地址管理
    manageAddress() {
      console.log('=== 跳转到地址选择页面 ===')
      uni.navigateTo({
        url: '/pages/shop/address/index?select=1',
        success: () => {
          console.log('跳转地址页面成功')
        },
        fail: (err) => {
          console.error('跳转地址页面失败:', err)
        }
      })
    },

    // 设置选中的地址（从地址管理页面返回时调用）
    setSelectedAddress(address) {
      console.log('=== 订单页面接收到选中地址 ===')
      console.log('接收到的地址数据:', address)
      console.log('地址数据类型:', typeof address)
      console.log('地址是否为空:', address === null || address === undefined)

      // 验证地址数据
      if (!address) {
        console.error('接收到的地址数据为空')
        uni.showToast({
          title: '地址数据异常',
          icon: 'none'
        })
        return
      }

      // 验证必要字段
      if (!address.name || !address.phone) {
        console.error('地址数据缺少必要字段:', address)
        uni.showToast({
          title: '地址信息不完整',
          icon: 'none'
        })
        return
      }

      try {
        // 处理地址ID字段兼容性
        const addressId = address.addressId || address.id
        console.log('解析的地址ID:', addressId)

        this.addressData = {
          id: addressId, // 保存地址ID
          name: address.name || '',
          phone: address.phone || '',
          province: address.province || '',
          city: address.city || '',
          district: address.district || '',
          detailAddress: address.detailAddress || address.detail_address || ''
        }
        this.regionArray = [
          address.province || '',
          address.city || '',
          address.district || ''
        ]

        console.log('设置后的地址数据:', this.addressData)
        console.log('设置后的地区数组:', this.regionArray)

        // 显示成功提示
        uni.showToast({
          title: '地址选择成功',
          icon: 'success',
          duration: 1500
        })
      } catch (error) {
        console.error('设置地址数据时发生错误:', error)
        uni.showToast({
          title: '地址设置失败',
          icon: 'none'
        })
      }
    },

    // 地区选择变化
    onRegionChange(e) {
      const regions = e.detail.value
      this.regionArray = regions
      this.addressData.province = regions[0]
      this.addressData.city = regions[1]
      this.addressData.district = regions[2]
    },
    
    // 选择支付方式
    selectPayment(method) {
      this.paymentMethod = method
    },

    // 切换保存地址选项
    onSaveAddressChange(e) {
      this.saveAsCommonAddress = e.detail.value
      console.log('保存地址选项:', this.saveAsCommonAddress)
    },
    
    // 表单验证
    validateForm() {
      if (!this.addressData.name.trim()) {
        uni.showToast({
          title: '请输入收货人姓名',
          icon: 'none'
        })
        return false
      }

      if (!this.addressData.phone.trim()) {
        uni.showToast({
          title: '请输入手机号码',
          icon: 'none'
        })
        return false
      }

      // 手机号格式验证
      const phoneReg = /^1[3-9]\d{9}$/
      if (!phoneReg.test(this.addressData.phone)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none'
        })
        return false
      }

      if (!this.addressData.province || !this.addressData.city || !this.addressData.district) {
        uni.showToast({
          title: '请选择所在地区',
          icon: 'none'
        })
        return false
      }

      if (!this.addressData.detailAddress.trim()) {
        uni.showToast({
          title: '请输入详细地址',
          icon: 'none'
        })
        return false
      }

      return true
    },

    // 提交订单
    async submitOrder() {
      if (!this.validateForm()) {
        return
      }

      // 防止重复提交
      if (this.loading) {
        console.log('正在提交中，请勿重复点击')
        return
      }

      this.loading = true
      uni.showLoading({
        title: '提交中...',
        mask: true
      })

      try {
        console.log('开始提交订单')
        console.log('地址信息:', this.addressData)
        console.log('商品列表:', this.goodsList)

        // 如果是新地址且用户选择保存，先保存地址到数据库
        let finalAddressData = { ...this.addressData }
        if (!this.addressData.id && this.saveAsCommonAddress) {
          console.log('检测到新地址且用户选择保存，准备保存到数据库')
          try {
            const addressToSave = {
              name: this.addressData.name,
              phone: this.addressData.phone,
              province: this.addressData.province,
              city: this.addressData.city,
              district: this.addressData.district,
              detailAddress: this.addressData.detailAddress,
              isDefault: false // 新地址默认不设为默认地址
            }

            const addressResult = await this.saveAddressToDatabase(addressToSave)
            if (addressResult && addressResult.data && addressResult.data.id) {
              finalAddressData.id = addressResult.data.id
              console.log('地址保存成功，地址ID:', finalAddressData.id)

              // 显示保存成功提示
              uni.showToast({
                title: '地址已保存',
                icon: 'success',
                duration: 1500
              })
            } else {
              console.warn('地址保存失败，但继续创建订单')
            }
          } catch (addressError) {
            console.error('保存地址失败:', addressError)
            // 地址保存失败不影响订单创建，继续执行
            uni.showToast({
              title: '地址保存失败，但订单继续创建',
              icon: 'none',
              duration: 2000
            })
          }
        } else if (!this.addressData.id) {
          console.log('用户选择不保存地址，仅用于本次订单')
        }

        const orderData = {
          addressData: finalAddressData,
          paymentMethod: this.paymentMethod,
          remark: this.orderRemark,
          goodsList: this.goodsList.map(item => ({
            goodsId: item.goodsId,
            skuId: item.skuId,
            quantity: item.quantity,
            price: item.goodsPrice,
            cartId: item.cartId // 添加购物车ID，用于清理购物车
          }))
        }

        console.log('提交的订单数据:', orderData)
        const res = await createOrder(orderData)
        console.log('订单创建结果:', res)

        uni.hideLoading()

        if (res && res.code === 200 && res.data && res.data.orderId) {
          // 显示成功提示
          uni.showToast({
            title: '订单创建成功',
            icon: 'success',
            duration: 1500
          })

          // 延迟跳转到支付页面
          setTimeout(() => {
            uni.redirectTo({
              url: `/pages/shop/order/pay?orderId=${res.data.orderId}`
            })
          }, 1500)
        } else {
          // 处理业务错误
          const errorMsg = res.msg || '订单创建失败'
          console.error('订单创建业务错误:', errorMsg)
          uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 3000
          })
        }

      } catch (error) {
        uni.hideLoading()
        console.error('提交订单异常:', error)

        // 根据错误类型显示不同的提示
        let errorMsg = '提交失败，请重试'

        if (error.response) {
          // HTTP错误
          if (error.response.status === 500) {
            errorMsg = '服务器错误，请稍后重试'
          } else if (error.response.status === 400) {
            errorMsg = error.response.data?.msg || '请求参数错误'
          }
        } else if (error.message) {
          // 网络错误或其他错误
          if (error.message.includes('timeout')) {
            errorMsg = '网络超时，请检查网络连接'
          } else if (error.message.includes('Network Error')) {
            errorMsg = '网络连接失败，请检查网络'
          } else {
            errorMsg = error.message
          }
        }

        uni.showModal({
          title: '提交失败',
          content: errorMsg,
          showCancel: false,
          confirmText: '确定'
        })
      } finally {
        this.loading = false
      }
    },

    // 保存地址到数据库
    async saveAddressToDatabase(addressData) {
      try {
        console.log('调用地址保存API:', addressData)
        const result = await addAddress(addressData)
        console.log('地址保存API结果:', result)

        if (result && result.code === 200) {
          // 同时保存到本地存储
          try {
            const localAddresses = uni.getStorageSync('addressList') || []
            const newAddress = {
              ...addressData,
              id: result.data?.id || Date.now(),
              createTime: new Date().toISOString()
            }
            localAddresses.push(newAddress)
            uni.setStorageSync('addressList', localAddresses)
            console.log('地址已同步到本地存储')
          } catch (localError) {
            console.error('保存到本地存储失败:', localError)
          }

          return result
        } else {
          throw new Error(result.msg || '地址保存失败')
        }
      } catch (error) {
        console.error('保存地址到数据库失败:', error)

        // API失败时，至少保存到本地存储
        try {
          const localAddresses = uni.getStorageSync('addressList') || []
          const newAddress = {
            ...addressData,
            id: Date.now(),
            createTime: new Date().toISOString()
          }
          localAddresses.push(newAddress)
          uni.setStorageSync('addressList', localAddresses)
          console.log('地址已保存到本地存储（API失败备选方案）')

          return {
            code: 200,
            data: { id: newAddress.id }
          }
        } catch (localError) {
          console.error('本地存储也失败:', localError)
          throw error
        }
      }
    },

    // 格式化价格
    formatPrice,
    
    // 获取完整图片URL
    getFullImageUrl(imagePath) {
      if (!imagePath) return '/static/images/default-goods.png'
      if (imagePath.startsWith('http')) return imagePath
      return config.baseUrl + imagePath
    }
  }
}
</script>

<style lang="scss" scoped>
.order-confirm-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.address-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.address-form {
  .form-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  .label {
    font-size: 30rpx;
    color: #333;
    width: 160rpx;
    flex-shrink: 0;
  }

  .input {
    flex: 1;
    font-size: 30rpx;
    color: #333;

    &::placeholder {
      color: #ccc;
    }
  }

  .textarea {
    flex: 1;
    font-size: 30rpx;
    color: #333;
    min-height: 80rpx;

    &::placeholder {
      color: #ccc;
    }
  }

  .region-selector {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .region-text {
    font-size: 30rpx;
    color: #333;
  }

  .placeholder {
    font-size: 30rpx;
    color: #ccc;
  }

  .save-address-option {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .option-text {
    font-size: 30rpx;
    color: #333;
  }
}

.goods-section, .payment-section, .remark-section, .cost-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.address-manage {
  font-size: 26rpx;
  color: #007aff;
}

.goods-count {
  font-size: 26rpx;
  color: #999;
}

.goods-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.goods-spec {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 30rpx;
  color: #ff6700;
  font-weight: 600;
}

.goods-qty {
  font-size: 28rpx;
  color: #666;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.payment-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.payment-icon {
  width: 40rpx;
  height: 40rpx;
}

.payment-name {
  font-size: 30rpx;
  color: #333;
}

.remark-input {
  width: 100%;
  min-height: 80rpx;
  font-size: 30rpx;
  color: #333;
  
  &::placeholder {
    color: #ccc;
  }
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  
  &.total {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 20rpx;
    margin-top: 10rpx;
  }
}

.cost-label {
  font-size: 30rpx;
  color: #666;
}

.cost-value {
  font-size: 30rpx;
  color: #333;
  
  &.total-amount {
    font-size: 32rpx;
    color: #ff6700;
    font-weight: 600;
  }
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.total-info {
  display: flex;
  align-items: center;
}

.total-text {
  font-size: 28rpx;
  color: #666;
}

.total-price {
  font-size: 36rpx;
  color: #ff6700;
  font-weight: 600;
}

.submit-btn {
  background-color: #ff6700;
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: none;
  transition: all 0.3s ease;

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  &.loading {
    background-color: #ff9500;
    opacity: 0.8;
  }
}
</style>
