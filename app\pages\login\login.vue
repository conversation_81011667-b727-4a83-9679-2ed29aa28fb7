<template>
	<view class="login-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="leaf leaf1"></view>
			<view class="leaf leaf2"></view>
			<view class="leaf leaf3"></view>
		</view>
		
		<!-- 主要内容 -->
		<view class="login-content">
			<!-- Logo和标题 -->
			<view class="header">
				<image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
				<text class="title">植物家园</text>
				<text class="subtitle">发现植物之美，记录绿色生活</text>
			</view>
			
			<!-- 登录表单 -->
			<view class="login-form">
				<!-- 微信一键登录按钮 -->
				<button 
					class="login-btn wechat-btn"
					open-type="getUserInfo"
					@getuserinfo="handleWechatLogin"
					:loading="loginLoading"
				>
					<image class="btn-icon" src="/static/icons/wechat.png"></image>
					<text class="btn-text">微信一键登录</text>
				</button>
				
				<!-- 或者分割线 -->
				<view class="divider">
					<view class="line"></view>
					<text class="divider-text">或者</text>
					<view class="line"></view>
				</view>
				
				<!-- 手机号登录 -->
				<view class="phone-login">
					<view class="input-group">
						<input 
							class="input"
							type="number"
							placeholder="请输入手机号"
							v-model="phoneNumber"
							maxlength="11"
						/>
					</view>
					
					<view class="input-group">
						<input 
							class="input code-input"
							type="number"
							placeholder="请输入验证码"
							v-model="verifyCode"
							maxlength="6"
						/>
						<button 
							class="code-btn"
							:disabled="codeDisabled"
							@click="sendCode"
						>
							{{ codeText }}
						</button>
					</view>
					
					<button 
						class="login-btn phone-btn"
						@click="handlePhoneLogin"
						:loading="phoneLoginLoading"
						:disabled="!canPhoneLogin"
					>
						手机号登录
					</button>
				</view>
			</view>
			
			<!-- 协议条款 -->
			<view class="agreement">
				<checkbox-group @change="onAgreementChange">
					<label class="agreement-item">
						<checkbox value="agree" :checked="agreed" />
						<text class="agreement-text">
							我已阅读并同意
							<text class="link" @click="showPrivacy">《隐私政策》</text>
							和
							<text class="link" @click="showTerms">《用户协议》</text>
						</text>
					</label>
				</checkbox-group>
			</view>
		</view>
	</view>
</template>

<script>
import { useUserStore } from '@/store/user'

export default {
	name: 'Login',
	data() {
		return {
			// 登录状态
			loginLoading: false,
			phoneLoginLoading: false,
			
			// 表单数据
			phoneNumber: '',
			verifyCode: '',
			agreed: false,
			
			// 验证码相关
			codeDisabled: false,
			codeText: '获取验证码',
			countdown: 60
		}
	},
	
	computed: {
		// 是否可以进行手机号登录
		canPhoneLogin() {
			return this.phoneNumber.length === 11 && 
				   this.verifyCode.length === 6 && 
				   this.agreed
		}
	},
	
	onLoad() {
		// 检查是否已登录
		this.checkLoginStatus()
	},
	
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			const token = uni.getStorageSync('token')
			if (token) {
				// 已登录，跳转到首页
				uni.reLaunch({
					url: '/pages/index/index'
				})
			}
		},
		
		// 微信登录
		async handleWechatLogin(e) {
			if (!this.agreed) {
				uni.showToast({
					title: '请先同意用户协议',
					icon: 'none'
				})
				return
			}
			
			this.loginLoading = true
			
			try {
				// 获取用户信息
				const userInfo = e.detail.userInfo
				if (!userInfo) {
					throw new Error('用户取消授权')
				}
				
				// 获取登录凭证
				const loginRes = await this.wxLogin()
				
				// 调用后端登录接口
				const loginData = {
					code: loginRes.code,
					userInfo: userInfo,
					encryptedData: e.detail.encryptedData,
					iv: e.detail.iv
				}
				
				await this.doWechatLogin(loginData)
				
			} catch (error) {
				console.error('微信登录失败:', error)
				uni.showToast({
					title: error.message || '登录失败',
					icon: 'none'
				})
			} finally {
				this.loginLoading = false
			}
		},
		
		// 获取微信登录凭证
		wxLogin() {
			return new Promise((resolve, reject) => {
				uni.login({
					provider: 'weixin',
					success: resolve,
					fail: reject
				})
			})
		},
		
		// 执行微信登录
		async doWechatLogin(loginData) {
			// 这里调用后端API
			const userStore = useUserStore()
			await userStore.wechatLogin(loginData)
			
			// 登录成功，跳转首页
			uni.reLaunch({
				url: '/pages/index/index'
			})
		},
		
		// 手机号登录
		async handlePhoneLogin() {
			if (!this.canPhoneLogin) {
				return
			}
			
			this.phoneLoginLoading = true
			
			try {
				const loginData = {
					phone: this.phoneNumber,
					code: this.verifyCode
				}
				
				const userStore = useUserStore()
				await userStore.phoneLogin(loginData)
				
				// 登录成功，跳转首页
				uni.reLaunch({
					url: '/pages/index/index'
				})
				
			} catch (error) {
				console.error('手机号登录失败:', error)
				uni.showToast({
					title: error.message || '登录失败',
					icon: 'none'
				})
			} finally {
				this.phoneLoginLoading = false
			}
		},
		
		// 发送验证码
		async sendCode() {
			if (this.phoneNumber.length !== 11) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				})
				return
			}
			
			try {
				// 调用发送验证码API
				await this.$api.sendSmsCode({ phone: this.phoneNumber })
				
				// 开始倒计时
				this.startCountdown()
				
				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				})
				
			} catch (error) {
				console.error('发送验证码失败:', error)
				uni.showToast({
					title: error.message || '发送失败',
					icon: 'none'
				})
			}
		},
		
		// 开始倒计时
		startCountdown() {
			this.codeDisabled = true
			this.countdown = 60
			
			const timer = setInterval(() => {
				this.countdown--
				this.codeText = `${this.countdown}s后重发`
				
				if (this.countdown <= 0) {
					clearInterval(timer)
					this.codeDisabled = false
					this.codeText = '获取验证码'
				}
			}, 1000)
		},
		
		// 协议变更
		onAgreementChange(e) {
			this.agreed = e.detail.value.includes('agree')
		},
		
		// 显示隐私政策
		showPrivacy() {
			uni.showModal({
				title: '隐私政策',
				content: '这里是隐私政策内容...',
				showCancel: false
			})
		},
		
		// 显示用户协议
		showTerms() {
			uni.showModal({
				title: '用户协议',
				content: '这里是用户协议内容...',
				showCancel: false
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
	position: relative;
	overflow: hidden;
}

.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
	
	.leaf {
		position: absolute;
		width: 60rpx;
		height: 60rpx;
		background: rgba(76, 175, 80, 0.1);
		border-radius: 0 100% 0 100%;
		animation: float 6s ease-in-out infinite;
		
		&.leaf1 {
			top: 20%;
			left: 10%;
			animation-delay: 0s;
		}
		
		&.leaf2 {
			top: 60%;
			right: 15%;
			animation-delay: 2s;
		}
		
		&.leaf3 {
			bottom: 30%;
			left: 20%;
			animation-delay: 4s;
		}
	}
}

@keyframes float {
	0%, 100% { transform: translateY(0px) rotate(0deg); }
	50% { transform: translateY(-20px) rotate(10deg); }
}

.login-content {
	padding: 100rpx 60rpx 60rpx;
	position: relative;
	z-index: 1;
}

.header {
	text-align: center;
	margin-bottom: 100rpx;
	
	.logo {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
	}
	
	.title {
		display: block;
		font-size: 48rpx;
		font-weight: bold;
		color: $plant-color-primary;
		margin-bottom: 20rpx;
	}
	
	.subtitle {
		display: block;
		font-size: 28rpx;
		color: #666;
	}
}

.login-form {
	.wechat-btn {
		background: #07C160;
		color: white;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 40rpx;
		
		.btn-icon {
			width: 40rpx;
			height: 40rpx;
			margin-right: 20rpx;
		}
		
		.btn-text {
			font-size: 32rpx;
		}
	}
	
	.divider {
		display: flex;
		align-items: center;
		margin: 60rpx 0;
		
		.line {
			flex: 1;
			height: 1rpx;
			background: #E0E0E0;
		}
		
		.divider-text {
			margin: 0 30rpx;
			color: #999;
			font-size: 28rpx;
		}
	}
	
	.phone-login {
		.input-group {
			position: relative;
			margin-bottom: 30rpx;
			
			.input {
				width: 100%;
				height: 88rpx;
				padding: 0 30rpx;
				border: 1rpx solid #E0E0E0;
				border-radius: $plant-input-radius;
				font-size: 30rpx;
				background: white;
			}
			
			.code-input {
				padding-right: 200rpx;
			}
			
			.code-btn {
				position: absolute;
				right: 20rpx;
				top: 50%;
				transform: translateY(-50%);
				padding: 10rpx 20rpx;
				background: $plant-color-secondary;
				color: white;
				border: none;
				border-radius: 8rpx;
				font-size: 24rpx;
				
				&:disabled {
					background: #ccc;
				}
			}
		}
		
		.phone-btn {
			background: $plant-color-primary;
			color: white;
			margin-top: 20rpx;
			
			&:disabled {
				background: #ccc;
			}
		}
	}
}

.login-btn {
	width: 100%;
	height: 88rpx;
	border-radius: $plant-button-radius;
	font-size: 32rpx;
	border: none;
	
	&::after {
		border: none;
	}
}

.agreement {
	margin-top: 60rpx;
	
	.agreement-item {
		display: flex;
		align-items: flex-start;
		
		.agreement-text {
			margin-left: 20rpx;
			font-size: 24rpx;
			color: #666;
			line-height: 1.5;
			
			.link {
				color: $plant-color-primary;
			}
		}
	}
}
</style>
