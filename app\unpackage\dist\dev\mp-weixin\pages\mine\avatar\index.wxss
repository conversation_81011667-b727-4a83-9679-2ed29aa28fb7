
.cropper-config.data-v-05deedaf {
	padding: 20rpx 40rpx;
}
.cropper-content.data-v-05deedaf {
	min-height: 750rpx;
	width: 100%;
}
.uni-corpper.data-v-05deedaf {
	position: relative;
	overflow: hidden;
	-webkit-user-select: none;
	user-select: none;
	-webkit-tap-highlight-color: transparent;
	-webkit-touch-callout: none;
	box-sizing: border-box;
}
.uni-corpper-content.data-v-05deedaf {
	position: relative;
}
.uni-corpper-content image.data-v-05deedaf {
	display: block;
	width: 100%;
	min-width: 0 !important;
	max-width: none !important;
	height: 100%;
	min-height: 0 !important;
	max-height: none !important;
	image-orientation: 0deg !important;
	margin: 0 auto;
}

/* 移动图片效果 */
.uni-cropper-drag-box.data-v-05deedaf {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	cursor: move;
	background: rgba(0, 0, 0, 0.6);
	z-index: 1;
}

/* 内部的信息 */
.uni-corpper-crop-box.data-v-05deedaf {
	position: absolute;
	background: rgba(255, 255, 255, 0.3);
	z-index: 2;
}
.uni-corpper-crop-box .uni-cropper-view-box.data-v-05deedaf {
	position: relative;
	display: block;
	width: 100%;
	height: 100%;
	overflow: visible;
	outline: 1rpx solid #69f;
	outline-color: rgba(102, 153, 255, .75)
}

/* 横向虚线 */
.uni-cropper-dashed-h.data-v-05deedaf {
	position: absolute;
	top: 33.33333333%;
	left: 0;
	width: 100%;
	height: 33.33333333%;
	border-top: 1rpx dashed rgba(255, 255, 255, 0.5);
	border-bottom: 1rpx dashed rgba(255, 255, 255, 0.5);
}

/* 纵向虚线 */
.uni-cropper-dashed-v.data-v-05deedaf {
	position: absolute;
	left: 33.33333333%;
	top: 0;
	width: 33.33333333%;
	height: 100%;
	border-left: 1rpx dashed rgba(255, 255, 255, 0.5);
	border-right: 1rpx dashed rgba(255, 255, 255, 0.5);
}

/* 四个方向的线  为了之后的拖动事件*/
.uni-cropper-line-t.data-v-05deedaf {
	position: absolute;
	display: block;
	width: 100%;
	background-color: #69f;
	top: 0;
	left: 0;
	height: 1rpx;
	opacity: 0.1;
	cursor: n-resize;
}
.uni-cropper-line-t.data-v-05deedaf::before {
	content: '';
	position: absolute;
	top: 50%;
	right: 0rpx;
	width: 100%;
	-webkit-transform: translate3d(0, -50%, 0);
	transform: translate3d(0, -50%, 0);
	bottom: 0;
	height: 41rpx;
	background: transparent;
	z-index: 11;
}
.uni-cropper-line-r.data-v-05deedaf {
	position: absolute;
	display: block;
	background-color: #69f;
	top: 0;
	right: 0rpx;
	width: 1rpx;
	opacity: 0.1;
	height: 100%;
	cursor: e-resize;
}
.uni-cropper-line-r.data-v-05deedaf::before {
	content: '';
	position: absolute;
	top: 0;
	left: 50%;
	width: 41rpx;
	-webkit-transform: translate3d(-50%, 0, 0);
	transform: translate3d(-50%, 0, 0);
	bottom: 0;
	height: 100%;
	background: transparent;
	z-index: 11;
}
.uni-cropper-line-b.data-v-05deedaf {
	position: absolute;
	display: block;
	width: 100%;
	background-color: #69f;
	bottom: 0;
	left: 0;
	height: 1rpx;
	opacity: 0.1;
	cursor: s-resize;
}
.uni-cropper-line-b.data-v-05deedaf::before {
	content: '';
	position: absolute;
	top: 50%;
	right: 0rpx;
	width: 100%;
	-webkit-transform: translate3d(0, -50%, 0);
	transform: translate3d(0, -50%, 0);
	bottom: 0;
	height: 41rpx;
	background: transparent;
	z-index: 11;
}
.uni-cropper-line-l.data-v-05deedaf {
	position: absolute;
	display: block;
	background-color: #69f;
	top: 0;
	left: 0;
	width: 1rpx;
	opacity: 0.1;
	height: 100%;
	cursor: w-resize;
}
.uni-cropper-line-l.data-v-05deedaf::before {
	content: '';
	position: absolute;
	top: 0;
	left: 50%;
	width: 41rpx;
	-webkit-transform: translate3d(-50%, 0, 0);
	transform: translate3d(-50%, 0, 0);
	bottom: 0;
	height: 100%;
	background: transparent;
	z-index: 11;
}
.uni-cropper-point.data-v-05deedaf {
	width: 5rpx;
	height: 5rpx;
	background-color: #69f;
	opacity: .75;
	position: absolute;
	z-index: 3;
}
.point-t.data-v-05deedaf {
	top: -3rpx;
	left: 50%;
	margin-left: -3rpx;
	cursor: n-resize;
}
.point-tr.data-v-05deedaf {
	top: -3rpx;
	left: 100%;
	margin-left: -3rpx;
	cursor: n-resize;
}
.point-r.data-v-05deedaf {
	top: 50%;
	left: 100%;
	margin-left: -3rpx;
	margin-top: -3rpx;
	cursor: n-resize;
}
.point-rb.data-v-05deedaf {
	left: 100%;
	top: 100%;
	-webkit-transform: translate3d(-50%, -50%, 0);
	transform: translate3d(-50%, -50%, 0);
	cursor: n-resize;
	width: 36rpx;
	height: 36rpx;
	background-color: #69f;
	position: absolute;
	z-index: 1112;
	opacity: 1;
}
.point-b.data-v-05deedaf {
	left: 50%;
	top: 100%;
	margin-left: -3rpx;
	margin-top: -3rpx;
	cursor: n-resize;
}
.point-bl.data-v-05deedaf {
	left: 0%;
	top: 100%;
	margin-left: -3rpx;
	margin-top: -3rpx;
	cursor: n-resize;
}
.point-l.data-v-05deedaf {
	left: 0%;
	top: 50%;
	margin-left: -3rpx;
	margin-top: -3rpx;
	cursor: n-resize;
}
.point-lt.data-v-05deedaf {
	left: 0%;
	top: 0%;
	margin-left: -3rpx;
	margin-top: -3rpx;
	cursor: n-resize;
}

/* 裁剪框预览内容 */
.uni-cropper-viewer.data-v-05deedaf {
	position: relative;
	width: 100%;
	height: 100%;
	overflow: hidden;
}
.uni-cropper-viewer image.data-v-05deedaf {
	position: absolute;
	z-index: 2;
}

