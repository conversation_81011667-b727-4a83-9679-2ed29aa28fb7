import config from '@/config'
import storage from '@/utils/storage'
import constant from '@/utils/constant'
import { isHttp, isEmpty } from "@/utils/validate"
import { getUserInfo, logout } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'
import defAva from '@/static/images/default-avatar.png'

const baseUrl = config.baseUrl

const user = {
  state: {
    token: getToken(),
    id: storage.get(constant.id),
    name: storage.get(constant.name),
    avatar: storage.get(constant.avatar),
    roles: storage.get(constant.roles),
    permissions: storage.get(constant.permissions),
    gender: storage.get(constant.gender) || 0
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_ID: (state, id) => {
      state.id = id
      storage.set(constant.id, id)
    },
    SET_NAME: (state, name) => {
      state.name = name
      storage.set(constant.name, name)
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
      storage.set(constant.avatar, avatar)
    },
    SET_GENDER: (state, gender) => {
      state.gender = gender
      storage.set(constant.gender, gender)
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
      storage.set(constant.roles, roles)
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
      storage.set(constant.permissions, permissions)
    }
  },

  actions: {
    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        // 检查token是否存在
        if (!state.token) {
          reject('获取用户信息失败，未找到有效token')
          return
        }
        
        console.log('开始获取用户信息，当前token:', state.token)
        
        getUserInfo().then(res => {
          console.log('获取用户信息成功:', res)
          
          const user = res.data || {}
          let avatar = user.avatar || ""
          if (!isHttp(avatar)) {
            avatar = (isEmpty(avatar)) ? defAva : baseUrl + avatar
          }
          const userid = user.userId || ""
          const username = user.nickname || ""
          const gender = user.gender || 0
          
          // 由于微信小程序登录可能没有角色和权限，设置默认值
          const roles = user.roles || ['ROLE_DEFAULT']
          const permissions = user.permissions || ['*:*:*']
          
          commit('SET_ID', userid)
          commit('SET_NAME', username)
          commit('SET_AVATAR', avatar)
          commit('SET_GENDER', gender)
          commit('SET_ROLES', roles)
          commit('SET_PERMISSIONS', permissions)
          
          resolve(res)
        }).catch(error => {
          console.error('获取用户信息失败:', error)
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout().then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          storage.clean()
          resolve()
        }).catch(error => {
          // 即使API调用失败，也清除本地数据
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          storage.clean()
          resolve()
        })
      })
    }
  }
}

export default user
