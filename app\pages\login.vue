<template>
  <view class="wx-login-container">
    <view class="logo-content align-center justify-center flex flex-direction">
      <image style="width: 120rpx;height: 120rpx;" :src="globalConfig.appInfo.logo" mode="widthFix">
      </image>
      <text class="title">电商平台</text>
    </view>

    <view class="login-form-content">
      <view class="welcome-text">
        <text class="welcome">欢迎使用</text>
        <text class="desc">使用微信快捷登录体验更多功能</text>
      </view>

      <view class="action-btn">
        <!-- 微信小程序环境 -->
        <!-- #ifdef MP-WEIXIN -->
        <button @click="wxQuickLogin" class="login-btn cu-btn block bg-green lg round">
          <text class="iconfont icon-wechat" style="margin-right: 10rpx;"></text>微信一键登录
        </button>
        <!-- #endif -->

        <!-- 其他环境 -->
        <!-- #ifndef MP-WEIXIN -->
        <button @click="handleWxLogin" class="login-btn cu-btn block bg-green lg round">
          <text class="iconfont icon-wechat" style="margin-right: 10rpx;"></text>微信一键登录
        </button>
        <!-- #endif -->
      </view>

      <view class="xieyi text-center">
        <text class="text-grey1">登录即代表同意</text>
        <text @click="handleUserAgrement" class="text-blue">《用户协议》</text>
        <text class="text-grey1">和</text>
        <text @click="handlePrivacy" class="text-blue">《隐私政策》</text>
      </view>
    </view>
  </view>
</template>

<script>
  import { wxLogin, getInfo } from '@/api/auth'
  import { getToken, setToken, removeToken } from '@/utils/auth'

  export default {
    data() {
      return {
        globalConfig: {
          appInfo: {
            logo: "/static/images/logo.png",
            name: "电商平台"
          }
        },
        loading: false
      }
    },
    mounted() {
      // 安全获取全局配置
      try {
        const app = getApp()
        if (app && app.globalData && app.globalData.config) {
          this.globalConfig = app.globalData.config
        }
      } catch (error) {
        console.warn('获取全局配置失败，使用默认配置:', error)
      }
    },
    onLoad() {
      // 检查是否已登录
      if (getToken()) {
        // 使用 uni.reLaunch 替代 $tab.reLaunch
        uni.reLaunch({
          url: '/pages/index'
        })
      }
    },
    methods: {
      // 隐私政策
      handlePrivacy() {
        // 直接使用type参数跳转到webview页面
        uni.navigateTo({
          url: '/pages/common/webview/index?type=privacy&title=' + encodeURIComponent('隐私政策')
        })
      },
      // 用户协议
      handleUserAgrement() {
        // 直接使用type参数跳转到webview页面
        uni.navigateTo({
          url: '/pages/common/webview/index?type=agreement&title=' + encodeURIComponent('用户协议')
        })
      },

      // 微信快速登录（直接使用code登录，不再获取用户信息）
      wxQuickLogin() {
        if (this.loading) return
        this.loading = true
        // 安全调用 $modal
        if (this.$modal && this.$modal.loading) {
          this.$modal.loading("登录中...")
        } else {
          uni.showLoading({
            title: '登录中...'
          })
        }

        // 获取登录凭证code
        uni.login({
          provider: 'weixin',
          success: (loginRes) => {
            console.log('获取登录凭证成功:', loginRes)
            if (loginRes.code) {
              // 构建默认用户信息
              const userInfo = {
                nickName: '微信用户',
                avatarUrl: '', // 默认头像，后端可以设置
                gender: 0      // 性别未知
              }

              // 调用后端登录接口
              wxLogin(loginRes.code, userInfo).then(res => {
                this.loading = false
                // 安全关闭加载框
                if (this.$modal && this.$modal.closeLoading) {
                  this.$modal.closeLoading()
                } else {
                  uni.hideLoading()
                }

                // 打印完整的返回数据，用于调试
                console.log('登录接口返回数据:', JSON.stringify(res))

                if (res.code === 2000) {
                  // 登录成功，尝试提取token和用户信息
                  let token = null;
                  let userData = null;

                  // 尝试从不同位置获取token
                  if (res.data && res.data.token) {
                    token = res.data.token;
                  } else if (res.token) {
                    token = res.token;
                  }

                  // 尝试从不同位置获取用户信息
                  if (res.data && res.data.user) {
                    userData = res.data.user;
                  } else if (res.user) {
                    userData = res.user;
                  } else if (res.data) {
                    // 如果data中没有user字段，可能整个data就是用户信息
                    if (!res.data.token && !res.data.userId) {
                      userData = res.data;
                    }
                  }

                  // 如果没有找到token，报错并返回
                  if (!token) {
                    console.error('登录返回数据中未找到token:', res)
                    if (this.$modal && this.$modal.msgError) {
                      this.$modal.msgError("登录失败，未获取到授权信息")
                    } else {
                      uni.showToast({
                        title: '登录失败，未获取到授权信息',
                        icon: 'none'
                      })
                    }
                    return
                  }

                  // 保存token
                  setToken(token)
                  this.$store.commit('SET_TOKEN', token)

                  // 如果没有找到用户数据，使用默认值
                  if (!userData) {
                    console.warn('登录返回数据中未找到用户信息，将使用默认值')
                    userData = {
                      nickname: userInfo.nickName,
                      avatar: userInfo.avatarUrl,
                      gender: userInfo.gender
                    }
                  }

                  // 保存用户基本信息到本地
                  uni.setStorageSync('userInfo', {
                    nickName: userData.nickname || userInfo.nickName,
                    avatarUrl: userData.avatar_url || userData.avatar || userInfo.avatarUrl,
                    gender: userData.gender || userInfo.gender
                  })

                  // 检查是否需要完善个人资料
                  this.checkUserProfile(userData)
                } else {
                  if (this.$modal && this.$modal.msgError) {
                    this.$modal.msgError(res.msg || "登录失败，请重试")
                  } else {
                    uni.showToast({
                      title: res.msg || "登录失败，请重试",
                      icon: 'none'
                    })
                  }
                  console.error('登录失败', res)
                }
              }).catch((error) => {
                this.loading = false
                if (this.$modal && this.$modal.closeLoading) {
                  this.$modal.closeLoading()
                } else {
                  uni.hideLoading()
                }
                if (this.$modal && this.$modal.msgError) {
                  this.$modal.msgError("登录失败，请重试")
                } else {
                  uni.showToast({
                    title: "登录失败，请重试",
                    icon: 'none'
                  })
                }
                console.error('登录接口调用失败', error)
              })
            } else {
              this.loading = false
              if (this.$modal && this.$modal.closeLoading) {
                this.$modal.closeLoading()
              } else {
                uni.hideLoading()
              }
              if (this.$modal && this.$modal.msgError) {
                this.$modal.msgError("微信登录失败，请重试")
              } else {
                uni.showToast({
                  title: "微信登录失败，请重试",
                  icon: 'none'
                })
              }
              console.error('微信登录失败', loginRes)
            }
          },
          fail: (err) => {
            this.loading = false
            if (this.$modal && this.$modal.closeLoading) {
              this.$modal.closeLoading()
            } else {
              uni.hideLoading()
            }
            if (this.$modal && this.$modal.msgError) {
              this.$modal.msgError("微信登录失败，请重试")
            } else {
              uni.showToast({
                title: "微信登录失败，请重试",
                icon: 'none'
              })
            }
            console.error('微信登录失败', err)
          }
        })
      },

      // 检查用户资料是否需要完善
      checkUserProfile(user) {
        // 防止user为undefined或null
        if (!user) {
          user = {}
          console.warn('用户信息为空，将使用默认值')
        }

        // 如果用户昵称为"微信用户"或头像为空，则认为需要完善资料
        const needComplete = !user.nickname || user.nickname === '微信用户' || (!user.avatar_url && !user.avatar)

        if (needComplete) {
          uni.showModal({
            title: '完善个人资料',
            content: '为了提供更好的服务，请完善您的个人资料',
            confirmText: '去完善',
            cancelText: '暂不完善',
            success: (res) => {
              if (res.confirm) {
                // 跳转到个人资料页面，并标记为首次登录
                uni.navigateTo({
                  url: '/pages/mine/profile/index?first=1'
                })
              } else {
                // 直接进入首页
                this.loginSuccess()
              }
            }
          })
        } else {
          // 资料已完善，直接进入首页
          this.loginSuccess()
        }
      },

      // 其他平台的微信登录方法
      handleWxLogin() {
        // 使用相同的快速登录逻辑
        this.wxQuickLogin()
      },

      // 登录成功后，处理函数
      loginSuccess() {
        // 获取用户信息
        getInfo().then(res => {
          console.log('获取用户信息返回数据:', JSON.stringify(res))

          if (res.code === 2000) {
            // 尝试提取用户信息
            let userData = null;

            if (res.data) {
              userData = res.data;
            } else if (res.user) {
              userData = res.user;
            }

            if (userData) {
              // 更新用户信息到store
              this.$store.commit('SET_USER_INFO', userData)
            } else {
              console.warn('获取用户信息成功，但未找到用户数据')
            }

            // 跳转到首页
            uni.reLaunch({
              url: '/pages/index'
            })
          } else {
            if (this.$modal && this.$modal.msgError) {
              this.$modal.msgError(res.msg || "获取用户信息失败，请重新登录")
            } else {
              uni.showToast({
                title: res.msg || "获取用户信息失败，请重新登录",
                icon: 'none'
              })
            }
            // 清除token
            this.$store.commit('SET_TOKEN', '')
            removeToken()
          }
        }).catch(error => {
          console.error('获取用户信息失败:', error)
          if (this.$modal && this.$modal.msgError) {
            this.$modal.msgError("获取用户信息失败，请重新登录")
          } else {
            uni.showToast({
              title: "获取用户信息失败，请重新登录",
              icon: 'none'
            })
          }
          // 清除token
          this.$store.commit('SET_TOKEN', '')
          removeToken()
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  page {
    background-color: #ffffff;
  }

  /* 添加缺失的工具类 */
  .flex {
    display: flex;
  }

  .flex-direction {
    flex-direction: column;
  }

  .align-center {
    align-items: center;
  }

  .justify-center {
    justify-content: center;
  }

  .text-center {
    text-align: center;
  }

  .text-grey1 {
    color: #999;
  }

  .text-blue {
    color: #007aff;
  }

  .block {
    display: block;
    width: 100%;
  }

  .round {
    border-radius: 50rpx;
  }

  .lg {
    padding: 0 60rpx;
    height: 88rpx;
    line-height: 88rpx;
  }

  .bg-green {
    background-color: #09bb07;
    color: #fff;
  }

  .cu-btn {
    border: none;
    font-size: 32rpx;
    font-weight: 600;
  }

  .wx-login-container {
    width: 100%;
    min-height: 100vh;
    background-color: #ffffff;

    .logo-content {
      width: 100%;
      font-size: 24px;
      text-align: center;
      padding-top: 15%;

      image {
        border-radius: 8px;
      }

      .title {
        margin-top: 15px;
        font-weight: bold;
      }
    }

    .login-form-content {
      text-align: center;
      margin: 20px auto;
      margin-top: 20%;
      width: 80%;

      .welcome-text {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 60px;

        .welcome {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 10px;
        }

        .desc {
          font-size: 14px;
          color: #999;
        }
      }

      .login-btn {
        margin-top: 40px;
        height: 45px;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .xieyi {
        color: #333;
        margin-top: 30px;
        font-size: 12px;
      }
    }
  }
</style>
