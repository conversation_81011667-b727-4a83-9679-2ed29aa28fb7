<template>
  <view class="order-detail-container">
    <scroll-view class="detail-content" scroll-y v-if="orderInfo">
      <!-- 订单状态 -->
      <view class="status-section">
        <view class="status-icon">
          <uni-icons 
            :type="getStatusIcon(orderInfo.orderStatus)" 
            size="40" 
            :color="getStatusColor(orderInfo.orderStatus)"
          ></uni-icons>
        </view>
        <view class="status-info">
          <text class="status-text">{{ getStatusText(orderInfo.orderStatus) }}</text>
          <text class="status-desc">{{ getStatusDesc(orderInfo.orderStatus) }}</text>
        </view>
      </view>
      
      <!-- 收货地址 -->
      <view class="address-section" v-if="orderInfo.consignee">
        <view class="section-header">
          <uni-icons type="location" size="16" color="#666"></uni-icons>
          <text class="section-title">收货信息</text>
        </view>
        <view class="address-content">
          <view class="receiver-info">
            <text class="receiver-name">{{ orderInfo.consignee }}</text>
            <text class="receiver-phone">{{ orderInfo.phone }}</text>
          </view>
          <text class="receiver-address">{{ orderInfo.province }} {{ orderInfo.city }} {{ orderInfo.district }} {{ orderInfo.address }}</text>
        </view>
      </view>
      
      <!-- 商品信息 -->
      <view class="goods-section">
        <view class="section-header">
          <uni-icons type="shop" size="16" color="#666"></uni-icons>
          <text class="section-title">商品信息</text>
        </view>
        <view class="goods-list">
          <view class="goods-item" v-for="item in orderItems" :key="item.itemId">
            <image class="goods-image" :src="getFullImageUrl(item.picUrl)" mode="aspectFill"></image>
            <view class="goods-info">
              <text class="goods-name">{{ item.goodsName }}</text>
              <text class="goods-spec" v-if="item.skuName">{{ item.skuName }}</text>
              <view class="goods-price-qty">
                <text class="goods-price">¥{{ formatPrice(item.price) }}</text>
                <text class="goods-qty">x{{ item.quantity }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 订单信息 -->
      <view class="order-section">
        <view class="section-header">
          <uni-icons type="list" size="16" color="#666"></uni-icons>
          <text class="section-title">订单信息</text>
        </view>
        <view class="order-info">
          <view class="info-item">
            <text class="info-label">订单编号</text>
            <text class="info-value">{{ orderInfo.orderNo }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">下单时间</text>
            <text class="info-value">{{ formatTime(orderInfo.createTime) }}</text>
          </view>
          <view class="info-item" v-if="orderInfo.payTime">
            <text class="info-label">支付时间</text>
            <text class="info-value">{{ formatTime(orderInfo.payTime) }}</text>
          </view>
          <view class="info-item" v-if="orderInfo.shippingTime">
            <text class="info-label">发货时间</text>
            <text class="info-value">{{ formatTime(orderInfo.shippingTime) }}</text>
          </view>
          <view class="info-item" v-if="orderInfo.finishTime">
            <text class="info-label">完成时间</text>
            <text class="info-value">{{ formatTime(orderInfo.finishTime) }}</text>
          </view>
          <view class="info-item" v-if="orderInfo.userRemark">
            <text class="info-label">订单备注</text>
            <text class="info-value">{{ orderInfo.userRemark }}</text>
          </view>
        </view>
      </view>
      
      <!-- 费用明细 -->
      <view class="cost-section">
        <view class="section-header">
          <uni-icons type="wallet" size="16" color="#666"></uni-icons>
          <text class="section-title">费用明细</text>
        </view>
        <view class="cost-info">
          <view class="cost-item">
            <text class="cost-label">商品总价</text>
            <text class="cost-value">¥{{ formatPrice(orderInfo.goodsAmount || orderInfo.totalAmount) }}</text>
          </view>
          <view class="cost-item">
            <text class="cost-label">运费</text>
            <text class="cost-value">¥{{ formatPrice(orderInfo.shippingFee || 0) }}</text>
          </view>
          <view class="cost-item total">
            <text class="cost-label">实付款</text>
            <text class="cost-value">¥{{ formatPrice(orderInfo.payAmount || orderInfo.totalAmount) }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作 -->
    <view class="bottom-actions" v-if="orderInfo">
      <button class="action-btn secondary" @click="cancelOrder" v-if="orderInfo.orderStatus === '0'">取消订单</button>
      <button class="action-btn primary" @click="payOrder" v-if="orderInfo.orderStatus === '0'">立即支付</button>
      <button class="action-btn secondary" @click="confirmOrder" v-if="orderInfo.orderStatus === '2'">确认收货</button>
      <button class="action-btn secondary" @click="contactService">联系客服</button>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <uni-load-more status="loading"></uni-load-more>
    </view>
  </view>
</template>

<script>
import { getOrderInfo, cancelOrder as cancelOrderApi, confirmOrder as confirmOrderApi } from '@/api/shop.js'
import { formatPrice } from '@/utils/shop.js'
import config from '@/config.js'

export default {
  data() {
    return {
      orderId: null,
      orderInfo: null,
      orderItems: [],
      loading: false
    }
  },
  
  onLoad(options) {
    if (options.orderId) {
      this.orderId = options.orderId
      this.loadOrderDetail()
    } else {
      uni.showToast({
        title: '订单信息错误',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  },
  
  methods: {
    // 加载订单详情
    async loadOrderDetail() {
      this.loading = true
      try {
        const res = await getOrderInfo(this.orderId)
        if (res.data) {
          this.orderInfo = res.data.order
          this.orderItems = res.data.orderItems || []
        }
      } catch (error) {
        console.error('加载订单详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 支付订单
    payOrder() {
      uni.navigateTo({
        url: `/pages/shop/order/pay?orderId=${this.orderId}`
      })
    },
    
    // 取消订单
    cancelOrder() {
      uni.showModal({
        title: '提示',
        content: '确定要取消这个订单吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await cancelOrderApi(this.orderId)
              this.orderInfo.orderStatus = '4'
              uni.showToast({
                title: '取消成功',
                icon: 'success'
              })
            } catch (error) {
              console.error('取消订单失败:', error)
              uni.showToast({
                title: '取消失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 确认收货
    confirmOrder() {
      uni.showModal({
        title: '提示',
        content: '确定已收到商品吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await confirmOrderApi(this.orderId)
              this.orderInfo.orderStatus = '3'
              uni.showToast({
                title: '确认收货成功',
                icon: 'success'
              })
            } catch (error) {
              console.error('确认收货失败:', error)
              uni.showToast({
                title: '确认收货失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 联系客服
    contactService() {
      uni.showModal({
        title: '联系客服',
        content: '客服电话：400-123-4567',
        showCancel: false
      })
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '待付款',
        '1': '待发货',
        '2': '待收货',
        '3': '已完成',
        '4': '已取消'
      }
      return statusMap[status] || '未知状态'
    },

    // 获取状态描述
    getStatusDesc(status) {
      const descMap = {
        '0': '请在24小时内完成支付',
        '1': '商家正在准备发货',
        '2': '商品正在配送中',
        '3': '订单已完成',
        '4': '订单已取消'
      }
      return descMap[status] || ''
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        '0': 'wallet',
        '1': 'gear',
        '2': 'car',
        '3': 'checkmarkempty',
        '4': 'closeempty'
      }
      return iconMap[status] || 'help'
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        '0': '#ff6700',
        '1': '#1890ff',
        '2': '#1890ff',
        '3': '#52c41a',
        '4': '#999'
      }
      return colorMap[status] || '#666'
    },
    
    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    
    // 格式化价格
    formatPrice,
    
    // 获取完整图片URL
    getFullImageUrl(imagePath) {
      if (!imagePath) return '/static/images/default-goods.png'
      if (imagePath.startsWith('http')) return imagePath
      return config.baseUrl + imagePath
    }
  }
}
</script>

<style lang="scss" scoped>
.order-detail-container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.detail-content {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 120rpx;
}

.status-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.status-info {
  flex: 1;
}

.status-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.status-desc {
  font-size: 26rpx;
  color: #999;
}

.address-section, .goods-section, .order-section, .cost-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.address-content {
  .receiver-info {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 12rpx;
  }
  
  .receiver-name {
    font-size: 30rpx;
    color: #333;
    font-weight: 600;
  }
  
  .receiver-phone {
    font-size: 28rpx;
    color: #666;
  }
  
  .receiver-address {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
  }
}

.goods-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.goods-spec {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 30rpx;
  color: #ff6700;
  font-weight: 600;
}

.goods-qty {
  font-size: 28rpx;
  color: #666;
}

.info-item, .cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.total {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 20rpx;
    margin-top: 10rpx;
  }
}

.info-label, .cost-label {
  font-size: 28rpx;
  color: #666;
}

.info-value, .cost-value {
  font-size: 28rpx;
  color: #333;
  
  &.total {
    font-size: 32rpx;
    color: #ff6700;
    font-weight: 600;
  }
}

.cost-item.total .cost-value {
  font-size: 32rpx;
  color: #ff6700;
  font-weight: 600;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  border: 1rpx solid #ddd;
  
  &.primary {
    background-color: #ff6700;
    color: #fff;
    border-color: #ff6700;
  }
  
  &.secondary {
    background-color: #fff;
    color: #666;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
</style>
