<template>
  <view class="pay-container">
    <!-- 订单信息卡片 -->
    <view class="order-card" v-if="orderInfo">
      <view class="order-header">
        <text class="order-title">订单支付</text>
        <text class="order-no">订单号：{{ orderInfo.orderNo || 'undefined' }}</text>
      </view>

      <view class="amount-section">
        <text class="amount-label">支付金额</text>
        <text class="amount-value">¥{{ formatPrice(orderInfo.payAmount || orderInfo.totalAmount) }}</text>
      </view>
    </view>

    <!-- 订单商品信息 -->
    <view class="goods-section" v-if="orderInfo.orderItems && orderInfo.orderItems.length > 0">
      <view class="section-title">商品信息</view>
      <view class="goods-list">
        <view
          class="goods-item"
          v-for="(item, index) in orderInfo.orderItems"
          :key="index"
        >
          <image
            class="goods-image"
            :src="item.picUrl || '/static/images/default-goods.png'"
            mode="aspectFill"
          />
          <view class="goods-info">
            <text class="goods-name">{{ item.goodsName }}</text>
            <view class="goods-spec" v-if="item.skuInfo">
              <text class="spec-text">{{ item.skuInfo }}</text>
            </view>
            <view class="goods-price-qty">
              <text class="goods-price">¥{{ formatPrice(item.goodsPrice) }}</text>
              <text class="goods-qty">x{{ item.quantity }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 支付方式选择 -->
    <view class="payment-section">
      <view class="section-title">支付方式</view>
      <view class="payment-methods">
        <view
          class="payment-item"
          :class="{ active: selectedPaymentMethod === 'wechat' }"
          @click="selectPaymentMethod('wechat')"
        >
          <view class="payment-icon wechat-icon">
            <text class="icon-text">微</text>
          </view>
          <text class="payment-name">微信支付</text>
          <view class="payment-radio">
            <view class="radio-dot" v-if="selectedPaymentMethod === 'wechat'"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 支付状态显示 -->
    <view class="status-section" v-if="paymentStatus !== 'pending'">
      <view class="status-content">
        <view class="status-icon" :class="paymentStatus">
          <text class="status-text">{{ getStatusText() }}</text>
        </view>
        <text class="status-message">{{ getStatusMessage() }}</text>
      </view>
    </view>

    <!-- 底部支付按钮 -->
    <view class="pay-footer">
      <view class="pay-info">
        <text class="pay-label">实付：</text>
        <text class="pay-amount">¥{{ formatPrice(orderInfo ? (orderInfo.payAmount || orderInfo.totalAmount) : 0) }}</text>
      </view>
      <button
        class="pay-button"
        :class="{
          disabled: paymentStatus === 'paying' || !selectedPaymentMethod,
          success: paymentStatus === 'success'
        }"
        :disabled="paymentStatus === 'paying' || !selectedPaymentMethod"
        @click="handlePayment"
      >
        {{ getPayButtonText() }}
      </button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-overlay" v-if="loading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getOrderInfo, requestWechatPay, updatePaymentStatus, queryPaymentStatus } from '@/api/shop.js'
import { formatPrice } from '@/utils/shop.js'
import { getOpenid } from '@/utils/auth.js'

export default {
  data() {
    return {
      orderId: null,
      orderInfo: null,
      selectedPaymentMethod: 'wechat',
      paymentStatus: 'pending', // pending, paying, success, failed, timeout
      loading: false,
      paymentTimer: null,
      openid: '', // 用户openid
      paymentPollingTimer: null, // 支付状态轮询定时器
      paymentPollingCount: 0, // 轮询次数
      maxPollingCount: 30 // 最大轮询次数（30次，每2秒一次，共1分钟）
    }
  },

  async onLoad(options) {
    console.log('=== 支付页面加载 ===')
    console.log('页面参数:', options)

    if (options.orderId) {
      this.orderId = options.orderId

      // 获取用户openid
      try {
        this.openid = await this.getUserOpenid()
        console.log('获取到用户openid:', this.openid)
      } catch (error) {
        console.error('获取openid失败:', error)
      }

      this.loadOrderInfo()
    } else {
      uni.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
    }
  },

  onUnload() {
    // 清理定时器
    if (this.paymentTimer) {
      clearTimeout(this.paymentTimer)
    }
    if (this.paymentPollingTimer) {
      clearInterval(this.paymentPollingTimer)
    }
  },

  methods: {
    // 格式化价格
    formatPrice,

    // 获取用户openid
    async getUserOpenid() {
      try {
        // 先从缓存获取
        let openid = uni.getStorageSync('openid')
        if (openid) {
          return openid
        }

        // 如果缓存中没有，通过微信登录获取
        const loginRes = await uni.login({
          provider: 'weixin'
        })

        if (loginRes[1] && loginRes[1].code) {
          // 调用后端接口获取openid
          const res = await uni.request({
            url: this.$config.baseUrl + '/api/auth/wechat/openid',
            method: 'POST',
            data: {
              code: loginRes[1].code
            }
          })

          if (res[1] && res[1].data && res[1].data.code === 200) {
            openid = res[1].data.data.openid
            // 缓存openid
            uni.setStorageSync('openid', openid)
            return openid
          }
        }

        throw new Error('获取openid失败')
      } catch (error) {
        console.error('获取openid异常:', error)
        // 返回一个测试用的openid（开发环境）
        return 'test_openid_for_development'
      }
    },

    // 加载订单信息
    async loadOrderInfo() {
      console.log('=== 开始加载订单信息 ===')
      this.loading = true

      try {
        uni.showLoading({
          title: '加载中...'
        })

        console.log('加载订单信息，订单ID:', this.orderId)
        const res = await getOrderInfo(this.orderId)
        console.log('订单信息加载结果:', res)

        uni.hideLoading()

        if (res && res.code === 200 && res.data) {
          console.log('=== 原始API返回数据 ===')
          console.log('完整返回数据:', res.data)

          // 修复数据结构：后端返回 {order: {...}, orderItems: [...]}
          if (res.data.order) {
            this.orderInfo = res.data.order
            // 将订单商品信息合并到订单对象中
            this.orderInfo.orderItems = res.data.orderItems || []
          } else {
            // 兼容直接返回订单对象的情况
            this.orderInfo = res.data
          }

          console.log('=== 订单信息详细检查 ===')
          console.log('订单ID:', this.orderInfo.orderId)
          console.log('订单号:', this.orderInfo.orderNo)
          console.log('订单状态:', this.orderInfo.orderStatus, '(类型:', typeof this.orderInfo.orderStatus, ')')
          console.log('支付状态:', this.orderInfo.payStatus, '(类型:', typeof this.orderInfo.payStatus, ')')
          console.log('订单商品数量:', this.orderInfo.orderItems ? this.orderInfo.orderItems.length : 0)
          console.log('完整订单信息:', this.orderInfo)

          // 检查订单状态
          if (this.orderInfo.orderStatus !== '0') {
            console.error('订单状态验证失败!')
            console.error('期望状态: "0" (字符串)')
            console.error('实际状态:', this.orderInfo.orderStatus, '(类型:', typeof this.orderInfo.orderStatus, ')')

            uni.showModal({
              title: '提示',
              content: `订单状态异常，无法支付。当前状态：${this.orderInfo.orderStatus}`,
              showCancel: false,
              success: () => {
                uni.navigateBack()
              }
            })
            return
          }

          // 检查支付状态
          if (this.orderInfo.payStatus === '1') {
            console.log('订单已支付，显示支付成功状态')
            this.paymentStatus = 'success'
            uni.showToast({
              title: '订单已支付',
              icon: 'success'
            })
          } else {
            console.log('订单未支付，可以进行支付')
          }
        } else {
          console.error('获取订单信息失败:', res)
          uni.showToast({
            title: res.msg || '获取订单信息失败',
            icon: 'none'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 2000)
        }
      } catch (error) {
        console.error('加载订单信息异常:', error)
        uni.hideLoading()
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 选择支付方式
    selectPaymentMethod(method) {
      console.log('选择支付方式:', method)
      this.selectedPaymentMethod = method
    },

    // 处理支付
    async handlePayment() {
      if (!this.selectedPaymentMethod) {
        uni.showToast({
          title: '请选择支付方式',
          icon: 'none'
        })
        return
      }

      if (this.paymentStatus === 'success') {
        // 已支付，跳转到成功页面
        uni.redirectTo({
          url: `/pages/shop/order/success?orderId=${this.orderId}`
        })
        return
      }

      console.log('=== 开始支付流程 ===')
      console.log('支付方式:', this.selectedPaymentMethod)
      console.log('订单ID:', this.orderId)

      if (this.selectedPaymentMethod === 'wechat') {
        await this.handleWechatPay()
      }
    },

    // 微信支付处理
    async handleWechatPay() {
      console.log('=== 微信支付处理 ===')

      try {
        this.paymentStatus = 'paying'

        uni.showLoading({
          title: '支付中...'
        })

        // 请求微信支付参数
        const payData = {
          orderId: this.orderId,
          paymentMethod: 'wechat',
          openid: this.openid || 'test_openid_for_development'
        }

        console.log('请求支付参数:', payData)
        const res = await requestWechatPay(payData)
        console.log('支付参数返回:', res)

        uni.hideLoading()

        if (res && res.code === 200 && res.data && res.data.payParams) {
          const payParams = res.data.payParams
          console.log('调用微信支付:', payParams)

          // 调用微信支付
          uni.requestPayment({
            provider: 'wxpay',
            timeStamp: payParams.timeStamp,
            nonceStr: payParams.nonceStr,
            package: payParams.package,
            signType: payParams.signType,
            paySign: payParams.paySign,
            success: (payRes) => {
              console.log('微信支付成功:', payRes)
              this.handlePaymentSuccess()
            },
            fail: (payErr) => {
              console.log('微信支付失败:', payErr)
              this.handlePaymentFailed(payErr)
            },
            complete: () => {
              // 支付完成后开始轮询支付状态
              this.startPaymentStatusPolling()
            }
          })
        } else {
          console.error('获取支付参数失败:', res)
          this.paymentStatus = 'failed'
          uni.showToast({
            title: res.msg || '获取支付参数失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('微信支付异常:', error)
        uni.hideLoading()
        this.paymentStatus = 'failed'
        uni.showToast({
          title: '支付请求失败',
          icon: 'none'
        })
      }
    },

    // 开始支付状态轮询
    startPaymentStatusPolling() {
      console.log('=== 开始支付状态轮询 ===')

      this.paymentPollingCount = 0
      this.paymentPollingTimer = setInterval(async () => {
        this.paymentPollingCount++
        console.log(`支付状态轮询第${this.paymentPollingCount}次`)

        try {
          const res = await queryPaymentStatus(this.orderId)
          console.log('支付状态查询结果:', res)

          if (res && res.code === 200 && res.data) {
            const payStatus = res.data.payStatus

            if (payStatus === '1') {
              // 支付成功
              console.log('轮询检测到支付成功')
              this.stopPaymentStatusPolling()
              this.handlePaymentSuccess()
              return
            } else if (payStatus === '2') {
              // 支付失败
              console.log('轮询检测到支付失败')
              this.stopPaymentStatusPolling()
              this.handlePaymentFailed({ errMsg: '支付失败' })
              return
            }
          }

          // 达到最大轮询次数
          if (this.paymentPollingCount >= this.maxPollingCount) {
            console.log('支付状态轮询超时')
            this.stopPaymentStatusPolling()
            this.handlePaymentTimeout()
          }

        } catch (error) {
          console.error('支付状态查询异常:', error)

          // 查询异常也计入轮询次数
          if (this.paymentPollingCount >= this.maxPollingCount) {
            this.stopPaymentStatusPolling()
            this.handlePaymentTimeout()
          }
        }
      }, 2000) // 每2秒查询一次
    },

    // 停止支付状态轮询
    stopPaymentStatusPolling() {
      if (this.paymentPollingTimer) {
        clearInterval(this.paymentPollingTimer)
        this.paymentPollingTimer = null
      }
      this.paymentPollingCount = 0
    },

    // 支付成功处理
    async handlePaymentSuccess() {
      console.log('=== 支付成功处理 ===')

      this.paymentStatus = 'success'
      this.stopPaymentStatusPolling()

      // 显示成功提示
      uni.showToast({
        title: '支付成功',
        icon: 'success',
        duration: 2000
      })

      // 更新订单信息
      this.orderInfo.payStatus = '1'
      this.orderInfo.orderStatus = '1'

      // 延迟跳转到成功页面
      setTimeout(() => {
        uni.redirectTo({
          url: `/pages/shop/order/success?orderId=${this.orderId}`
        })
      }, 2000)
    },

    // 支付失败处理
    handlePaymentFailed(error) {
      console.log('=== 支付失败处理 ===')
      console.log('支付错误:', error)

      this.paymentStatus = 'failed'
      this.stopPaymentStatusPolling()

      let errorMsg = '支付失败'
      if (error.errMsg) {
        if (error.errMsg.includes('cancel')) {
          errorMsg = '支付已取消'
        } else if (error.errMsg.includes('fail')) {
          errorMsg = '支付失败，请重试'
        }
      }

      uni.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 3000
      })
    },

    // 支付超时处理
    handlePaymentTimeout() {
      console.log('=== 支付超时处理 ===')

      this.paymentStatus = 'timeout'
      this.stopPaymentStatusPolling()

      uni.showModal({
        title: '支付状态确认',
        content: '支付状态确认超时，请手动确认支付结果。如果已完成支付，请稍后查看订单状态。',
        confirmText: '查看订单',
        cancelText: '重新支付',
        success: (res) => {
          if (res.confirm) {
            // 跳转到订单列表
            uni.redirectTo({
              url: '/pages/shop/order/list'
            })
          } else {
            // 重新支付
            this.paymentStatus = 'pending'
          }
        }
      })
    },

    // 获取状态文本
    getStatusText() {
      switch (this.paymentStatus) {
        case 'paying':
          return '支付中'
        case 'success':
          return '支付成功'
        case 'failed':
          return '支付失败'
        case 'timeout':
          return '支付超时'
        default:
          return ''
      }
    },

    // 获取状态消息
    getStatusMessage() {
      switch (this.paymentStatus) {
        case 'paying':
          return '正在处理支付请求...'
        case 'success':
          return '订单支付成功，即将跳转'
        case 'failed':
          return '支付失败，请重新支付'
        case 'timeout':
          return '支付超时，请重新支付'
        default:
          return ''
      }
    },

    // 获取支付按钮文本
    getPayButtonText() {
      switch (this.paymentStatus) {
        case 'paying':
          return '支付中...'
        case 'success':
          return '支付成功'
        case 'failed':
          return '重新支付'
        case 'timeout':
          return '重新支付'
        default:
          return '立即支付'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pay-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 订单信息卡片 */
.order-card {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.order-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.order-no {
  font-size: 28rpx;
  color: #666;
}

.amount-section {
  text-align: center;
  padding: 20rpx 0;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.amount-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff4757;
}

/* 商品信息 */
.goods-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.goods-list {
  padding: 0 30rpx;
}

.goods-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 10rpx;
}

.goods-spec {
  margin-bottom: 10rpx;
}

.spec-text {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 30rpx;
  color: #ff4757;
  font-weight: bold;
}

.goods-qty {
  font-size: 28rpx;
  color: #666;
}

/* 支付方式 */
.payment-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.payment-methods {
  padding: 0 30rpx;
}

.payment-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &.active {
    .payment-radio {
      border-color: #007aff;

      .radio-dot {
        display: block;
      }
    }
  }
}

.payment-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;

  &.wechat-icon {
    background-color: #07c160;
  }
}

.icon-image {
  width: 100%;
  height: 100%;
}

.icon-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
}

.payment-name {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.payment-radio {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-dot {
  width: 20rpx;
  height: 20rpx;
  background-color: #007aff;
  border-radius: 50%;
  display: none;
}

/* 支付状态 */
.status-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
}

.status-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;

  &.paying {
    background-color: #ffa726;
  }

  &.success {
    background-color: #4caf50;
  }

  &.failed {
    background-color: #f44336;
  }

  &.timeout {
    background-color: #ff9800;
  }
}

.status-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
}

.status-message {
  font-size: 30rpx;
  color: #666;
}

/* 底部支付按钮 */
.pay-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
}

.pay-info {
  display: flex;
  align-items: center;
}

.pay-label {
  font-size: 28rpx;
  color: #666;
}

.pay-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4757;
  margin-left: 10rpx;
}

.pay-button {
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 32rpx;
  font-weight: bold;

  &.disabled {
    background-color: #ccc;
    color: #999;
  }

  &.success {
    background-color: #4caf50;
  }
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
