#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
花百科爬虫 - 爬取花卉大全数据
包含反爬虫处理机制
"""

import requests
from bs4 import BeautifulSoup
import time
import random
import json
import csv
import os
import logging
from fake_useragent import UserAgent
import pandas as pd
from urllib.parse import urljoin

class HuabaikeSpider:
    def __init__(self):
        self.base_url = "https://www.huabaike.com"
        self.session = requests.Session()
        self.ua = UserAgent()
        self.setup_logging()
        self.setup_session()
        self.data = []
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('huabaike_spider.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_session(self):
        """设置请求会话，包含反爬虫处理"""
        # 设置请求头
        headers = {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://www.huabaike.com/',
        }
        self.session.headers.update(headers)
        
        # 设置代理池（可选）
        # self.proxies = self.get_proxy_list()
        
    def get_random_delay(self, min_delay=1, max_delay=3):
        """获取随机延迟时间"""
        return random.uniform(min_delay, max_delay)
        
    def safe_request(self, url, max_retries=3):
        """安全请求，包含重试机制"""
        for attempt in range(max_retries):
            try:
                # 随机延迟
                time.sleep(self.get_random_delay())
                
                # 更换User-Agent
                self.session.headers['User-Agent'] = self.ua.random
                
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                
                self.logger.info(f"成功请求: {url}")
                return response
                
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {url}, 错误: {e}")
                if attempt < max_retries - 1:
                    time.sleep(self.get_random_delay(2, 5))
                else:
                    self.logger.error(f"请求最终失败: {url}")
                    return None
                    
    def get_flower_categories(self):
        """获取花卉分类 - 专门爬取花卉大全下的7个植物分类"""
        # 花卉大全下的7个主要植物分类
        plant_categories = [
            {'name': '草本植物', 'url': f"{self.base_url}/cbzw"},
            {'name': '木本植物', 'url': f"{self.base_url}/mbzw"},
            {'name': '藤本植物', 'url': f"{self.base_url}/tbzw"},
            {'name': '兰科植物', 'url': f"{self.base_url}/lkzw"},
            {'name': '水生植物', 'url': f"{self.base_url}/sszw"},
            {'name': '球根植物', 'url': f"{self.base_url}/qgzw"},
            {'name': '宿根植物', 'url': f"{self.base_url}/sgzw"},
        ]

        categories = []

        for category in plant_categories:
            # 验证URL是否可访问
            response = self.safe_request(category['url'])
            if response:
                categories.append(category)
                self.logger.info(f"添加分类: {category['name']} - {category['url']}")
            else:
                self.logger.warning(f"无法访问分类: {category['name']} - {category['url']}")

        self.logger.info(f"成功找到 {len(categories)} 个植物分类")
        return categories
        
    def get_flower_list(self, category_url):
        """获取某个分类下的花卉列表"""
        response = self.safe_request(category_url)
        if not response:
            return []

        soup = BeautifulSoup(response.content, 'html.parser')
        flowers = []

        # 查找花卉链接，基于实际网站结构
        flower_links = soup.find_all('a', href=True)

        for link in flower_links:
            href = link.get('href')
            text = link.get_text(strip=True)

            if href and text and self.is_flower_detail_url(href):
                full_url = urljoin(self.base_url, href)

                # 过滤掉太短或太长的文本
                if 2 <= len(text) <= 50 and not any(skip in text for skip in ['更多', '查看', '首页', '论坛']):
                    flowers.append({
                        'name': text,
                        'url': full_url
                    })

        # 去重
        seen_urls = set()
        unique_flowers = []
        for flower in flowers:
            if flower['url'] not in seen_urls:
                seen_urls.add(flower['url'])
                unique_flowers.append(flower)

        self.logger.info(f"在分类 {category_url} 中找到 {len(unique_flowers)} 个花卉")
        return unique_flowers
        
    def is_flower_detail_url(self, url):
        """判断是否为花卉详情页URL"""
        # 根据实际网站分析结果更新URL模式
        patterns = [
            '/mbzw/',      # 名贵植物
            '/qgzw/',      # 球根植物
            '/cbzw/',      # 草本植物
            '/jtyh/',      # 家庭养花
            '/yhjq/',      # 养花技巧
            '/hyjk/',      # 花与健康
            '/yzfh/',      # 叶子发黄
            '/langen/',    # 烂根
            '/chonghai/',  # 虫害
            '/duorou/',    # 多肉
            '/lktp/',      # 兰科图谱
            '/ghzw/',      # 观花植物
            '.html'        # HTML页面
        ]

        # 检查是否包含这些模式，并且不是外部链接
        if any(pattern in url for pattern in patterns):
            # 排除论坛和问答链接
            if not any(exclude in url for exclude in ['bbs.', 'wenda.', 'tuku.', 'forum']):
                return True
        return False

    def extract_botanical_info(self, soup, flower_data):
        """提取植物学信息：学名、科、属等"""
        try:
            # 方法1：查找包含植物学信息的表格或列表
            info_tables = soup.find_all(['table', 'dl', 'div'], class_=lambda x: x and any(
                keyword in x.lower() for keyword in ['info', 'detail', 'basic', 'property', 'attr']
            ))

            for table in info_tables:
                text_content = table.get_text()
                self.parse_botanical_text(text_content, flower_data)

            # 方法2：查找包含特定关键词的段落
            all_text = soup.get_text()
            self.parse_botanical_text(all_text, flower_data)

            # 方法3：查找meta标签中的信息
            meta_tags = soup.find_all('meta')
            for meta in meta_tags:
                content = meta.get('content', '')
                if content:
                    self.parse_botanical_text(content, flower_data)

        except Exception as e:
            self.logger.warning(f"提取植物学信息失败: {e}")

    def parse_botanical_text(self, text, flower_data):
        """从文本中解析植物学信息"""
        if not text:
            return

        text = text.replace('\n', ' ').replace('\r', ' ')

        # 学名提取模式
        scientific_patterns = [
            r'学名[：:]\s*([A-Z][a-z]+\s+[a-z]+(?:\s+[a-z]+)?)',
            r'拉丁名[：:]\s*([A-Z][a-z]+\s+[a-z]+(?:\s+[a-z]+)?)',
            r'Scientific name[：:]\s*([A-Z][a-z]+\s+[a-z]+(?:\s+[a-z]+)?)',
            r'([A-Z][a-z]+\s+[a-z]+)(?=\s|$|，|。)',  # 直接匹配拉丁学名格式
        ]

        # 科名提取模式
        family_patterns = [
            r'([^，。\s]+科)(?=\s|$|，|。)',
            r'科[：:]\s*([^，。\s]+科?)',
            r'Family[：:]\s*([^，。\s]+)',
        ]

        # 属名提取模式
        genus_patterns = [
            r'([^，。\s]+属)(?=\s|$|，|。)',
            r'属[：:]\s*([^，。\s]+属?)',
            r'Genus[：:]\s*([^，。\s]+)',
        ]

        # 花期提取模式
        flowering_patterns = [
            r'花期[：:]\s*([^，。\n]+)',
            r'开花时间[：:]\s*([^，。\n]+)',
            r'花期为([^，。\n]+)',
        ]

        import re

        # 提取学名
        if not flower_data['scientific_name']:
            for pattern in scientific_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    scientific_name = match.group(1).strip()
                    # 验证学名格式（至少包含两个单词）
                    if len(scientific_name.split()) >= 2:
                        flower_data['scientific_name'] = scientific_name
                        break

        # 提取科名
        if not flower_data['family']:
            for pattern in family_patterns:
                match = re.search(pattern, text)
                if match:
                    family = match.group(1).strip()
                    if family and len(family) < 20:  # 避免提取到过长的文本
                        flower_data['family'] = family
                        break

        # 提取属名
        if not flower_data['genus']:
            for pattern in genus_patterns:
                match = re.search(pattern, text)
                if match:
                    genus = match.group(1).strip()
                    if genus and len(genus) < 20:  # 避免提取到过长的文本
                        flower_data['genus'] = genus
                        break

        # 提取花期
        if not flower_data['flowering_period']:
            for pattern in flowering_patterns:
                match = re.search(pattern, text)
                if match:
                    flowering_period = match.group(1).strip()
                    if flowering_period and len(flowering_period) < 50:
                        flower_data['flowering_period'] = flowering_period
                        break

    def parse_flower_detail(self, flower_url):
        """解析花卉详情页"""
        response = self.safe_request(flower_url)
        if not response:
            return None
            
        soup = BeautifulSoup(response.content, 'html.parser')
        
        flower_data = {
            'url': flower_url,
            'name': '',
            'scientific_name': '',
            'family': '',
            'genus': '',
            'description': '',
            'care_tips': '',
            'images': [],
            'flowering_period': '',
            'growth_habit': ''
        }
        
        try:
            # 提取花卉名称
            title = soup.find('h1') or soup.find('title')
            if title:
                flower_data['name'] = title.get_text(strip=True)
                
            # 提取学名、科、属等信息
            self.extract_botanical_info(soup, flower_data)

            # 提取图片 - 只保存jpg类型的图片
            images = soup.find_all('img')
            for img in images:
                src = img.get('src')
                if src:
                    full_img_url = urljoin(self.base_url, src)
                    # 只保存jpg类型的图片
                    if full_img_url.lower().endswith('.jpg'):
                        flower_data['images'].append(full_img_url)

            # 提取描述信息（保留HTML标签结构）
            content_div = soup.find('div', class_='content') or soup.find('div', class_='article-content')
            if content_div:
                # 保留HTML标签，但清理一些不需要的标签
                # 移除script、style等标签
                for tag in content_div.find_all(['script', 'style', 'noscript']):
                    tag.decompose()

                # 获取HTML内容，保留标签结构
                flower_data['description'] = str(content_div)
            else:
                # 如果找不到特定的content div，尝试其他可能的容器
                main_content = soup.find('div', class_='main-content') or soup.find('article') or soup.find('div', id='content')
                if main_content:
                    # 移除不需要的标签
                    for tag in main_content.find_all(['script', 'style', 'noscript']):
                        tag.decompose()
                    flower_data['description'] = str(main_content)
                else:
                    # 最后的备选方案：获取body中的主要文本内容，但保留基本标签
                    body = soup.find('body')
                    if body:
                        # 移除导航、侧边栏等不相关内容
                        for tag in body.find_all(['nav', 'header', 'footer', 'aside', 'script', 'style']):
                            tag.decompose()

                        # 查找包含主要内容的段落
                        paragraphs = body.find_all(['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                        content_parts = []
                        for p in paragraphs:
                            text = p.get_text(strip=True)
                            if len(text) > 20:  # 只保留有意义的段落
                                content_parts.append(str(p))

                        if content_parts:
                            flower_data['description'] = '\n'.join(content_parts)
                
            self.logger.info(f"成功解析花卉: {flower_data['name']}")
            return flower_data
            
        except Exception as e:
            self.logger.error(f"解析花卉详情失败: {flower_url}, 错误: {e}")
            return None
            
    def save_to_csv(self, filename='huabaike_flowers.csv'):
        """保存数据到CSV文件"""
        if not self.data:
            self.logger.warning("没有数据可保存")
            return
            
        df = pd.DataFrame(self.data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        self.logger.info(f"数据已保存到 {filename}, 共 {len(self.data)} 条记录")
        
    def save_to_json(self, filename='huabaike_flowers.json'):
        """保存数据到JSON文件"""
        if not self.data:
            self.logger.warning("没有数据可保存")
            return
            
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.data, f, ensure_ascii=False, indent=2)
        self.logger.info(f"数据已保存到 {filename}, 共 {len(self.data)} 条记录")
        
    def run(self):
        """运行爬虫"""
        self.logger.info("开始爬取花百科数据...")
        
        # 获取花卉分类
        categories = self.get_flower_categories()
        
        if not categories:
            self.logger.error("未能获取到花卉分类，请检查网站结构")
            return
            
        # 遍历每个分类
        for category in categories:  # 处理所有7个植物分类
            self.logger.info(f"正在处理分类: {category['name']}")

            # 获取该分类下的花卉列表
            flowers = self.get_flower_list(category['url'])

            # 解析每个花卉的详情
            for flower in flowers[:10000]:  # 每个分类限制前10个花卉
                self.logger.info(f"正在解析花卉: {flower['name']}")
                flower_data = self.parse_flower_detail(flower['url'])
                if flower_data:
                    flower_data['category'] = category['name']  # 添加分类信息
                    self.data.append(flower_data)

                # 随机延迟，避免被封
                time.sleep(self.get_random_delay(2, 4))
                
        # 保存数据
        self.save_to_csv()
        self.save_to_json()
        
        self.logger.info(f"爬取完成，共获取 {len(self.data)} 条花卉数据")

if __name__ == "__main__":
    spider = HuabaikeSpider()
    spider.run()
