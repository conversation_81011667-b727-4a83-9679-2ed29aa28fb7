#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from django.db import connection

def mark_migration_applied():
    """标记迁移为已应用"""
    cursor = connection.cursor()
    
    # 插入迁移记录
    sql = """
    INSERT INTO django_migrations (app, name, applied) 
    VALUES ('system', '0002_add_miniprogram_fields', NOW())
    ON DUPLICATE KEY UPDATE applied = NOW()
    """
    
    try:
        cursor.execute(sql)
        print("✓ 迁移记录已标记为已应用")
    except Exception as e:
        print(f"✗ 标记迁移失败: {e}")

if __name__ == "__main__":
    mark_migration_applied()
