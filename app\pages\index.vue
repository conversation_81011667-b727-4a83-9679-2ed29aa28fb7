<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="custom-nav" :style="{paddingTop: statusBarHeight + 'px'}">
      <view class="nav-content">
        <view class="title-text">
          <text>植物之家</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{paddingTop: navHeight + 'px'}">
      <!-- 轮播图 -->
      <swiper class="banner" indicator-dots autoplay circular :interval="3000" :duration="500">
        <swiper-item v-for="(item, index) in banners" :key="index">
          <image :src="item.image" mode="aspectFill"></image>
        </swiper-item>
      </swiper>

      <!-- 植物分类导航 -->
      <view class="category-section">
        <view class="category-item" v-for="(item, index) in plantCategories" :key="index" @click="handleCategoryClick(item)">
          <image
            :src="item.icon"
            mode="aspectFit"
          ></image>
          <text>{{item.name}}</text>
        </view>
      </view>

      <!-- 推荐植物 -->
      <view class="plant-section">
        <view class="section-header">
          <text class="title">推荐植物</text>
        </view>
        <view class="plant-list">
          <view class="plant-item" v-for="(item, index) in recommendPlants" :key="index" @click="handlePlantClick(item)">
            <image :src="item.image" mode="aspectFill"></image>
            <view class="plant-info">
              <text class="plant-name">{{item.name}}</text>
              <text class="plant-desc">{{item.description}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 植物百科 -->
      <view class="plant-section">
        <view class="section-header">
          <text class="title">植物百科</text>
        </view>
        <view class="plant-list">
          <view class="plant-item" v-for="(item, index) in encyclopediaPlants" :key="index" @click="handlePlantClick(item)">
            <image :src="item.image" mode="aspectFill"></image>
            <view class="plant-info">
              <text class="plant-name">{{item.name}}</text>
              <text class="plant-desc">{{item.description}}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import config from '@/config.js'

export default {
  data() {
    return {
      statusBarHeight: 20,
      navHeight: 64,
      banners: [],
      plantCategories: [],
      recommendPlants: [],
      encyclopediaPlants: []
    }
  },
  onLoad() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    this.navHeight = this.statusBarHeight + 44

    // 加载数据
    this.loadData()
  },
  methods: {
    // 获取完整的图片URL
    getFullImageUrl(imageUrl) {
      if (!imageUrl) return '/static/images/default.png'
      if (imageUrl.startsWith('http')) return imageUrl
      return config.baseUrl + imageUrl
    },

    // 加载数据
    async loadData() {
      try {
        await Promise.all([
          this.loadBanners(),
          this.loadPlantCategories(),
          this.loadRecommendPlants(),
          this.loadEncyclopediaPlants()
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    // 加载轮播图
    async loadBanners() {
      try {
        // 使用植物相关的默认轮播图
        this.banners = [
          { id: 1, image: '/static/images/banner/plant01.jpg' },
          { id: 2, image: '/static/images/banner/plant02.jpg' },
          { id: 3, image: '/static/images/banner/plant03.jpg' }
        ]
      } catch (error) {
        console.error('加载轮播图失败:', error)
      }
    },

    // 加载植物分类
    async loadPlantCategories() {
      try {
        // 使用植物相关的分类
        this.plantCategories = [
          { id: 1, name: '观叶植物', icon: '/static/images/plant/leaf.png' },
          { id: 2, name: '观花植物', icon: '/static/images/plant/flower.png' },
          { id: 3, name: '多肉植物', icon: '/static/images/plant/succulent.png' },
          { id: 4, name: '水培植物', icon: '/static/images/plant/hydroponic.png' },
          { id: 5, name: '盆景植物', icon: '/static/images/plant/bonsai.png' },
          { id: 6, name: '药用植物', icon: '/static/images/plant/medicinal.png' }
        ]
      } catch (error) {
        console.error('加载植物分类失败:', error)
      }
    },

    // 加载推荐植物
    async loadRecommendPlants() {
      try {
        // 使用植物相关的推荐数据
        this.recommendPlants = [
          { id: 1, name: '绿萝', description: '净化空气，易养护', image: '/static/images/plant/lvluo.jpg' },
          { id: 2, name: '吊兰', description: '观叶植物，适合室内', image: '/static/images/plant/diaoLan.jpg' },
          { id: 3, name: '虎皮兰', description: '耐旱易养，净化空气', image: '/static/images/plant/hupilan.jpg' },
          { id: 4, name: '发财树', description: '寓意吉祥，观叶植物', image: '/static/images/plant/facaishu.jpg' }
        ]
      } catch (error) {
        console.error('加载推荐植物失败:', error)
      }
    },

    // 加载植物百科
    async loadEncyclopediaPlants() {
      try {
        // 使用植物百科数据
        this.encyclopediaPlants = [
          { id: 5, name: '仙人掌', description: '多肉植物，耐旱性强', image: '/static/images/plant/xianrenzhang.jpg' },
          { id: 6, name: '芦荟', description: '药用植物，美容护肤', image: '/static/images/plant/luhui.jpg' },
          { id: 7, name: '薄荷', description: '香草植物，清香怡人', image: '/static/images/plant/bohe.jpg' },
          { id: 8, name: '茉莉花', description: '观花植物，香气浓郁', image: '/static/images/plant/molihua.jpg' }
        ]
      } catch (error) {
        console.error('加载植物百科失败:', error)
      }
    },

    // 点击分类
    handleCategoryClick(item) {
      uni.showToast({
        title: `查看${item.name}`,
        icon: 'none'
      })
    },

    // 点击植物
    handlePlantClick(item) {
      uni.showToast({
        title: `查看${item.name}详情`,
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f7;
  position: relative;
}

.custom-nav {
  position: fixed;
  width: 100%;
  background-color: #3c96f3;
  z-index: 999;
  
  .nav-content {
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;

    .title-text {
      text {
        color: #fff;
        font-size: 36rpx;
        font-weight: bold;
      }
    }
  }
}

.content {
  width: 100%;
  height: 100%;
  
  .banner {
    width: 100%;
    height: 350rpx;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .category-section {
    display: flex;
    justify-content: space-around;
    padding: 20rpx 0;
    background-color: #fff;
    margin-bottom: 20rpx;
    
    .category-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10rpx;
      border-radius: 12rpx;
      transition: all 0.3s ease;

      &:active {
        background-color: #f5f5f5;
        transform: scale(0.95);
      }

      image {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 10rpx;
        border-radius: 8rpx;
      }

      text {
        font-size: 24rpx;
        color: #333;
        text-align: center;
        line-height: 1.2;
      }
    }
  }
  
  .plant-section {
    background-color: #fff;
    margin-bottom: 20rpx;
    padding: 20rpx;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #2d8659;
      }
    }

    .plant-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .plant-item {
        width: 48%;
        background-color: #f9f9f9;
        border-radius: 10rpx;
        overflow: hidden;
        margin-bottom: 20rpx;

        image {
          width: 100%;
          height: 320rpx;
        }

        .plant-info {
          padding: 15rpx;

          .plant-name {
            font-size: 28rpx;
            color: #333;
            font-weight: bold;
            margin-bottom: 8rpx;
          }

          .plant-desc {
            font-size: 24rpx;
            color: #666;
            line-height: 1.4;
          }
        }
      }
    }
  }
}
</style>
