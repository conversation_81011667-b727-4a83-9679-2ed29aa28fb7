<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="custom-nav" :style="{paddingTop: statusBarHeight + 'px'}">
      <view class="nav-content">
        <view class="search-box" @click="handleSearch">
          <uni-icons type="search" size="16" color="#999"></uni-icons>
          <text>搜索商品</text>
        </view>
        <view class="cart-icon" @click="goToCart">
          <uni-icons type="cart" size="20" color="#fff"></uni-icons>
          <view class="cart-badge" v-if="cartCount > 0">{{ cartCount > 99 ? '99+' : cartCount }}</view>
        </view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{paddingTop: navHeight + 'px'}">
      <!-- 轮播图 -->
      <swiper class="banner" indicator-dots autoplay circular :interval="3000" :duration="500">
        <swiper-item v-for="(item, index) in banners" :key="index">
          <image :src="item.image" mode="aspectFill" @click="handleBannerClick(item)"></image>
        </swiper-item>
      </swiper>
      
      <!-- 分类导航 -->
      <view class="category-section">
        <view class="category-item" v-for="(item, index) in categories" :key="index" @click="handleCategoryClick(item)">
          <image
            :src="item.icon"
            mode="aspectFit"
            @error="handleImageError"
            @load="handleImageLoad"
          ></image>
          <text>{{item.name}}</text>
        </view>
      </view>
      
      <!-- 推荐商品 -->
      <view class="goods-section">
        <view class="section-header">
          <text class="title">推荐商品</text>
          <view class="more" @click="handleViewMore('recommend')">
            <text>查看更多</text>
            <view class="iconfont icon-right"></view>
          </view>
        </view>
        <view class="goods-list">
          <view class="goods-item" v-for="(item, index) in recommendGoods" :key="index" @click="handleGoodsClick(item)">
            <image :src="item.image" mode="aspectFill"></image>
            <view class="goods-info">
              <text class="goods-name">{{item.name}}</text>
              <view class="goods-price">
                <text class="price">¥{{item.price}}</text>
                <text class="original-price" v-if="item.originalPrice">¥{{item.originalPrice}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 新品上架 -->
      <view class="goods-section">
        <view class="section-header">
          <text class="title">新品上架</text>
          <view class="more" @click="handleViewMore('new')">
            <text>查看更多</text>
            <view class="iconfont icon-right"></view>
          </view>
        </view>
        <view class="goods-list">
          <view class="goods-item" v-for="(item, index) in newGoods" :key="index" @click="handleGoodsClick(item)">
            <image :src="item.image" mode="aspectFill"></image>
            <view class="goods-info">
              <text class="goods-name">{{item.name}}</text>
              <view class="goods-price">
                <text class="price">¥{{item.price}}</text>
                <text class="original-price" v-if="item.originalPrice">¥{{item.originalPrice}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getBanners, getCategories, getRecommendGoods, getGoodsList } from '@/api/shop.js'
import { formatPrice, formatImageUrl, cart } from '@/utils/shop.js'
import config from '@/config.js'

export default {
  data() {
    return {
      statusBarHeight: 20,
      navHeight: 64,
      cartCount: 0,
      banners: [],
      categories: [],
      recommendGoods: [],
      newGoods: []
    }
  },
  onLoad() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    this.navHeight = this.statusBarHeight + 44

    // 加载数据
    this.loadData()
  },
  onShow() {
    // 更新购物车数量
    this.updateCartCount()
  },
  methods: {
    // 获取完整的图片URL
    getFullImageUrl(imageUrl) {
      if (!imageUrl) return '/static/images/default.png'
      if (imageUrl.startsWith('http')) return imageUrl
      return config.baseUrl + imageUrl
    },

    // 获取分类图标
    getCategoryIcon(iconPath, categoryName) {
      // 如果后端提供了完整的图标URL，直接使用
      if (iconPath && iconPath.startsWith('http')) {
        return iconPath
      }

      // 根据分类名称映射到本地图标
      const iconMap = {
        '手机数码': '/static/images/category/phone.svg',
        '家用电器': '/static/images/category/appliance.svg',
        '电脑办公': '/static/images/category/computer.svg',
        '服装鞋包': '/static/images/category/clothes.svg',
        '美妆护肤': '/static/images/category/beauty.svg',
        '食品生鲜': '/static/images/category/food.svg',
        '运动户外': '/static/images/category/sports.svg',
        '图书音像': '/static/images/category/books.svg'
      }

      return iconMap[categoryName] || '/static/images/category/default.svg'
    },

    // 加载数据
    async loadData() {
      try {
        await Promise.all([
          this.loadBanners(),
          this.loadCategories(),
          this.loadRecommendGoods(),
          this.loadNewGoods()
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    // 加载轮播图
    async loadBanners() {
      try {
        const res = await getBanners()
        console.log('轮播图数据:', res)
        // 映射后端数据字段到前端需要的格式
        this.banners = (res.data || []).map(item => ({
          id: item.bannerId,
          image: this.getFullImageUrl(item.imageUrl),
          title: item.title,
          linkType: item.linkType,
          linkValue: item.linkValue
        }))
      } catch (error) {
        console.error('加载轮播图失败:', error)
        // 使用默认数据
        this.banners = [
          { id: 1, image: '/static/images/banner/banner01.jpg', link: '' },
          { id: 2, image: '/static/images/banner/banner02.jpg', link: '' },
          { id: 3, image: '/static/images/banner/banner03.jpg', link: '' }
        ]
      }
    },

    // 加载分类
    async loadCategories() {
      try {
        const res = await getCategories({ parentId: 0, pageSize: 8 })
        console.log('分类数据:', res)
        // 映射后端数据字段到前端需要的格式
        this.categories = (res.rows || []).map(item => ({
          id: item.categoryId,
          name: item.name,
          icon: this.getCategoryIcon(item.icon, item.name)
        }))
      } catch (error) {
        console.error('加载分类失败:', error)
        // 使用默认数据
        this.categories = [
          { id: 1, name: '手机数码', icon: '/static/images/category/phone.svg' },
          { id: 2, name: '家用电器', icon: '/static/images/category/appliance.svg' },
          { id: 3, name: '电脑办公', icon: '/static/images/category/computer.svg' },
          { id: 4, name: '服装鞋包', icon: '/static/images/category/clothes.svg' },
          { id: 5, name: '美妆护肤', icon: '/static/images/category/beauty.svg' },
          { id: 6, name: '食品生鲜', icon: '/static/images/category/food.svg' }
        ]
      }

      // 调试：打印图标路径
      this.$nextTick(() => {
        this.debugCategoryIcons()
      })
    },

    // 加载推荐商品
    async loadRecommendGoods() {
      try {
        const res = await getRecommendGoods({ pageSize: 6 })
        console.log('推荐商品数据:', res)
        // 映射后端数据字段到前端需要的格式
        this.recommendGoods = (res.rows || []).map(item => ({
          id: item.goodsId,
          name: item.goodsName,
          price: item.price,
          originalPrice: item.originalPrice,
          image: this.getFullImageUrl(item.coverImg) || '/static/images/goods/default.png'
        }))
      } catch (error) {
        console.error('加载推荐商品失败:', error)
        // 使用默认数据
        this.recommendGoods = [
          { id: 1, name: '商品名称1', price: '199.00', originalPrice: '299.00', image: '/static/images/goods/goods1.png' },
          { id: 2, name: '商品名称2', price: '299.00', originalPrice: '399.00', image: '/static/images/goods/goods2.png' }
        ]
      }
    },

    // 加载新品
    async loadNewGoods() {
      try {
        const res = await getGoodsList({
          orderBy: 'create_time desc',
          pageSize: 6
        })
        console.log('新品数据:', res)
        // 映射后端数据字段到前端需要的格式
        this.newGoods = (res.rows || []).map(item => ({
          id: item.goodsId,
          name: item.goodsName,
          price: item.price,
          originalPrice: item.originalPrice,
          image: this.getFullImageUrl(item.coverImg) || '/static/images/goods/default.png'
        }))
      } catch (error) {
        console.error('加载新品失败:', error)
        // 使用默认数据
        this.newGoods = [
          { id: 3, name: '商品名称3', price: '99.00', originalPrice: '199.00', image: '/static/images/goods/goods3.png' },
          { id: 4, name: '商品名称4', price: '399.00', originalPrice: '499.00', image: '/static/images/goods/goods4.png' }
        ]
      }
    },

    // 更新购物车数量
    updateCartCount() {
      this.cartCount = cart.getCount()
    },

    // 搜索
    handleSearch() {
      uni.navigateTo({
        url: '/pages/shop/search/index'
      })
    },

    // 跳转到购物车
    goToCart() {
      uni.switchTab({
        url: '/pages/shop/cart/index'
      })
    },

    // 点击轮播图
    handleBannerClick(item) {
      if (item.linkType === 'goods' && item.linkValue) {
        this.handleGoodsClick({ id: item.linkValue })
      } else if (item.linkType === 'category' && item.linkValue) {
        this.handleCategoryClick({ id: item.linkValue })
      }
    },

    // 点击分类
    handleCategoryClick(item) {
      uni.navigateTo({
        url: `/pages/shop/goods/list?categoryId=${item.id}`
      })
    },

    // 点击商品
    handleGoodsClick(item) {
      uni.navigateTo({
        url: `/pages/shop/goods/detail?goodsId=${item.id}`
      })
    },

    // 查看更多
    handleViewMore(type) {
      if (type === 'recommend') {
        uni.navigateTo({
          url: '/pages/shop/goods/list?type=recommend'
        })
      } else {
        uni.navigateTo({
          url: '/pages/shop/goods/list?type=new'
        })
      }
    },

    // 格式化价格
    formatPrice,

    // 图标加载错误处理
    handleImageError(e) {
      console.log('图标加载失败:', e.detail)
      // 设置默认图标
      const target = e.target || e.currentTarget
      if (target) {
        // 在UniApp中，需要通过这种方式设置图片源
        this.$nextTick(() => {
          // 找到对应的分类项并更新图标
          const failedSrc = e.detail.errMsg
          console.log('失败的图标路径:', failedSrc)
        })
      }
    },

    // 图标加载成功
    handleImageLoad(e) {
      console.log('图标加载成功')
    },

    // 调试：打印所有分类图标路径
    debugCategoryIcons() {
      console.log('=== 分类图标调试信息 ===')
      this.categories.forEach((category, index) => {
        console.log(`${index + 1}. ${category.name}: ${category.icon}`)
      })
      console.log('========================')
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f7;
  position: relative;
}

.custom-nav {
  position: fixed;
  width: 100%;
  background-color: #3c96f3;
  z-index: 999;
  
  .nav-content {
    height: 44px;
    display: flex;
    align-items: center;
    padding: 0 15px;
    
    .search-box {
      flex: 1;
      height: 32px;
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
      margin-right: 15px;
    }

    .cart-icon {
      position: relative;

      .cart-badge {
        position: absolute;
        top: -8px;
        right: -8px;
        background-color: #ff4757;
        color: #fff;
        font-size: 20rpx;
        min-width: 32rpx;
        height: 32rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8rpx;
      }
    }
  }
}

.content {
  width: 100%;
  height: 100%;
  
  .banner {
    width: 100%;
    height: 350rpx;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .category-section {
    display: flex;
    justify-content: space-around;
    padding: 20rpx 0;
    background-color: #fff;
    margin-bottom: 20rpx;
    
    .category-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10rpx;
      border-radius: 12rpx;
      transition: all 0.3s ease;

      &:active {
        background-color: #f5f5f5;
        transform: scale(0.95);
      }

      image {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 10rpx;
        border-radius: 8rpx;
      }

      text {
        font-size: 24rpx;
        color: #333;
        text-align: center;
        line-height: 1.2;
      }
    }
  }
  
  .goods-section {
    background-color: #fff;
    margin-bottom: 20rpx;
    padding: 20rpx;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      .title {
        font-size: 32rpx;
        font-weight: bold;
      }
      
      .more {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .goods-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      
      .goods-item {
        width: 48%;
        background-color: #f9f9f9;
        border-radius: 10rpx;
        overflow: hidden;
        margin-bottom: 20rpx;
        
        image {
          width: 100%;
          height: 320rpx;
        }
        
        .goods-info {
          padding: 15rpx;
          
          .goods-name {
            font-size: 28rpx;
            color: #333;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            margin-bottom: 10rpx;
          }
          
          .goods-price {
            display: flex;
            align-items: center;
            
            .price {
              font-size: 32rpx;
              color: #ff6700;
              font-weight: bold;
            }
            
            .original-price {
              font-size: 24rpx;
              color: #999;
              text-decoration: line-through;
              margin-left: 10rpx;
            }
          }
        }
      }
    }
  }
}
</style>
