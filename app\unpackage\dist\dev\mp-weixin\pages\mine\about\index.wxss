@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-7f1ae28d {
  background-color: #f8f8f8;
}
.copyright.data-v-7f1ae28d {
  margin-top: 50rpx;
  text-align: center;
  line-height: 60rpx;
  color: #999;
}
.header-section.data-v-7f1ae28d {
  display: flex;
  padding: 30rpx 0 0;
  flex-direction: column;
  align-items: center;
}
