/**
 * 工具函数集合
 */

/**
 * 格式化时间
 * @param {Date|string|number} time 时间
 * @param {string} format 格式化字符串
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
	const date = new Date(time)
	
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	const day = String(date.getDate()).padStart(2, '0')
	const hour = String(date.getHours()).padStart(2, '0')
	const minute = String(date.getMinutes()).padStart(2, '0')
	const second = String(date.getSeconds()).padStart(2, '0')
	
	return format
		.replace('YYYY', year)
		.replace('MM', month)
		.replace('DD', day)
		.replace('HH', hour)
		.replace('mm', minute)
		.replace('ss', second)
}

/**
 * 获取相对时间
 * @param {Date|string|number} time 时间
 * @returns {string} 相对时间字符串
 */
export function getRelativeTime(time) {
	const now = new Date()
	const date = new Date(time)
	const diff = now - date
	
	const minute = 60 * 1000
	const hour = 60 * minute
	const day = 24 * hour
	const week = 7 * day
	const month = 30 * day
	const year = 365 * day
	
	if (diff < minute) {
		return '刚刚'
	} else if (diff < hour) {
		return Math.floor(diff / minute) + '分钟前'
	} else if (diff < day) {
		return Math.floor(diff / hour) + '小时前'
	} else if (diff < week) {
		return Math.floor(diff / day) + '天前'
	} else if (diff < month) {
		return Math.floor(diff / week) + '周前'
	} else if (diff < year) {
		return Math.floor(diff / month) + '个月前'
	} else {
		return Math.floor(diff / year) + '年前'
	}
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay = 300) {
	let timer = null
	return function(...args) {
		if (timer) clearTimeout(timer)
		timer = setTimeout(() => {
			func.apply(this, args)
		}, delay)
	}
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay = 300) {
	let timer = null
	return function(...args) {
		if (!timer) {
			timer = setTimeout(() => {
				func.apply(this, args)
				timer = null
			}, delay)
		}
	}
}

/**
 * 深拷贝
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
	if (obj === null || typeof obj !== 'object') {
		return obj
	}
	
	if (obj instanceof Date) {
		return new Date(obj.getTime())
	}
	
	if (obj instanceof Array) {
		return obj.map(item => deepClone(item))
	}
	
	if (typeof obj === 'object') {
		const cloned = {}
		for (let key in obj) {
			if (obj.hasOwnProperty(key)) {
				cloned[key] = deepClone(obj[key])
			}
		}
		return cloned
	}
	
	return obj
}

/**
 * 生成UUID
 * @returns {string} UUID字符串
 */
export function generateUUID() {
	return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
		const r = Math.random() * 16 | 0
		const v = c === 'x' ? r : (r & 0x3 | 0x8)
		return v.toString(16)
	})
}

/**
 * 验证手机号
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
export function validatePhone(phone) {
	const phoneRegex = /^1[3-9]\d{9}$/
	return phoneRegex.test(phone)
}

/**
 * 验证邮箱
 * @param {string} email 邮箱
 * @returns {boolean} 是否有效
 */
export function validateEmail(email) {
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
	return emailRegex.test(email)
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
	if (bytes === 0) return '0 B'
	
	const k = 1024
	const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
	const i = Math.floor(Math.log(bytes) / Math.log(k))
	
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 获取图片信息
 * @param {string} src 图片路径
 * @returns {Promise} 图片信息
 */
export function getImageInfo(src) {
	return new Promise((resolve, reject) => {
		uni.getImageInfo({
			src,
			success: resolve,
			fail: reject
		})
	})
}

/**
 * 选择图片
 * @param {Object} options 选择选项
 * @returns {Promise} 选择结果
 */
export function chooseImage(options = {}) {
	const defaultOptions = {
		count: 1,
		sizeType: ['original', 'compressed'],
		sourceType: ['album', 'camera']
	}
	
	return new Promise((resolve, reject) => {
		uni.chooseImage({
			...defaultOptions,
			...options,
			success: resolve,
			fail: reject
		})
	})
}

/**
 * 预览图片
 * @param {Array} urls 图片URL数组
 * @param {number} current 当前显示图片索引
 */
export function previewImage(urls, current = 0) {
	uni.previewImage({
		urls: Array.isArray(urls) ? urls : [urls],
		current: typeof current === 'number' ? current : urls.indexOf(current)
	})
}

/**
 * 保存图片到相册
 * @param {string} filePath 图片路径
 * @returns {Promise} 保存结果
 */
export function saveImageToPhotosAlbum(filePath) {
	return new Promise((resolve, reject) => {
		uni.saveImageToPhotosAlbum({
			filePath,
			success: resolve,
			fail: reject
		})
	})
}

/**
 * 获取位置信息
 * @param {Object} options 选项
 * @returns {Promise} 位置信息
 */
export function getLocation(options = {}) {
	const defaultOptions = {
		type: 'gcj02'
	}
	
	return new Promise((resolve, reject) => {
		uni.getLocation({
			...defaultOptions,
			...options,
			success: resolve,
			fail: reject
		})
	})
}

/**
 * 设置剪贴板内容
 * @param {string} data 要复制的内容
 * @returns {Promise} 设置结果
 */
export function setClipboardData(data) {
	return new Promise((resolve, reject) => {
		uni.setClipboardData({
			data,
			success: resolve,
			fail: reject
		})
	})
}

/**
 * 获取剪贴板内容
 * @returns {Promise} 剪贴板内容
 */
export function getClipboardData() {
	return new Promise((resolve, reject) => {
		uni.getClipboardData({
			success: resolve,
			fail: reject
		})
	})
}

/**
 * 页面跳转
 * @param {string} url 页面路径
 * @param {Object} params 参数
 */
export function navigateTo(url, params = {}) {
	if (Object.keys(params).length > 0) {
		const queryString = Object.keys(params)
			.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
			.join('&')
		url += (url.includes('?') ? '&' : '?') + queryString
	}
	
	uni.navigateTo({ url })
}

/**
 * 页面重定向
 * @param {string} url 页面路径
 * @param {Object} params 参数
 */
export function redirectTo(url, params = {}) {
	if (Object.keys(params).length > 0) {
		const queryString = Object.keys(params)
			.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
			.join('&')
		url += (url.includes('?') ? '&' : '?') + queryString
	}
	
	uni.redirectTo({ url })
}

/**
 * 返回上一页
 * @param {number} delta 返回层数
 */
export function navigateBack(delta = 1) {
	uni.navigateBack({ delta })
}

export default {
	formatTime,
	getRelativeTime,
	debounce,
	throttle,
	deepClone,
	generateUUID,
	validatePhone,
	validateEmail,
	formatFileSize,
	getImageInfo,
	chooseImage,
	previewImage,
	saveImageToPhotosAlbum,
	getLocation,
	setClipboardData,
	getClipboardData,
	navigateTo,
	redirectTo,
	navigateBack
}
