<template>
  <view class="goods-list-container">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" @click="showSortMenu = !showSortMenu">
        <text>{{ currentSort.name }}</text>
        <uni-icons type="arrowdown" size="12" color="#666"></uni-icons>
      </view>
      <view class="filter-item" @click="showFilterMenu = !showFilterMenu">
        <text>筛选</text>
        <uni-icons type="tune" size="14" color="#666"></uni-icons>
      </view>
      <view class="filter-item" @click="toggleViewMode">
        <text v-if="viewMode === 'grid'">列表</text>
        <text v-else>网格</text>
      </view>
    </view>

    <!-- 排序菜单 -->
    <view class="sort-menu" v-if="showSortMenu" @click="showSortMenu = false">
      <view class="sort-options" @click.stop>
        <view 
          class="sort-option" 
          :class="{ active: currentSort.value === sort.value }"
          v-for="sort in sortOptions" 
          :key="sort.value"
          @click="selectSort(sort)"
        >
          <text>{{ sort.name }}</text>
          <uni-icons v-if="currentSort.value === sort.value" type="checkmarkempty" size="16" color="#007aff"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 筛选菜单 -->
    <view class="filter-menu" v-if="showFilterMenu" @click="showFilterMenu = false">
      <view class="filter-content" @click.stop>
        <view class="filter-section">
          <text class="filter-title">价格区间</text>
          <view class="price-range">
            <input class="price-input" v-model="filterParams.minPrice" placeholder="最低价" type="number" />
            <text>-</text>
            <input class="price-input" v-model="filterParams.maxPrice" placeholder="最高价" type="number" />
          </view>
        </view>
        <view class="filter-actions">
          <button class="reset-btn" @click="resetFilter">重置</button>
          <button class="confirm-btn" @click="applyFilter">确定</button>
        </view>
      </view>
    </view>

    <!-- 商品列表 -->
    <scroll-view 
      scroll-y 
      class="goods-scroll"
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <!-- 网格视图 -->
      <view class="goods-grid" v-if="viewMode === 'grid'">
        <view 
          class="goods-item-grid" 
          v-for="goods in goodsList" 
          :key="goods.goodsId"
          @click="goToGoodsDetail(goods)"
        >
          <image class="goods-image" :src="getFullImageUrl(goods.coverImg || goods.mainPic)" mode="aspectFill"></image>
          <view class="goods-info">
            <text class="goods-name">{{ goods.goodsName }}</text>
            <view class="price-row">
              <text class="current-price">¥{{ formatPrice(goods.price || goods.sellPrice || 0) }}</text>
              <text class="original-price" v-if="(goods.originalPrice || goods.marketPrice || 0) > (goods.price || goods.sellPrice || 0)">¥{{ formatPrice(goods.originalPrice || goods.marketPrice || 0) }}</text>
            </view>
            <view class="goods-meta">
              <text class="sales">已售{{ goods.sales || goods.salesCount || 0 }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 列表视图 -->
      <view class="goods-list" v-else>
        <view 
          class="goods-item-list" 
          v-for="goods in goodsList" 
          :key="goods.goodsId"
          @click="goToGoodsDetail(goods)"
        >
          <image class="goods-image" :src="getFullImageUrl(goods.coverImg || goods.mainPic)" mode="aspectFill"></image>
          <view class="goods-info">
            <text class="goods-name">{{ goods.goodsName }}</text>
            <text class="goods-desc">{{ goods.goodsDesc }}</text>
            <view class="price-row">
              <text class="current-price">¥{{ formatPrice(goods.price || goods.sellPrice || 0) }}</text>
              <text class="original-price" v-if="(goods.originalPrice || goods.marketPrice || 0) > (goods.price || goods.sellPrice || 0)">¥{{ formatPrice(goods.originalPrice || goods.marketPrice || 0) }}</text>
            </view>
            <view class="goods-meta">
              <text class="sales">已售{{ goods.sales || goods.salesCount || 0 }}</text>
              <text class="rating" v-if="goods.rating">{{ goods.rating }}分</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <uni-load-more :status="loadStatus"></uni-load-more>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && goodsList.length === 0">
        <uni-icons type="shop" size="60" color="#ccc"></uni-icons>
        <text>暂无商品</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getGoodsList } from '@/api/shop.js'
import { formatPrice } from '@/utils/shop.js'
import request from '@/utils/request'
import config from '@/config.js'

export default {
  data() {
    return {
      // 页面参数
      categoryId: '',
      categoryName: '',
      keyword: '',
      type: '',
      
      // 视图模式
      viewMode: 'grid', // grid | list
      
      // 排序和筛选
      showSortMenu: false,
      showFilterMenu: false,
      currentSort: { name: '综合排序', value: 'default' },
      sortOptions: [
        { name: '综合排序', value: 'default' },
        { name: '销量优先', value: 'sales' },
        { name: '价格从低到高', value: 'price_asc' },
        { name: '价格从高到低', value: 'price_desc' },
        { name: '最新上架', value: 'newest' }
      ],
      
      // 筛选参数
      filterParams: {
        minPrice: '',
        maxPrice: ''
      },
      
      // 列表数据
      goodsList: [],
      loading: false,
      refreshing: false,
      loadStatus: 'more',
      
      // 分页参数
      pageNum: 1,
      pageSize: 20,
      hasMore: true
    }
  },
  async onLoad(options) {
    this.categoryId = options.categoryId || ''
    this.keyword = options.keyword || ''
    this.type = options.type || ''

    // 如果有分类ID，通过API获取分类名称
    if (this.categoryId) {
      await this.loadCategoryName()
    }

    // 设置导航标题
    if (this.categoryName) {
      uni.setNavigationBarTitle({
        title: this.categoryName
      })
    } else if (this.keyword) {
      uni.setNavigationBarTitle({
        title: `搜索"${this.keyword}"`
      })
    }

    this.loadGoodsList()
  },
  methods: {
    // 加载分类名称
    async loadCategoryName() {
      if (!this.categoryId) return

      try {
        const res = await request({
          url: `/api/shop/category/${this.categoryId}`,
          method: 'get'
        })
        if (res.code === 200 && res.data) {
          this.categoryName = res.data.name
        } else {
          this.categoryName = '商品分类'
        }
      } catch (error) {
        console.error('获取分类名称失败:', error)
        this.categoryName = '商品分类'
      }
    },

    // 加载商品列表
    async loadGoodsList(refresh = false) {
      if (this.loading) return
      
      if (refresh) {
        this.pageNum = 1
        this.hasMore = true
        this.goodsList = []
      }
      
      if (!this.hasMore) {
        this.loadStatus = 'noMore'
        return
      }
      
      this.loading = true
      this.loadStatus = 'loading'
      
      try {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
        
        // 添加筛选条件
        if (this.categoryId) params.categoryId = this.categoryId
        if (this.keyword) params.keyword = this.keyword
        if (this.filterParams.minPrice) params.minPrice = this.filterParams.minPrice
        if (this.filterParams.maxPrice) params.maxPrice = this.filterParams.maxPrice
        
        // 添加排序条件
        switch (this.currentSort.value) {
          case 'sales':
            params.orderBy = 'sales_count desc'
            break
          case 'price_asc':
            params.orderBy = 'sell_price asc'
            break
          case 'price_desc':
            params.orderBy = 'sell_price desc'
            break
          case 'newest':
            params.orderBy = 'create_time desc'
            break
        }
        
        // 根据类型添加特殊条件
        if (this.type === 'recommend') {
          params.isRecommend = 1
        } else if (this.type === 'new') {
          params.orderBy = 'create_time desc'
        }
        
        const res = await getGoodsList(params)
        const newList = res.rows || []

        // 调试：打印后端返回的数据结构
        console.log('=== 商品列表数据调试 ===')
        console.log('完整响应:', res)
        if (newList.length > 0) {
          console.log('第一个商品数据:', newList[0])
          console.log('图片字段检查:')
          console.log('- coverImg:', newList[0].coverImg)
          console.log('- mainPic:', newList[0].mainPic)
          console.log('- 所有字段:', Object.keys(newList[0]))
        }
        console.log('========================')

        if (refresh) {
          this.goodsList = newList
        } else {
          this.goodsList.push(...newList)
        }
        
        this.hasMore = newList.length >= this.pageSize
        this.pageNum++
        
        this.loadStatus = this.hasMore ? 'more' : 'noMore'
        
      } catch (error) {
        console.error('加载商品列表失败:', error)
        this.loadStatus = 'more'
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.refreshing = true
      this.loadGoodsList(true)
    },
    
    // 加载更多
    loadMore() {
      if (!this.loading && this.hasMore) {
        this.loadGoodsList()
      }
    },
    
    // 选择排序
    selectSort(sort) {
      this.currentSort = sort
      this.showSortMenu = false
      this.loadGoodsList(true)
    },
    
    // 切换视图模式
    toggleViewMode() {
      this.viewMode = this.viewMode === 'grid' ? 'list' : 'grid'
    },
    
    // 重置筛选
    resetFilter() {
      this.filterParams = {
        minPrice: '',
        maxPrice: ''
      }
    },
    
    // 应用筛选
    applyFilter() {
      this.showFilterMenu = false
      this.loadGoodsList(true)
    },
    
    // 跳转到商品详情
    goToGoodsDetail(goods) {
      uni.navigateTo({
        url: `/pages/shop/goods/detail?goodsId=${goods.goodsId}`
      })
    },
    
    // 获取完整的图片URL
    getFullImageUrl(imageUrl) {
      if (!imageUrl) return '/static/images/goods/default.svg'
      if (imageUrl.startsWith('http')) return imageUrl
      return config.baseUrl + imageUrl
    },

    // 格式化价格
    formatPrice
  }
}
</script>

<style lang="scss" scoped>
.goods-list-container {
  height: 100vh;
  background-color: #f5f5f7;
  display: flex;
  flex-direction: column;
}

.filter-bar {
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #eee;

  .filter-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: #333;

    text {
      margin-right: 8rpx;
    }
  }
}

.sort-menu, .filter-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 200rpx;
}

.sort-options {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin: 0 40rpx;
  max-width: 400rpx;

  .sort-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 20rpx;
    border-bottom: 1px solid #eee;
    font-size: 28rpx;

    &:last-child {
      border-bottom: none;
    }

    &.active {
      color: #007aff;
    }
  }
}

.filter-content {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 40rpx;
  margin: 0 40rpx;
  min-width: 500rpx;

  .filter-section {
    margin-bottom: 40rpx;

    .filter-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      color: #333;
    }

    .price-range {
      display: flex;
      align-items: center;

      .price-input {
        flex: 1;
        height: 80rpx;
        border: 1px solid #ddd;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
      }

      text {
        margin: 0 20rpx;
        color: #666;
      }
    }
  }

  .filter-actions {
    display: flex;
    gap: 20rpx;

    button {
      flex: 1;
      height: 80rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      border: none;
    }

    .reset-btn {
      background-color: #f5f5f5;
      color: #666;
    }

    .confirm-btn {
      background-color: #007aff;
      color: #fff;
    }
  }
}

.goods-scroll {
  flex: 1;
  padding: 20rpx;
}

.goods-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .goods-item-grid {
    width: 48%;
    background-color: #fff;
    border-radius: 10rpx;
    overflow: hidden;
    margin-bottom: 20rpx;

    .goods-image {
      width: 100%;
      height: 320rpx;
    }

    .goods-info {
      padding: 20rpx;

      .goods-name {
        font-size: 28rpx;
        color: #333;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        margin-bottom: 15rpx;
      }

      .price-row {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;

        .current-price {
          font-size: 32rpx;
          color: #ff6700;
          font-weight: bold;
        }

        .original-price {
          font-size: 24rpx;
          color: #999;
          text-decoration: line-through;
          margin-left: 10rpx;
        }
      }

      .goods-meta {
        display: flex;
        justify-content: space-between;
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.goods-list {
  .goods-item-list {
    background-color: #fff;
    border-radius: 10rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    display: flex;

    .goods-image {
      width: 200rpx;
      height: 200rpx;
      border-radius: 8rpx;
      margin-right: 20rpx;
    }

    .goods-info {
      flex: 1;

      .goods-name {
        font-size: 30rpx;
        color: #333;
        font-weight: bold;
        margin-bottom: 10rpx;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .goods-desc {
        font-size: 26rpx;
        color: #666;
        margin-bottom: 15rpx;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .price-row {
        display: flex;
        align-items: center;
        margin-bottom: 15rpx;

        .current-price {
          font-size: 36rpx;
          color: #ff6700;
          font-weight: bold;
        }

        .original-price {
          font-size: 26rpx;
          color: #999;
          text-decoration: line-through;
          margin-left: 15rpx;
        }
      }

      .goods-meta {
        display: flex;
        justify-content: space-between;
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #999;

  text {
    margin-top: 20rpx;
    font-size: 28rpx;
  }
}
</style>
