# Generated by Django 4.2.14 on 2025-07-04 23:49

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="WxUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        help_text="Id",
                        primary_key=True,
                        serialize=False,
                        verbose_name="Id",
                    ),
                ),
                (
                    "description",
                    models.CharField(
                        blank=True,
                        help_text="描述",
                        max_length=255,
                        null=True,
                        verbose_name="描述",
                    ),
                ),
                (
                    "modifier",
                    models.CharField(
                        blank=True,
                        help_text="修改人",
                        max_length=255,
                        null=True,
                        verbose_name="修改人",
                    ),
                ),
                (
                    "dept_belong_id",
                    models.CharField(
                        blank=True,
                        help_text="数据归属部门",
                        max_length=255,
                        null=True,
                        verbose_name="数据归属部门",
                    ),
                ),
                (
                    "update_datetime",
                    models.DateTimeField(
                        auto_now=True,
                        help_text="修改时间",
                        null=True,
                        verbose_name="修改时间",
                    ),
                ),
                (
                    "create_datetime",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="创建时间",
                        null=True,
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "openid",
                    models.CharField(
                        help_text="微信用户唯一标识",
                        max_length=100,
                        unique=True,
                        verbose_name="微信OpenID",
                    ),
                ),
                (
                    "unionid",
                    models.CharField(
                        blank=True,
                        help_text="微信开放平台唯一标识",
                        max_length=100,
                        null=True,
                        verbose_name="微信UnionID",
                    ),
                ),
                (
                    "session_key",
                    models.CharField(
                        blank=True,
                        help_text="微信会话密钥",
                        max_length=100,
                        null=True,
                        verbose_name="会话密钥",
                    ),
                ),
                (
                    "nickname",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="微信昵称"
                    ),
                ),
                (
                    "avatar_url",
                    models.URLField(blank=True, null=True, verbose_name="头像URL"),
                ),
                (
                    "gender",
                    models.IntegerField(
                        choices=[(0, "未知"), (1, "男"), (2, "女")],
                        default=0,
                        verbose_name="性别",
                    ),
                ),
                (
                    "country",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="国家"
                    ),
                ),
                (
                    "province",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="省份"
                    ),
                ),
                (
                    "city",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="城市"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否激活"),
                ),
                (
                    "last_login_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="最后登录时间"
                    ),
                ),
                (
                    "creator",
                    models.ForeignKey(
                        db_constraint=False,
                        help_text="创建人",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_query_name="creator_query",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建人",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="wx_user",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="关联用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "微信小程序用户",
                "verbose_name_plural": "微信小程序用户",
                "db_table": "plant_wx_user",
                "ordering": ["-create_datetime"],
            },
        ),
        migrations.CreateModel(
            name="WxLoginLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        help_text="Id",
                        primary_key=True,
                        serialize=False,
                        verbose_name="Id",
                    ),
                ),
                (
                    "description",
                    models.CharField(
                        blank=True,
                        help_text="描述",
                        max_length=255,
                        null=True,
                        verbose_name="描述",
                    ),
                ),
                (
                    "modifier",
                    models.CharField(
                        blank=True,
                        help_text="修改人",
                        max_length=255,
                        null=True,
                        verbose_name="修改人",
                    ),
                ),
                (
                    "dept_belong_id",
                    models.CharField(
                        blank=True,
                        help_text="数据归属部门",
                        max_length=255,
                        null=True,
                        verbose_name="数据归属部门",
                    ),
                ),
                (
                    "update_datetime",
                    models.DateTimeField(
                        auto_now=True,
                        help_text="修改时间",
                        null=True,
                        verbose_name="修改时间",
                    ),
                ),
                (
                    "create_datetime",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="创建时间",
                        null=True,
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "login_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="登录时间"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP地址"
                    ),
                ),
                (
                    "user_agent",
                    models.TextField(blank=True, null=True, verbose_name="用户代理"),
                ),
                (
                    "login_result",
                    models.BooleanField(default=True, verbose_name="登录结果"),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, null=True, verbose_name="错误信息"),
                ),
                (
                    "creator",
                    models.ForeignKey(
                        db_constraint=False,
                        help_text="创建人",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_query_name="creator_query",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建人",
                    ),
                ),
                (
                    "wx_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="login_logs",
                        to="wx.wxuser",
                        verbose_name="微信用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "微信登录日志",
                "verbose_name_plural": "微信登录日志",
                "db_table": "plant_wx_login_log",
                "ordering": ["-login_time"],
            },
        ),
    ]
