{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?3395", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?373d", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?68fa", "uni-app:///pages/mine/profile/index.vue", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?d4e5", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?4cc3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "avatar", "nickname", "defaultAvatar", "is<PERSON>irstL<PERSON>in", "baseUrl", "showWechatAvatarButton", "onLoad", "methods", "checkWechatAvatarSupport", "console", "onChooseWechatAvatar", "uni", "title", "icon", "chooseAvatarImage", "itemList", "success", "fail", "canUseWechatAvatar", "chooseWechatAvatar", "getWechatAvatarInDevtools", "desc", "tryGetStoredUserInfo", "content", "getWechatAvatarOnDevice", "tryChooseAvatarOnDevice", "fallbackToAlbum", "chooseFromAlbum", "count", "sizeType", "sourceType", "chooseFromCamera", "downloadAndUploadWechatAvatar", "url", "onInputNickname", "uploadAvatar", "filePath", "name", "header", "Authorization", "updateUserAvatar", "avatar_url", "saveProfile", "nick<PERSON><PERSON>", "avatarUrl", "gender", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC+B9uB;AACA;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA,gDACAP;MACAE;MACAD;IAAA,EACA;;IAEA;IACA;;IAEA;IACA;EACA;EACAO;IACA;IACAC;MACA;;MAEA;MACAC;;MAEA;MACA;QACA;MACA;IAEA;IAEA;IACAC;MACAD;MACA;MAEA;QACA;QACA;MACA;QACAE;UAAAC;UAAAC;QAAA;MACA;IACA;IAEA;IACAC;MAAA;MACAL;;MAEA;MACA;;MAEA;MACAM;;MAEA;MACA;QACAA;MACA;MAEAJ;QACAI;QACAC;UACA;UAEA;YACA;UACA;YACA;UACA;YACA;UACA;QACA;QACAC;UACAR;QACA;MACA;IACA;IAEA;IACAS;MAEA;MACA;IAMA;IAEA;IACAC;MACAV;;MAEA;MACA;MACAA;MAEA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAW;MAAA;MACAX;;MAEA;MACAE;QACAU;QACAL;UACAP;UACA;UAEA;YACA;YACA;UACA;YACA;UACA;QACA;QACAQ;UACAR;UACA;QACA;MACA;IACA;IAEA;IACAa;MAAA;MACAb;;MAEA;MACA;MACA;QACAA;QACA;QACA;QACA;MACA;;MAEA;MACAE;QACAC;QACAW;QACAP;UACA;YACA;YACA;YACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAQ;MAAA;MACAf;;MAEA;MACAE;QACAU;QACAL;UACAP;UACA;UAEA;YACA;YACA;UACA;YACA;UACA;QACA;QACAQ;UACAR;UACA;QACA;MACA;IACA;IAEA;IACAgB;MAAA;MACAhB;;MAEA;MACA;MACAE;QACAC;QACAW;QACAP;UACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IAIA;IACAU;MAAA;MACAf;QACAC;QACAW;QACAP;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAW;MAAA;MACAhB;QACAiB;QACAC;QACAC;QACAd;UACAP;UACA;UACA;UACA;QACA;QACAQ;UACAR;UACAE;YAAAC;YAAAC;UAAA;QACA;MACA;IACA;IAEA;IACAkB;MAAA;MACApB;QACAiB;QACAC;QACAC;QACAd;UACAP;UACA;UACA;UACA;QACA;QACAQ;UACAR;UACAE;YAAAC;YAAAC;UAAA;QACA;MACA;IACA;IAEA;IACAmB;MAAA;MACAvB;MACAE;QAAAC;MAAA;;MAEA;MACAD;QACAsB;QACAjB;UACAP;UACA;YACA;YACA;UACA;YACAE;YACAA;cAAAC;cAAAC;YAAA;UACA;QACA;QACAI;UACAR;UACAE;UACAA;YAAAC;YAAAC;UAAA;QACA;MACA;IACA;IAEA;IACAqB;MACA;IACA;IAEA;IACAC;MAAA;MACA1B;;MAEA;MACA;QACAE;UAAAC;UAAAC;QAAA;QACA;MACA;MAEAF;QAAAC;MAAA;;MAEA;MACA;MACAH;MACAA;;MAEA;MACAE;QACAsB;QACAG;QACAC;QACAC;UACAC;QACA;;QACAvB;UACAP;UACA;YACA;YACAA;YAEA;cAAA;cAAA;cACA;cACA;cACAA;cAEA;gBACA;;gBAEA;gBACA;gBAEAE;gBACAA;kBAAAC;kBAAAC;gBAAA;cACA;gBACAJ;gBACAE;gBACAA;kBAAAC;kBAAAC;gBAAA;cACA;YACA;cACAJ;cACAE;cACAA;gBAAAC;gBAAAC;cAAA;YACA;UACA;YACAJ;YACAE;YACAA;cAAAC;cAAAC;YAAA;UACA;QACA;QACAI;UACAR;UACAE;UACAA;YAAAC;YAAAC;UAAA;QACA;MACA;IACA;IAEA;IACA2B;MACA;QACAC;MACA;QACA;UACA;UACA;UACA1C;UACAY;UAEAF;QACA;UACAA;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAiC;MAAA;MACA;QACA;UAAA9B;UAAAC;QAAA;MACA;MAEAF;QAAAC;MAAA;MAEA;QACAX;QACAD;MACA;MAEA;QACAW;QACA;UACA;UACAA;YACAgC;YACAC;YACAC;UACA;UAEAlC;YAAAC;UAAA;;UAEA;UACA;YACA;UACA;YACA;YACAkC;cACAnC;YACA;UACA;QACA;UACAA;YAAAC;YAAAC;UAAA;QACA;MACA;QACAF;QACAA;UAAAC;UAAAC;QAAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpdA;AAAA;AAAA;AAAA;AAA63C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/profile/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/profile/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=b1b03f2e&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=b1b03f2e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b1b03f2e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/profile/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=b1b03f2e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"profile-container\">\r\n    <view class=\"avatar-section\">\r\n      <!-- 使用安全的头像选择方式 -->\r\n      <view @tap=\"chooseAvatarImage\" class=\"avatar-button\">\r\n        <image :src=\"userInfo.avatar || defaultAvatar\" class=\"avatar\"></image>\r\n      </view>\r\n\r\n      <!-- 隐藏的 chooseAvatar 按钮，用于在支持的环境中获取微信头像 -->\r\n      <button\r\n        v-if=\"showWechatAvatarButton\"\r\n        open-type=\"chooseAvatar\"\r\n        @chooseavatar=\"onChooseWechatAvatar\"\r\n        class=\"hidden-avatar-button\">\r\n      </button>\r\n\r\n      <text class=\"tip\">点击更换头像</text>\r\n    </view>\r\n    \r\n    <view class=\"form-section\">\r\n      <view class=\"form-item\">\r\n        <text class=\"label\">昵称</text>\r\n        <input type=\"nickname\" @change=\"onInputNickname\" :value=\"userInfo.nickname\" placeholder=\"请输入您的昵称\" />\r\n      </view>\r\n      \r\n      <button @click=\"saveProfile\" class=\"save-btn cu-btn block bg-green lg\">保存</button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { updateUserInfo } from '@/api/auth'\r\nimport { getToken } from '@/utils/auth'\r\nimport config from '@/config'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userInfo: {\r\n        avatar: '',\r\n        nickname: ''\r\n      },\r\n      defaultAvatar: '/static/images/default-avatar.png',\r\n      isFirstLogin: false,\r\n      baseUrl: config.baseUrl || 'http://localhost:8080',\r\n      showWechatAvatarButton: false // 控制是否显示微信头像按钮\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    // 获取当前用户信息\r\n    const userInfo = uni.getStorageSync('userInfo') || {}\r\n    this.userInfo = {\r\n      ...userInfo,\r\n      nickname: userInfo.nickName || '',\r\n      avatar: userInfo.avatarUrl || ''\r\n    }\r\n\r\n    // 检查是否首次登录\r\n    this.isFirstLogin = options.first === '1'\r\n\r\n    // 检查当前环境是否支持 chooseAvatar\r\n    this.checkWechatAvatarSupport()\r\n  },\r\n  methods: {\r\n    // 检查微信头像支持\r\n    checkWechatAvatarSupport() {\r\n      // 检查是否在真机环境且支持 chooseAvatar\r\n      // #ifdef MP-WEIXIN\r\n      const systemInfo = uni.getSystemInfoSync()\r\n      console.log('系统信息:', systemInfo);\r\n\r\n      // 在真机上或者较新版本的开发者工具中启用\r\n      if (systemInfo.platform !== 'devtools' || systemInfo.SDKVersion >= '2.21.2') {\r\n        this.showWechatAvatarButton = false // 暂时禁用，避免ENOENT错误\r\n      }\r\n      // #endif\r\n    },\r\n\r\n    // 微信头像选择回调\r\n    onChooseWechatAvatar(e) {\r\n      console.log('微信头像选择回调:', e.detail);\r\n      const { avatarUrl } = e.detail\r\n\r\n      if (avatarUrl) {\r\n        this.userInfo.avatar = avatarUrl\r\n        this.downloadAndUploadWechatAvatar(avatarUrl)\r\n      } else {\r\n        uni.showToast({ title: '获取微信头像失败', icon: 'none' })\r\n      }\r\n    },\r\n\r\n    // 选择头像 - 提供多种选项\r\n    chooseAvatarImage() {\r\n      console.log('开始选择头像');\r\n\r\n      // 根据环境提供不同选项\r\n      const itemList = []\r\n\r\n      // 总是提供基础选项\r\n      itemList.push('从相册选择', '拍照')\r\n\r\n      // 如果支持微信头像，添加到第一位\r\n      if (this.canUseWechatAvatar()) {\r\n        itemList.unshift('使用微信头像')\r\n      }\r\n\r\n      uni.showActionSheet({\r\n        itemList: itemList,\r\n        success: (res) => {\r\n          const selectedOption = itemList[res.tapIndex]\r\n\r\n          if (selectedOption === '使用微信头像') {\r\n            this.chooseWechatAvatar()\r\n          } else if (selectedOption === '从相册选择') {\r\n            this.chooseFromAlbum()\r\n          } else if (selectedOption === '拍照') {\r\n            this.chooseFromCamera()\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.log('用户取消选择');\r\n        }\r\n      })\r\n    },\r\n\r\n    // 检查是否可以使用微信头像\r\n    canUseWechatAvatar() {\r\n      // #ifdef MP-WEIXIN\r\n      // 在微信小程序环境中总是显示微信头像选项\r\n      return true\r\n      // #endif\r\n\r\n      // #ifndef MP-WEIXIN\r\n      return false\r\n      // #endif\r\n    },\r\n\r\n    // 使用微信头像\r\n    chooseWechatAvatar() {\r\n      console.log('尝试获取微信头像');\r\n\r\n      // 检查运行环境\r\n      const systemInfo = uni.getSystemInfoSync()\r\n      console.log('当前环境:', systemInfo.platform);\r\n\r\n      if (systemInfo.platform === 'devtools') {\r\n        // 在开发者工具中使用安全的方法\r\n        this.getWechatAvatarInDevtools()\r\n      } else {\r\n        // 在真机中使用完整功能\r\n        this.getWechatAvatarOnDevice()\r\n      }\r\n    },\r\n\r\n    // 在开发者工具中获取微信头像\r\n    getWechatAvatarInDevtools() {\r\n      console.log('开发者工具环境 - 使用安全的头像获取方式');\r\n\r\n      // 方法1: 尝试 getUserProfile\r\n      uni.getUserProfile({\r\n        desc: '用于完善用户资料',\r\n        success: (res) => {\r\n          console.log('getUserProfile成功:', res);\r\n          const avatarUrl = res.userInfo.avatarUrl\r\n\r\n          if (avatarUrl && avatarUrl !== '') {\r\n            this.userInfo.avatar = avatarUrl\r\n            this.downloadAndUploadWechatAvatar(avatarUrl)\r\n          } else {\r\n            this.tryGetStoredUserInfo()\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.log('getUserProfile失败:', err);\r\n          this.tryGetStoredUserInfo()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 尝试获取已存储的用户信息\r\n    tryGetStoredUserInfo() {\r\n      console.log('尝试获取已存储的用户信息');\r\n\r\n      // 从本地存储获取用户信息\r\n      const storedUserInfo = uni.getStorageSync('userInfo')\r\n      if (storedUserInfo && storedUserInfo.avatarUrl) {\r\n        console.log('使用已存储的头像:', storedUserInfo.avatarUrl);\r\n        this.userInfo.avatar = storedUserInfo.avatarUrl\r\n        this.downloadAndUploadWechatAvatar(storedUserInfo.avatarUrl)\r\n        return\r\n      }\r\n\r\n      // 如果没有存储的头像，提供模拟数据用于开发测试\r\n      uni.showModal({\r\n        title: '开发者工具提示',\r\n        content: '在开发者工具中无法获取真实微信头像，是否使用测试头像？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 使用一个测试头像URL\r\n            const testAvatarUrl = 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132'\r\n            this.userInfo.avatar = testAvatarUrl\r\n            this.downloadAndUploadWechatAvatar(testAvatarUrl)\r\n          } else {\r\n            this.fallbackToAlbum()\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 在真机上获取微信头像\r\n    getWechatAvatarOnDevice() {\r\n      console.log('真机环境 - 使用完整的头像获取功能');\r\n\r\n      // 方法1: 优先使用 getUserProfile\r\n      uni.getUserProfile({\r\n        desc: '用于完善用户资料',\r\n        success: (res) => {\r\n          console.log('getUserProfile成功:', res);\r\n          const avatarUrl = res.userInfo.avatarUrl\r\n\r\n          if (avatarUrl && avatarUrl !== '') {\r\n            this.userInfo.avatar = avatarUrl\r\n            this.downloadAndUploadWechatAvatar(avatarUrl)\r\n          } else {\r\n            this.tryChooseAvatarOnDevice()\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.log('getUserProfile失败，尝试其他方法:', err);\r\n          this.tryChooseAvatarOnDevice()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 在真机上尝试使用 chooseAvatar\r\n    tryChooseAvatarOnDevice() {\r\n      console.log('真机环境 - 尝试使用chooseAvatar');\r\n\r\n      // 创建一个临时的 chooseAvatar 按钮\r\n      // 注意：这里我们不直接使用模板中的按钮，而是通过编程方式触发\r\n      uni.showModal({\r\n        title: '获取微信头像',\r\n        content: '请点击确定后选择微信头像',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 这里可以触发隐藏的 chooseAvatar 按钮\r\n            // 或者降级到其他方案\r\n            this.fallbackToAlbum()\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n\r\n\r\n    // 降级到相册选择\r\n    fallbackToAlbum() {\r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '无法获取微信头像，是否从相册选择？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            this.chooseFromAlbum()\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 从相册选择\r\n    chooseFromAlbum() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        sizeType: ['compressed'],\r\n        sourceType: ['album'],\r\n        success: (res) => {\r\n          console.log('从相册选择成功:', res);\r\n          const tempFilePath = res.tempFilePaths[0]\r\n          this.userInfo.avatar = tempFilePath\r\n          this.uploadAvatar(tempFilePath)\r\n        },\r\n        fail: (err) => {\r\n          console.error('从相册选择失败:', err);\r\n          uni.showToast({ title: '选择图片失败', icon: 'none' })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 拍照\r\n    chooseFromCamera() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        sizeType: ['compressed'],\r\n        sourceType: ['camera'],\r\n        success: (res) => {\r\n          console.log('拍照成功:', res);\r\n          const tempFilePath = res.tempFilePaths[0]\r\n          this.userInfo.avatar = tempFilePath\r\n          this.uploadAvatar(tempFilePath)\r\n        },\r\n        fail: (err) => {\r\n          console.error('拍照失败:', err);\r\n          uni.showToast({ title: '拍照失败', icon: 'none' })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 下载并上传微信头像\r\n    downloadAndUploadWechatAvatar(avatarUrl) {\r\n      console.log('开始下载微信头像:', avatarUrl);\r\n      uni.showLoading({ title: '处理中...' })\r\n\r\n      // 下载微信头像到本地\r\n      uni.downloadFile({\r\n        url: avatarUrl,\r\n        success: (res) => {\r\n          console.log('下载微信头像成功:', res);\r\n          if (res.statusCode === 200) {\r\n            // 上传下载的头像文件\r\n            this.uploadAvatar(res.tempFilePath)\r\n          } else {\r\n            uni.hideLoading()\r\n            uni.showToast({ title: '下载头像失败', icon: 'none' })\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.error('下载微信头像失败:', err);\r\n          uni.hideLoading()\r\n          uni.showToast({ title: '下载头像失败', icon: 'none' })\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 输入昵称回调\r\n    onInputNickname(e) {\r\n      this.userInfo.nickname = e.detail.value\r\n    },\r\n    \r\n    // 上传头像\r\n    uploadAvatar(filePath) {\r\n      console.log('开始上传头像:', filePath);\r\n\r\n      // 检查文件路径是否有效\r\n      if (!filePath) {\r\n        uni.showToast({ title: '文件路径无效', icon: 'none' })\r\n        return\r\n      }\r\n\r\n      uni.showLoading({ title: '上传中...' })\r\n\r\n      // 使用正确的上传端点\r\n      const uploadUrl = this.baseUrl + '/api/system/file/';\r\n      console.log('上传URL:', uploadUrl);\r\n      console.log('Token:', getToken());\r\n\r\n      // 将头像上传到服务器\r\n      uni.uploadFile({\r\n        url: uploadUrl,\r\n        filePath: filePath,\r\n        name: 'file',\r\n        header: {\r\n          Authorization: 'JWT ' + getToken()  // 修复：使用JWT前缀\r\n        },\r\n        success: (uploadRes) => {\r\n          console.log('上传原始响应:', uploadRes);\r\n          try {\r\n            const data = JSON.parse(uploadRes.data)\r\n            console.log('上传解析后响应:', data);\r\n\r\n            if (data.code === 2000) {  // 修复：检查2000状态码\r\n              // 更新头像URL - 尝试多个可能的字段\r\n              const avatarUrl = data.data?.url || data.url || data.data?.file_url\r\n              console.log('获取到的头像URL:', avatarUrl);\r\n\r\n              if (avatarUrl) {\r\n                this.userInfo.avatar = avatarUrl\r\n\r\n                // 同时更新用户信息到后端\r\n                this.updateUserAvatar(avatarUrl)\r\n\r\n                uni.hideLoading()\r\n                uni.showToast({ title: '上传成功', icon: 'success' })\r\n              } else {\r\n                console.error('响应中没有找到头像URL');\r\n                uni.hideLoading()\r\n                uni.showToast({ title: '上传成功但获取URL失败', icon: 'none' })\r\n              }\r\n            } else {\r\n              console.error('上传失败，错误码:', data.code, '错误信息:', data.msg);\r\n              uni.hideLoading()\r\n              uni.showToast({ title: data.msg || '上传失败', icon: 'none' })\r\n            }\r\n          } catch (e) {\r\n            console.error('解析上传响应失败:', e, '原始响应:', uploadRes.data);\r\n            uni.hideLoading()\r\n            uni.showToast({ title: '上传失败', icon: 'none' })\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.error('上传请求失败:', err);\r\n          uni.hideLoading()\r\n          uni.showToast({ title: '上传失败，请重试', icon: 'none' })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 更新用户头像到后端\r\n    updateUserAvatar(avatarUrl) {\r\n      updateUserInfo({\r\n        avatar_url: avatarUrl\r\n      }).then(res => {\r\n        if (res.code === 2000) {\r\n          // 更新本地存储\r\n          const userInfo = uni.getStorageSync('userInfo') || {}\r\n          userInfo.avatarUrl = avatarUrl\r\n          uni.setStorageSync('userInfo', userInfo)\r\n\r\n          console.log('头像更新成功');\r\n        } else {\r\n          console.error('更新用户头像失败:', res.msg);\r\n        }\r\n      }).catch(err => {\r\n        console.error('更新用户头像接口调用失败:', err);\r\n      })\r\n    },\r\n    \r\n    // 保存个人资料\r\n    saveProfile() {\r\n      if (!this.userInfo.nickname) {\r\n        return uni.showToast({ title: '请输入昵称', icon: 'none' })\r\n      }\r\n      \r\n      uni.showLoading({ title: '保存中...' })\r\n      \r\n      const updateData = {\r\n        nickname: this.userInfo.nickname,\r\n        avatar: this.userInfo.avatar\r\n      }\r\n      \r\n      updateUserInfo(updateData).then(res => {\r\n        uni.hideLoading()\r\n        if (res.code === 200) {\r\n          // 更新本地存储\r\n          uni.setStorageSync('userInfo', {\r\n            nickName: this.userInfo.nickname,\r\n            avatarUrl: this.userInfo.avatar,\r\n            gender: this.userInfo.gender || 0\r\n          })\r\n          \r\n          uni.showToast({ title: '保存成功' })\r\n          \r\n          // 如果是首次登录，跳转到首页，否则返回上一页\r\n          if (this.isFirstLogin) {\r\n            this.$tab.reLaunch('/pages/index')\r\n          } else {\r\n            // 延迟返回\r\n            setTimeout(() => {\r\n              uni.navigateBack()\r\n            }, 1500)\r\n          }\r\n        } else {\r\n          uni.showToast({ title: res.msg || '保存失败', icon: 'none' })\r\n        }\r\n      }).catch(() => {\r\n        uni.hideLoading()\r\n        uni.showToast({ title: '保存失败', icon: 'none' })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.profile-container {\r\n  padding: 30rpx;\r\n  background-color: #ffffff;\r\n  min-height: 100vh;\r\n  \r\n  .avatar-section {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    margin-bottom: 50rpx;\r\n    padding-top: 50rpx;\r\n    \r\n    .avatar-button {\r\n      padding: 0;\r\n      background: none;\r\n      border: none;\r\n      width: 180rpx;\r\n      height: 180rpx;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n      position: relative;\r\n      transition: transform 0.2s;\r\n\r\n      &::after {\r\n        border: none;\r\n      }\r\n\r\n      &:active {\r\n        transform: scale(0.95);\r\n      }\r\n\r\n      .avatar {\r\n        width: 180rpx;\r\n        height: 180rpx;\r\n        border-radius: 50%;\r\n        border: 4rpx solid #f0f0f0;\r\n        transition: border-color 0.2s;\r\n      }\r\n\r\n      &:hover .avatar {\r\n        border-color: #4CAF50;\r\n      }\r\n    }\r\n    \r\n    .tip {\r\n      margin-top: 20rpx;\r\n      font-size: 28rpx;\r\n      color: #999;\r\n    }\r\n\r\n    .hidden-avatar-button {\r\n      position: fixed;\r\n      top: -200rpx;\r\n      left: -200rpx;\r\n      width: 1rpx;\r\n      height: 1rpx;\r\n      opacity: 0;\r\n      pointer-events: none;\r\n    }\r\n  }\r\n  \r\n  .form-section {\r\n    .form-item {\r\n      margin-bottom: 30rpx;\r\n      \r\n      .label {\r\n        display: block;\r\n        font-size: 30rpx;\r\n        color: #333;\r\n        margin-bottom: 20rpx;\r\n      }\r\n      \r\n      input {\r\n        width: 100%;\r\n        height: 90rpx;\r\n        border: 1px solid #eee;\r\n        border-radius: 8rpx;\r\n        padding: 0 20rpx;\r\n        font-size: 30rpx;\r\n      }\r\n    }\r\n    \r\n    .save-btn {\r\n      margin-top: 50rpx;\r\n      height: 90rpx;\r\n      line-height: 90rpx;\r\n      font-size: 32rpx;\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=b1b03f2e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=b1b03f2e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751682214011\n      var cssReload = require(\"D:/System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}