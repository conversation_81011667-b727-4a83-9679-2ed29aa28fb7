{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?eb33", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?3395", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?373d", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?68fa", "uni-app:///pages/mine/profile/index.vue", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?d4e5", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?4cc3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "avatar", "nickname", "defaultAvatar", "is<PERSON>irstL<PERSON>in", "baseUrl", "onLoad", "methods", "onChooseAvatar", "onInputNickname", "uploadAvatar", "uni", "title", "console", "url", "filePath", "name", "header", "Authorization", "success", "icon", "fail", "saveProfile", "nick<PERSON><PERSON>", "avatarUrl", "gender", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACqB9uB;AACA;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA,gDACAN;MACAE;MACAD;IAAA,EACA;;IAEA;IACA;EACA;EACAM;IACA;IACAC;MACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACAC;QAAAC;MAAA;;MAEA;MACA;MACAC;;MAEA;MACAF;QACAG;QACAC;QACAC;QACAC;UACAC;QACA;QACAC;UACA;YACA;YACA;cACA;cACAR;cACAA;gBAAAC;gBAAAQ;cAAA;YACA;cACAT;cACAA;gBAAAC;gBAAAQ;cAAA;YACA;UACA;YACAT;YACAA;cAAAC;cAAAQ;YAAA;UACA;QACA;QACAC;UACAR;UACAF;UACAA;YAAAC;YAAAQ;UAAA;QACA;MACA;IACA;IAEA;IACAE;MAAA;MACA;QACA;UAAAV;UAAAQ;QAAA;MACA;MAEAT;QAAAC;MAAA;MAEA;QACAV;QACAD;MACA;MAEA;QACAU;QACA;UACA;UACAA;YACAY;YACAC;YACAC;UACA;UAEAd;YAAAC;UAAA;;UAEA;UACA;YACA;UACA;YACA;YACAc;cACAf;YACA;UACA;QACA;UACAA;YAAAC;YAAAQ;UAAA;QACA;MACA;QACAT;QACAA;UAAAC;UAAAQ;QAAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnJA;AAAA;AAAA;AAAA;AAA63C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/profile/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/profile/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=b1b03f2e&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=b1b03f2e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b1b03f2e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/profile/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=b1b03f2e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"profile-container\">\r\n    <view class=\"avatar-section\">\r\n      <button open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\" class=\"avatar-button\">\r\n        <image :src=\"userInfo.avatar || defaultAvatar\" class=\"avatar\"></image>\r\n      </button>\r\n      <text class=\"tip\">点击更换头像</text>\r\n    </view>\r\n    \r\n    <view class=\"form-section\">\r\n      <view class=\"form-item\">\r\n        <text class=\"label\">昵称</text>\r\n        <input type=\"nickname\" @change=\"onInputNickname\" :value=\"userInfo.nickname\" placeholder=\"请输入您的昵称\" />\r\n      </view>\r\n      \r\n      <button @click=\"saveProfile\" class=\"save-btn cu-btn block bg-green lg\">保存</button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { updateUserInfo } from '@/api/auth'\r\nimport { getToken } from '@/utils/auth'\r\nimport config from '@/config'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userInfo: {\r\n        avatar: '',\r\n        nickname: ''\r\n      },\r\n      defaultAvatar: '/static/images/default-avatar.png',\r\n      isFirstLogin: false,\r\n      baseUrl: config.baseUrl || 'http://localhost:8080'\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    // 获取当前用户信息\r\n    const userInfo = uni.getStorageSync('userInfo') || {}\r\n    this.userInfo = {\r\n      ...userInfo,\r\n      nickname: userInfo.nickName || '',\r\n      avatar: userInfo.avatarUrl || ''\r\n    }\r\n    \r\n    // 检查是否首次登录\r\n    this.isFirstLogin = options.first === '1'\r\n  },\r\n  methods: {\r\n    // 选择头像回调\r\n    onChooseAvatar(e) {\r\n      const { avatarUrl } = e.detail\r\n      this.userInfo.avatar = avatarUrl\r\n      \r\n      // 上传头像到服务器\r\n      this.uploadAvatar(avatarUrl)\r\n    },\r\n    \r\n    // 输入昵称回调\r\n    onInputNickname(e) {\r\n      this.userInfo.nickname = e.detail.value\r\n    },\r\n    \r\n    // 上传头像\r\n    uploadAvatar(filePath) {\r\n      uni.showLoading({ title: '上传中...' })\r\n      \r\n      // 使用配置的baseUrl\r\n      const uploadUrl = this.baseUrl + '/api/common/upload';\r\n      console.log('上传URL:', uploadUrl);\r\n      \r\n      // 将头像上传到临时文件\r\n      uni.uploadFile({\r\n        url: uploadUrl,\r\n        filePath: filePath,\r\n        name: 'file',\r\n        header: {\r\n          Authorization: 'Bearer ' + getToken()\r\n        },\r\n        success: (uploadRes) => {\r\n          try {\r\n            const data = JSON.parse(uploadRes.data)\r\n            if (data.code === 200) {\r\n              this.userInfo.avatar = data.url\r\n              uni.hideLoading()\r\n              uni.showToast({ title: '上传成功', icon: 'success' })\r\n            } else {\r\n              uni.hideLoading()\r\n              uni.showToast({ title: data.msg || '上传失败', icon: 'none' })\r\n            }\r\n          } catch (e) {\r\n            uni.hideLoading()\r\n            uni.showToast({ title: '上传失败', icon: 'none' })\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.error('上传失败:', err);\r\n          uni.hideLoading()\r\n          uni.showToast({ title: '上传失败', icon: 'none' })\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 保存个人资料\r\n    saveProfile() {\r\n      if (!this.userInfo.nickname) {\r\n        return uni.showToast({ title: '请输入昵称', icon: 'none' })\r\n      }\r\n      \r\n      uni.showLoading({ title: '保存中...' })\r\n      \r\n      const updateData = {\r\n        nickname: this.userInfo.nickname,\r\n        avatar: this.userInfo.avatar\r\n      }\r\n      \r\n      updateUserInfo(updateData).then(res => {\r\n        uni.hideLoading()\r\n        if (res.code === 200) {\r\n          // 更新本地存储\r\n          uni.setStorageSync('userInfo', {\r\n            nickName: this.userInfo.nickname,\r\n            avatarUrl: this.userInfo.avatar,\r\n            gender: this.userInfo.gender || 0\r\n          })\r\n          \r\n          uni.showToast({ title: '保存成功' })\r\n          \r\n          // 如果是首次登录，跳转到首页，否则返回上一页\r\n          if (this.isFirstLogin) {\r\n            this.$tab.reLaunch('/pages/index')\r\n          } else {\r\n            // 延迟返回\r\n            setTimeout(() => {\r\n              uni.navigateBack()\r\n            }, 1500)\r\n          }\r\n        } else {\r\n          uni.showToast({ title: res.msg || '保存失败', icon: 'none' })\r\n        }\r\n      }).catch(() => {\r\n        uni.hideLoading()\r\n        uni.showToast({ title: '保存失败', icon: 'none' })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.profile-container {\r\n  padding: 30rpx;\r\n  background-color: #ffffff;\r\n  min-height: 100vh;\r\n  \r\n  .avatar-section {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    margin-bottom: 50rpx;\r\n    padding-top: 50rpx;\r\n    \r\n    .avatar-button {\r\n      padding: 0;\r\n      background: none;\r\n      border: none;\r\n      width: 180rpx;\r\n      height: 180rpx;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n      \r\n      &::after {\r\n        border: none;\r\n      }\r\n      \r\n      .avatar {\r\n        width: 180rpx;\r\n        height: 180rpx;\r\n        border-radius: 50%;\r\n      }\r\n    }\r\n    \r\n    .tip {\r\n      margin-top: 20rpx;\r\n      font-size: 28rpx;\r\n      color: #999;\r\n    }\r\n  }\r\n  \r\n  .form-section {\r\n    .form-item {\r\n      margin-bottom: 30rpx;\r\n      \r\n      .label {\r\n        display: block;\r\n        font-size: 30rpx;\r\n        color: #333;\r\n        margin-bottom: 20rpx;\r\n      }\r\n      \r\n      input {\r\n        width: 100%;\r\n        height: 90rpx;\r\n        border: 1px solid #eee;\r\n        border-radius: 8rpx;\r\n        padding: 0 20rpx;\r\n        font-size: 30rpx;\r\n      }\r\n    }\r\n    \r\n    .save-btn {\r\n      margin-top: 50rpx;\r\n      height: 90rpx;\r\n      line-height: 90rpx;\r\n      font-size: 32rpx;\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=b1b03f2e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=b1b03f2e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751643652886\n      var cssReload = require(\"D:/System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}