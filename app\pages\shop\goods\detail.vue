<template>
  <view class="goods-detail-container">
    <!-- 商品图片轮播 -->
    <swiper class="goods-swiper" indicator-dots circular>
      <swiper-item v-for="(image, index) in goodsImages" :key="index">
        <image class="goods-image" :src="getFullImageUrl(image)" mode="aspectFill" @click="previewImage(index)"></image>
      </swiper-item>
    </swiper>

    <!-- 商品基本信息 -->
    <view class="goods-info-section">
      <view class="price-row">
        <text class="current-price">¥{{ formatPrice(goodsInfo.price || goodsInfo.sellPrice) }}</text>
        <text class="original-price" v-if="(goodsInfo.originalPrice || goodsInfo.marketPrice) > (goodsInfo.price || goodsInfo.sellPrice)">¥{{ formatPrice(goodsInfo.originalPrice || goodsInfo.marketPrice) }}</text>
        <view class="discount-tag" v-if="discount < 10">{{ discount }}折</view>
      </view>
      <text class="goods-name">{{ goodsInfo.goodsName }}</text>
      <text class="goods-desc">{{ goodsInfo.goodsDesc }}</text>
      
      <view class="goods-meta">
        <view class="meta-item">
          <text class="label">销量</text>
          <text class="value">{{ goodsInfo.sales || goodsInfo.salesCount || 0 }}</text>
        </view>
        <view class="meta-item">
          <text class="label">库存</text>
          <text class="value">{{ goodsInfo.stock || goodsInfo.stockCount || 0 }}</text>
        </view>
        <view class="meta-item" v-if="goodsInfo.rating">
          <text class="label">评分</text>
          <text class="value">{{ goodsInfo.rating }}分</text>
        </view>
      </view>
    </view>

    <!-- 规格选择 -->
    <view class="spec-section" v-if="goodsSpecs.length > 0" @click="showSpecPopup = true">
      <view class="section-title">
        <text>选择规格</text>
        <uni-icons type="arrowright" size="14" color="#999"></uni-icons>
      </view>
      <view class="selected-spec">
        <text v-if="selectedSku.skuName">{{ selectedSku.skuName }}</text>
        <text v-else class="placeholder">请选择商品规格</text>
      </view>
    </view>

    <!-- 商品详情 -->
    <view class="detail-section">
      <view class="section-title">商品详情</view>
      <view class="detail-content">
        <rich-text :nodes="goodsInfo.goodsDetail"></rich-text>
      </view>
    </view>

    <!-- 评价列表 -->
    <view class="review-section" v-if="reviews.length > 0">
      <view class="section-title">
        <text>用户评价</text>
        <text class="more-btn" @click="goToReviews">查看全部 ></text>
      </view>
      <view class="review-list">
        <view class="review-item" v-for="review in reviews.slice(0, 3)" :key="review.reviewId">
          <view class="review-header">
            <image class="user-avatar" :src="getFullImageUrl(review.userAvatar)" mode="aspectFill"></image>
            <view class="user-info">
              <text class="username">{{ review.userNickname }}</text>
              <uni-rate :value="review.rating" readonly size="12"></uni-rate>
            </view>
            <text class="review-time">{{ formatTime(review.createTime) }}</text>
          </view>
          <text class="review-content">{{ review.content }}</text>
          <view class="review-images" v-if="review.images && review.images.length > 0">
            <image 
              class="review-image" 
              v-for="(img, idx) in review.images" 
              :key="idx"
              :src="img" 
              mode="aspectFill"
              @click="previewReviewImage(review.images, idx)"
            ></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-left">
        <view class="action-btn" @click="toggleFavorite">
          <uni-icons :type="isFavorite ? 'heart-filled' : 'heart'" size="20" :color="isFavorite ? '#ff4757' : '#666'"></uni-icons>
          <text>{{ isFavorite ? '已收藏' : '收藏' }}</text>
        </view>
        <view class="action-btn" @click="goToCart">
          <uni-icons type="cart" size="20" color="#666"></uni-icons>
          <text>购物车</text>
          <view class="cart-badge" v-if="cartCount > 0">{{ cartCount > 99 ? '99+' : cartCount }}</view>
        </view>
      </view>
      <view class="action-right">
        <button class="add-cart-btn" @click="addToCart">加入购物车</button>
        <button class="buy-now-btn" @click="buyNow">立即购买</button>
      </view>
    </view>

    <!-- 规格选择弹窗 -->
    <uni-popup ref="specPopup" type="bottom" :mask-click="false">
      <view class="spec-popup">
        <view class="popup-header">
          <text class="popup-title">选择规格</text>
          <uni-icons type="close" size="20" color="#666" @click="closeSpecPopup"></uni-icons>
        </view>
        
        <view class="popup-goods-info">
          <image class="popup-goods-image" :src="getFullImageUrl(selectedSku.picUrl || goodsInfo.coverImg || goodsInfo.mainPic)" mode="aspectFill"></image>
          <view class="popup-goods-detail">
            <text class="popup-price">¥{{ formatPrice(selectedSku.price || selectedSku.sellPrice || goodsInfo.price || goodsInfo.sellPrice) }}</text>
            <text class="popup-stock">库存{{ selectedSku.stock || selectedSku.stockCount || goodsInfo.stock || goodsInfo.stockCount }}件</text>
            <text class="popup-spec" v-if="selectedSku.skuName">{{ selectedSku.skuName }}</text>
          </view>
        </view>

        <view class="spec-options" v-for="spec in goodsSpecs" :key="spec.specId">
          <text class="spec-name">{{ spec.specName }}</text>
          <view class="spec-values">
            <view 
              class="spec-value" 
              :class="{ active: selectedSpecs[spec.specId] === value.valueId }"
              v-for="value in spec.values" 
              :key="value.valueId"
              @click="selectSpec(spec.specId, value.valueId)"
            >
              <text>{{ value.valueName }}</text>
            </view>
          </view>
        </view>

        <view class="quantity-section">
          <text class="quantity-label">数量</text>
          <uni-number-box 
            v-model="quantity" 
            :min="1" 
            :max="selectedSku.stockCount || goodsInfo.stockCount"
          ></uni-number-box>
        </view>

        <view class="popup-actions">
          <button class="popup-add-cart" @click="confirmAddToCart">加入购物车</button>
          <button class="popup-buy-now" @click="confirmBuyNow">立即购买</button>
        </view>
      </view>
    </uni-popup>

    <!-- 加载状态 -->
    <uni-load-more :status="loadStatus" v-if="loading"></uni-load-more>
  </view>
</template>

<script>
import { getGoodsDetail, getGoodsSkus, getGoodsReviews, favoriteGoods, unfavoriteGoods, addToCart, getCartCount } from '@/api/shop.js'
import { formatPrice, calculateDiscount } from '@/utils/shop.js'
import config from '@/config.js'

export default {
  data() {
    return {
      goodsId: '',
      goodsInfo: {},
      goodsImages: [],
      goodsSpecs: [],
      goodsSkus: [],
      reviews: [],
      
      // 选择的规格和SKU
      selectedSpecs: {},
      selectedSku: {},
      quantity: 1,
      
      // 状态
      loading: false,
      loadStatus: 'loading',
      isFavorite: false,
      cartCount: 0,
      showSpecPopup: false
    }
  },
  computed: {
    // 计算折扣
    discount() {
      const originalPrice = this.goodsInfo.originalPrice || this.goodsInfo.marketPrice
      const currentPrice = this.goodsInfo.price || this.goodsInfo.sellPrice
      return calculateDiscount(originalPrice, currentPrice)
    }
  },
  onLoad(options) {
    this.goodsId = options.goodsId
    if (this.goodsId) {
      this.loadGoodsDetail()
    }
  },
  onShow() {
    this.updateCartCount()
  },
  methods: {
    // 加载商品详情
    async loadGoodsDetail() {
      this.loading = true
      try {
        await Promise.all([
          this.loadBasicInfo(),
          this.loadGoodsSpecs(),
          this.loadReviews()
        ])
      } catch (error) {
        console.error('加载商品详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 加载基本信息
    async loadBasicInfo() {
      const res = await getGoodsDetail(this.goodsId)
      // 修复：从res.data.goods获取商品信息
      this.goodsInfo = res.data?.goods || res.data || {}

      // 调试：打印商品详情数据
      console.log('=== 商品详情数据调试 ===')
      console.log('完整响应:', res)
      console.log('res.data:', res.data)
      console.log('res.data.goods:', res.data?.goods)
      console.log('最终商品信息:', this.goodsInfo)
      console.log('图片字段检查:')
      console.log('- coverImg:', this.goodsInfo.coverImg)
      console.log('- mainPic:', this.goodsInfo.mainPic)
      console.log('- images:', this.goodsInfo.images)
      console.log('- gallery:', this.goodsInfo.gallery)
      console.log('========================')

      // 处理商品图片
      if (this.goodsInfo.gallery) {
        this.goodsImages = this.goodsInfo.gallery.split(',').filter(img => img.trim())
      } else if (this.goodsInfo.images) {
        this.goodsImages = this.goodsInfo.images.split(',').filter(img => img.trim())
      } else if (this.goodsInfo.coverImg) {
        this.goodsImages = [this.goodsInfo.coverImg]
      } else if (this.goodsInfo.mainPic) {
        this.goodsImages = [this.goodsInfo.mainPic]
      } else {
        // 如果没有图片，使用默认图片
        this.goodsImages = ['/static/images/goods/default.svg']
      }

      console.log('处理后的图片数组:', this.goodsImages)
    },

    // 加载商品规格
    async loadGoodsSpecs() {
      try {
        const res = await getGoodsSkus(this.goodsId)
        const skuData = res.data || {}
        this.goodsSpecs = skuData.specs || []
        this.goodsSkus = skuData.skus || []
      } catch (error) {
        console.error('加载商品规格失败:', error)
      }
    },

    // 加载评价
    async loadReviews() {
      try {
        const res = await getGoodsReviews(this.goodsId, { pageSize: 5 })
        this.reviews = res.rows || []
      } catch (error) {
        console.error('加载评价失败:', error)
      }
    },

    // 更新购物车数量
    async updateCartCount() {
      try {
        const res = await getCartCount()
        this.cartCount = res.data?.count || 0
      } catch (error) {
        console.error('获取购物车数量失败:', error)
        this.cartCount = 0
      }
    },

    // 选择规格
    selectSpec(specId, valueId) {
      this.selectedSpecs[specId] = valueId
      this.findMatchingSku()
    },

    // 查找匹配的SKU
    findMatchingSku() {
      const selectedValues = Object.values(this.selectedSpecs)
      const matchingSku = this.goodsSkus.find(sku => {
        const skuSpecs = sku.specValues || []
        return selectedValues.every(valueId => 
          skuSpecs.some(spec => spec.valueId === valueId)
        )
      })
      
      if (matchingSku) {
        this.selectedSku = matchingSku
      } else {
        this.selectedSku = {}
      }
    },

    // 图片预览
    previewImage(index) {
      uni.previewImage({
        urls: this.goodsImages,
        current: index
      })
    },

    // 预览评价图片
    previewReviewImage(images, index) {
      uni.previewImage({
        urls: images,
        current: index
      })
    },

    // 收藏/取消收藏
    async toggleFavorite() {
      try {
        if (this.isFavorite) {
          await unfavoriteGoods(this.goodsId)
          this.isFavorite = false
          uni.showToast({ title: '已取消收藏', icon: 'none' })
        } else {
          await favoriteGoods(this.goodsId)
          this.isFavorite = true
          uni.showToast({ title: '收藏成功', icon: 'none' })
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        uni.showToast({ title: '操作失败', icon: 'none' })
      }
    },

    // 跳转到购物车
    goToCart() {
      uni.switchTab({
        url: '/pages/shop/cart/index'
      })
    },

    // 跳转到评价页面
    goToReviews() {
      uni.navigateTo({
        url: `/pages/shop/goods/reviews?goodsId=${this.goodsId}`
      })
    },

    // 添加到购物车
    addToCart() {
      if (this.goodsSpecs.length > 0 && !this.selectedSku.skuId) {
        this.showSpecPopup = true
        return
      }
      this.confirmAddToCart()
    },

    // 确认添加到购物车
    async confirmAddToCart() {
      const cartData = {
        goodsId: this.goodsId,
        skuId: this.selectedSku.skuId || null,
        quantity: this.quantity
      }

      try {
        await addToCart(cartData)
        uni.showToast({ title: '已加入购物车', icon: 'success' })
        this.updateCartCount()
        this.closeSpecPopup()
      } catch (error) {
        console.error('添加购物车失败:', error)
        uni.showToast({ title: '添加失败', icon: 'none' })
      }
    },

    // 立即购买
    buyNow() {
      if (this.goodsSpecs.length > 0 && !this.selectedSku.skuId) {
        this.showSpecPopup = true
        return
      }
      this.confirmBuyNow()
    },

    // 确认立即购买
    confirmBuyNow() {
      // 跳转到订单确认页面
      const orderData = {
        goodsId: this.goodsId,
        skuId: this.selectedSku.skuId || null,
        quantity: this.quantity
      }
      
      uni.navigateTo({
        url: `/pages/shop/order/confirm?data=${encodeURIComponent(JSON.stringify(orderData))}`
      })
    },

    // 关闭规格弹窗
    closeSpecPopup() {
      this.showSpecPopup = false
      this.$refs.specPopup.close()
    },

    // 格式化价格
    formatPrice,

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return `${date.getMonth() + 1}-${date.getDate()}`
    },

    // 获取完整的图片URL
    getFullImageUrl(imageUrl) {
      if (!imageUrl) return '/static/images/goods/default.svg'
      if (imageUrl.startsWith('http')) return imageUrl
      return config.baseUrl + imageUrl
    }
  },
  watch: {
    showSpecPopup(val) {
      if (val) {
        this.$refs.specPopup.open()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.goods-detail-container {
  background-color: #f5f5f7;
  padding-bottom: 120rpx;
}

.goods-swiper {
  height: 750rpx;

  .goods-image {
    width: 100%;
    height: 100%;
  }
}

.goods-info-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .price-row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .current-price {
      font-size: 48rpx;
      color: #ff6700;
      font-weight: bold;
    }

    .original-price {
      font-size: 28rpx;
      color: #999;
      text-decoration: line-through;
      margin-left: 20rpx;
    }

    .discount-tag {
      background-color: #ff6700;
      color: #fff;
      font-size: 20rpx;
      padding: 4rpx 8rpx;
      border-radius: 4rpx;
      margin-left: 20rpx;
    }
  }

  .goods-name {
    font-size: 36rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 15rpx;
    display: block;
  }

  .goods-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
    margin-bottom: 30rpx;
    display: block;
  }

  .goods-meta {
    display: flex;

    .meta-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;

      .label {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 8rpx;
      }

      .value {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
      }
    }
  }
}

.spec-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .section-title {
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
    display: flex;
    align-items: center;
  }

  .selected-spec {
    font-size: 28rpx;
    color: #666;

    .placeholder {
      color: #999;
    }
  }
}

.detail-section, .review-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .section-title {
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .more-btn {
      font-size: 26rpx;
      color: #007aff;
      font-weight: normal;
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  z-index: 999;

  .action-left {
    display: flex;
    margin-right: 30rpx;

    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 40rpx;
      position: relative;

      text {
        font-size: 20rpx;
        color: #666;
        margin-top: 8rpx;
      }

      .cart-badge {
        position: absolute;
        top: -8rpx;
        right: -8rpx;
        background-color: #ff4757;
        color: #fff;
        font-size: 18rpx;
        min-width: 28rpx;
        height: 28rpx;
        border-radius: 14rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 6rpx;
      }
    }
  }

  .action-right {
    flex: 1;
    display: flex;
    gap: 20rpx;

    button {
      flex: 1;
      height: 80rpx;
      border-radius: 40rpx;
      font-size: 28rpx;
      border: none;
    }

    .add-cart-btn {
      background-color: #ffa500;
      color: #fff;
    }

    .buy-now-btn {
      background-color: #ff6700;
      color: #fff;
    }
  }
}
</style>

<style lang="scss" scoped>
.goods-detail-container {
  background-color: #f5f5f7;
  padding-bottom: 120rpx;
}

.goods-swiper {
  height: 750rpx;

  .goods-image {
    width: 100%;
    height: 100%;
  }
}

.goods-info-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .price-row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .current-price {
      font-size: 48rpx;
      color: #ff6700;
      font-weight: bold;
    }

    .original-price {
      font-size: 28rpx;
      color: #999;
      text-decoration: line-through;
      margin-left: 20rpx;
    }

    .discount-tag {
      background-color: #ff6700;
      color: #fff;
      font-size: 20rpx;
      padding: 4rpx 8rpx;
      border-radius: 4rpx;
      margin-left: 20rpx;
    }
  }

  .goods-name {
    font-size: 36rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 15rpx;
    display: block;
  }

  .goods-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
    margin-bottom: 30rpx;
    display: block;
  }

  .goods-meta {
    display: flex;

    .meta-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;

      .label {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 8rpx;
      }

      .value {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
      }
    }
  }
}

.spec-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .section-title {
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
    display: flex;
    align-items: center;
  }

  .selected-spec {
    font-size: 28rpx;
    color: #666;

    .placeholder {
      color: #999;
    }
  }
}

.detail-section, .review-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .section-title {
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .more-btn {
      font-size: 26rpx;
      color: #007aff;
      font-weight: normal;
    }
  }

  .detail-content {
    line-height: 1.6;
  }
}

.review-list {
  .review-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 30rpx;
    margin-bottom: 30rpx;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    .review-header {
      display: flex;
      align-items: center;
      margin-bottom: 15rpx;

      .user-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .user-info {
        flex: 1;

        .username {
          font-size: 28rpx;
          color: #333;
          display: block;
          margin-bottom: 8rpx;
        }
      }

      .review-time {
        font-size: 24rpx;
        color: #999;
      }
    }

    .review-content {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
      margin-bottom: 15rpx;
      display: block;
    }

    .review-images {
      display: flex;
      gap: 10rpx;

      .review-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 8rpx;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  z-index: 999;

  .action-left {
    display: flex;
    margin-right: 30rpx;

    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 40rpx;
      position: relative;

      text {
        font-size: 20rpx;
        color: #666;
        margin-top: 8rpx;
      }

      .cart-badge {
        position: absolute;
        top: -8rpx;
        right: -8rpx;
        background-color: #ff4757;
        color: #fff;
        font-size: 18rpx;
        min-width: 28rpx;
        height: 28rpx;
        border-radius: 14rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 6rpx;
      }
    }
  }

  .action-right {
    flex: 1;
    display: flex;
    gap: 20rpx;

    button {
      flex: 1;
      height: 80rpx;
      border-radius: 40rpx;
      font-size: 28rpx;
      border: none;
    }

    .add-cart-btn {
      background-color: #ffa500;
      color: #fff;
    }

    .buy-now-btn {
      background-color: #ff6700;
      color: #fff;
    }
  }
}

.spec-popup {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 30rpx;
  max-height: 80vh;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;

    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .popup-goods-info {
    display: flex;
    margin-bottom: 40rpx;

    .popup-goods-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 8rpx;
      margin-right: 20rpx;
    }

    .popup-goods-detail {
      flex: 1;

      .popup-price {
        font-size: 36rpx;
        color: #ff6700;
        font-weight: bold;
        display: block;
        margin-bottom: 10rpx;
      }

      .popup-stock {
        font-size: 24rpx;
        color: #999;
        display: block;
        margin-bottom: 10rpx;
      }

      .popup-spec {
        font-size: 26rpx;
        color: #666;
        display: block;
      }
    }
  }

  .spec-options {
    margin-bottom: 40rpx;

    .spec-name {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
      display: block;
      margin-bottom: 20rpx;
    }

    .spec-values {
      display: flex;
      flex-wrap: wrap;
      gap: 20rpx;

      .spec-value {
        padding: 15rpx 30rpx;
        border: 1px solid #ddd;
        border-radius: 8rpx;
        font-size: 26rpx;
        color: #666;

        &.active {
          border-color: #007aff;
          color: #007aff;
          background-color: #f0f8ff;
        }
      }
    }
  }

  .quantity-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40rpx;

    .quantity-label {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
    }
  }

  .popup-actions {
    display: flex;
    gap: 20rpx;

    button {
      flex: 1;
      height: 80rpx;
      border-radius: 40rpx;
      font-size: 28rpx;
      border: none;
    }

    .popup-add-cart {
      background-color: #ffa500;
      color: #fff;
    }

    .popup-buy-now {
      background-color: #ff6700;
      color: #fff;
    }
  }
}
</style>
