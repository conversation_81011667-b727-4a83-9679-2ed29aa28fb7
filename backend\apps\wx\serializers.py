from rest_framework import serializers
from dvadmin.utils.serializers import CustomModelSerializer
from dvadmin.system.models import Users
from .models import WxUser, WxLoginLog


class WxLoginSerializer(serializers.Serializer):
    """微信登录序列化器"""
    
    code = serializers.CharField(
        max_length=100,
        required=True,
        help_text='微信登录凭证code'
    )
    
    userInfo = serializers.DictField(
        required=False,
        help_text='用户信息（可选）'
    )
    
    def validate_code(self, value):
        """验证code"""
        if not value:
            raise serializers.ValidationError("登录凭证不能为空")
        return value


class WxUserSerializer(CustomModelSerializer):
    """微信用户序列化器"""
    
    username = serializers.CharField(source='user.username', read_only=True)
    name = serializers.Char<PERSON>ield(source='user.name', read_only=True)
    email = serializers.Char<PERSON>ield(source='user.email', read_only=True)
    mobile = serializers.Char<PERSON>ield(source='user.mobile', read_only=True)
    
    class Meta:
        model = WxUser
        fields = [
            'id', 'openid', 'nickname', 'avatar_url', 'gender',
            'country', 'province', 'city', 'is_active', 'last_login_time',
            'username', 'name', 'email', 'mobile', 'create_datetime'
        ]
        read_only_fields = ['id', 'openid', 'create_datetime']


class WxUserUpdateSerializer(serializers.ModelSerializer):
    """微信用户信息更新序列化器"""
    
    class Meta:
        model = WxUser
        fields = ['nickname', 'avatar_url', 'gender', 'country', 'province', 'city']


class WxLoginLogSerializer(CustomModelSerializer):
    """微信登录日志序列化器"""
    
    wx_user_nickname = serializers.CharField(source='wx_user.nickname', read_only=True)
    wx_user_openid = serializers.CharField(source='wx_user.openid', read_only=True)
    
    class Meta:
        model = WxLoginLog
        fields = [
            'id', 'wx_user', 'wx_user_nickname', 'wx_user_openid',
            'login_time', 'ip_address', 'user_agent', 'login_result', 'error_message'
        ]
        read_only_fields = ['id', 'login_time']
