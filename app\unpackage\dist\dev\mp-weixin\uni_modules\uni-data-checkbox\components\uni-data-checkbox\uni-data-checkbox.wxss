@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-data-loading {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 36px;
  padding-left: 10px;
  color: #999;
}
.uni-data-checklist {
  position: relative;
  z-index: 0;
  flex: 1;
}
.uni-data-checklist .checklist-group {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.uni-data-checklist .checklist-group.is-list {
  flex-direction: column;
}
.uni-data-checklist .checklist-group .checklist-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  margin: 5px 0;
  margin-right: 25px;
}
.uni-data-checklist .checklist-group .checklist-box .hidden {
  position: absolute;
  opacity: 0;
}
.uni-data-checklist .checklist-group .checklist-box .checklist-content {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.uni-data-checklist .checklist-group .checklist-box .checklist-content .checklist-text {
  font-size: 14px;
  color: #666;
  margin-left: 5px;
  line-height: 14px;
}
.uni-data-checklist .checklist-group .checklist-box .checklist-content .checkobx__list {
  border-right-width: 1px;
  border-right-color: #007aff;
  border-right-style: solid;
  border-bottom-width: 1px;
  border-bottom-color: #007aff;
  border-bottom-style: solid;
  height: 12px;
  width: 6px;
  left: -5px;
  -webkit-transform-origin: center;
          transform-origin: center;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  opacity: 0;
}
.uni-data-checklist .checklist-group .checklist-box .checkbox__inner {
  flex-shrink: 0;
  box-sizing: border-box;
  position: relative;
  width: 16px;
  height: 16px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  background-color: #fff;
  z-index: 1;
}
.uni-data-checklist .checklist-group .checklist-box .checkbox__inner .checkbox__inner-icon {
  position: absolute;
  top: 1px;
  left: 5px;
  height: 8px;
  width: 4px;
  border-right-width: 1px;
  border-right-color: #fff;
  border-right-style: solid;
  border-bottom-width: 1px;
  border-bottom-color: #fff;
  border-bottom-style: solid;
  opacity: 0;
  -webkit-transform-origin: center;
          transform-origin: center;
  -webkit-transform: rotate(40deg);
          transform: rotate(40deg);
}
.uni-data-checklist .checklist-group .checklist-box .radio__inner {
  display: flex;
  flex-shrink: 0;
  box-sizing: border-box;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 16px;
  height: 16px;
  border: 1px solid #DCDFE6;
  border-radius: 16px;
  background-color: #fff;
  z-index: 1;
}
.uni-data-checklist .checklist-group .checklist-box .radio__inner .radio__inner-icon {
  width: 8px;
  height: 8px;
  border-radius: 10px;
  opacity: 0;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-disable .checkbox__inner {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-disable .radio__inner {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-disable .checklist-text {
  color: #999;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .checkbox__inner {
  border-color: #2979ff;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .checkbox__inner .checkbox__inner-icon {
  opacity: 1;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .radio__inner {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .radio__inner .radio__inner-icon {
  opacity: 1;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .checklist-text {
  color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked.is-disable .checkbox__inner {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked.is-disable .checklist-text {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked.is-disable .radio__inner {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--button {
  margin-right: 10px;
  padding: 5px 10px;
  border: 1px #DCDFE6 solid;
  border-radius: 3px;
  transition: border-color 0.2s;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable {
  border: 1px #eee solid;
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable .checkbox__inner {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable .radio__inner {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable .checklist-text {
  color: #999;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .checkbox__inner {
  border-color: #2979ff;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .checkbox__inner .checkbox__inner-icon {
  opacity: 1;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .radio__inner {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .radio__inner .radio__inner-icon {
  opacity: 1;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .checklist-text {
  color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked.is-disable {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag {
  margin-right: 10px;
  padding: 5px 10px;
  border: 1px #DCDFE6 solid;
  border-radius: 3px;
  background-color: #f5f5f5;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag .checklist-text {
  margin: 0;
  color: #666;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag.is-disable {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag.is-checked {
  background-color: #2979ff;
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag.is-checked .checklist-text {
  color: #fff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list {
  display: flex;
  padding: 10px 15px;
  padding-left: 0;
  margin: 0;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-list-border {
  border-top: 1px #eee solid;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-disable .checkbox__inner {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-disable .checklist-text {
  color: #999;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checkbox__inner {
  border-color: #2979ff;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checkbox__inner .checkbox__inner-icon {
  opacity: 1;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .radio__inner {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .radio__inner .radio__inner-icon {
  opacity: 1;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checklist-text {
  color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checklist-content .checkobx__list {
  opacity: 1;
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked.is-disable .checkbox__inner {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked.is-disable .checklist-text {
  opacity: 0.4;
}
