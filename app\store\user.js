/**
 * 用户状态管理
 */
import { defineStore } from 'pinia'
import api from '@/api/index.js'

export const useUserStore = defineStore('user', {
	state: () => ({
		// 用户信息
		userInfo: null,
		// 登录状态
		isLoggedIn: false,
		// 用户token
		token: null,
		// 用户权限
		permissions: [],
		// 用户设置
		settings: {
			theme: 'light',
			language: 'zh-CN',
			notifications: true
		}
	}),
	
	getters: {
		// 获取用户头像
		avatar: (state) => {
			return state.userInfo?.avatar_url || state.userInfo?.avatar || '/static/images/default-avatar.png'
		},
		
		// 获取用户昵称
		nickname: (state) => {
			return state.userInfo?.nickname || state.userInfo?.name || '植物爱好者'
		},
		
		// 获取用户等级
		level: (state) => {
			return state.userInfo?.level || 1
		},
		
		// 获取用户积分
		points: (state) => {
			return state.userInfo?.points || 0
		},
		
		// 是否是VIP用户
		isVip: (state) => {
			return state.userInfo?.level >= 5
		}
	},
	
	actions: {
		// 初始化用户状态
		initUserState() {
			try {
				const token = uni.getStorageSync('token')
				const userInfo = uni.getStorageSync('userInfo')
				const settings = uni.getStorageSync('userSettings')
				
				if (token) {
					this.token = token
					this.isLoggedIn = true
				}
				
				if (userInfo) {
					this.userInfo = userInfo
				}
				
				if (settings) {
					this.settings = { ...this.settings, ...settings }
				}
			} catch (error) {
				console.error('初始化用户状态失败:', error)
			}
		},
		
		// 微信登录
		async wechatLogin(loginData) {
			try {
				const response = await api.user.wechatLogin(loginData)
				
				// 保存用户信息和token
				this.setUserInfo(response.user)
				this.setToken(response.token)
				
				// 持久化存储
				uni.setStorageSync('token', response.token)
				uni.setStorageSync('userInfo', response.user)
				
				this.isLoggedIn = true
				
				return response
			} catch (error) {
				console.error('微信登录失败:', error)
				throw error
			}
		},
		
		// 手机号登录
		async phoneLogin(loginData) {
			try {
				const response = await api.user.phoneLogin(loginData)
				
				// 保存用户信息和token
				this.setUserInfo(response.user)
				this.setToken(response.token)
				
				// 持久化存储
				uni.setStorageSync('token', response.token)
				uni.setStorageSync('userInfo', response.user)
				
				this.isLoggedIn = true
				
				return response
			} catch (error) {
				console.error('手机号登录失败:', error)
				throw error
			}
		},
		
		// 获取用户信息
		async getUserInfo() {
			try {
				const userInfo = await api.user.getProfile()
				this.setUserInfo(userInfo)
				
				// 更新本地存储
				uni.setStorageSync('userInfo', userInfo)
				
				return userInfo
			} catch (error) {
				console.error('获取用户信息失败:', error)
				throw error
			}
		},
		
		// 更新用户信息
		async updateUserInfo(data) {
			try {
				const userInfo = await api.user.updateProfile(data)
				this.setUserInfo(userInfo)
				
				// 更新本地存储
				uni.setStorageSync('userInfo', userInfo)
				
				return userInfo
			} catch (error) {
				console.error('更新用户信息失败:', error)
				throw error
			}
		},
		
		// 退出登录
		async logout() {
			try {
				// 调用后端退出接口
				await api.user.logout()
			} catch (error) {
				console.error('退出登录接口调用失败:', error)
			} finally {
				// 清除本地状态和存储
				this.clearUserState()
			}
		},
		
		// 设置用户信息
		setUserInfo(userInfo) {
			this.userInfo = userInfo
		},
		
		// 设置token
		setToken(token) {
			this.token = token
		},
		
		// 设置用户设置
		setSettings(settings) {
			this.settings = { ...this.settings, ...settings }
			uni.setStorageSync('userSettings', this.settings)
		},
		
		// 清除用户状态
		clearUserState() {
			this.userInfo = null
			this.token = null
			this.isLoggedIn = false
			this.permissions = []
			
			// 清除本地存储
			uni.removeStorageSync('token')
			uni.removeStorageSync('userInfo')
			
			// 跳转到登录页
			uni.reLaunch({
				url: '/pages/login/login'
			})
		},
		
		// 检查登录状态
		checkLoginStatus() {
			const token = uni.getStorageSync('token')
			if (!token) {
				this.clearUserState()
				return false
			}
			return true
		},
		
		// 更新用户积分
		updatePoints(points) {
			if (this.userInfo) {
				this.userInfo.points = points
				uni.setStorageSync('userInfo', this.userInfo)
			}
		},
		
		// 更新用户等级
		updateLevel(level) {
			if (this.userInfo) {
				this.userInfo.level = level
				uni.setStorageSync('userInfo', this.userInfo)
			}
		}
	}
})
