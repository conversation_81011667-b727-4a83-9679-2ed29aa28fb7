from django.db import models
from dvadmin.utils.models import CoreModel
from dvadmin.system.models import Users


class WxUser(CoreModel):
    """微信小程序用户模型"""
    
    # 关联系统用户
    user = models.OneToOneField(
        Users, 
        on_delete=models.CASCADE, 
        related_name='wx_user',
        verbose_name='关联用户'
    )
    
    # 微信相关字段
    openid = models.CharField(
        max_length=100, 
        unique=True, 
        verbose_name='微信OpenID',
        help_text='微信用户唯一标识'
    )
    
    unionid = models.CharField(
        max_length=100, 
        null=True, 
        blank=True, 
        verbose_name='微信UnionID',
        help_text='微信开放平台唯一标识'
    )
    
    session_key = models.CharField(
        max_length=100, 
        null=True, 
        blank=True, 
        verbose_name='会话密钥',
        help_text='微信会话密钥'
    )
    
    # 用户信息
    nickname = models.CharField(
        max_length=100, 
        null=True, 
        blank=True, 
        verbose_name='微信昵称'
    )
    
    avatar_url = models.URLField(
        null=True, 
        blank=True, 
        verbose_name='头像URL'
    )
    
    GENDER_CHOICES = [
        (0, '未知'),
        (1, '男'),
        (2, '女'),
    ]
    gender = models.IntegerField(
        choices=GENDER_CHOICES, 
        default=0, 
        verbose_name='性别'
    )
    
    # 地理位置信息
    country = models.CharField(
        max_length=50, 
        null=True, 
        blank=True, 
        verbose_name='国家'
    )
    
    province = models.CharField(
        max_length=50, 
        null=True, 
        blank=True, 
        verbose_name='省份'
    )
    
    city = models.CharField(
        max_length=50, 
        null=True, 
        blank=True, 
        verbose_name='城市'
    )
    
    # 状态字段
    is_active = models.BooleanField(
        default=True, 
        verbose_name='是否激活'
    )
    
    last_login_time = models.DateTimeField(
        null=True, 
        blank=True, 
        verbose_name='最后登录时间'
    )
    
    class Meta:
        db_table = 'plant_wx_user'
        verbose_name = '微信小程序用户'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']
    
    def __str__(self):
        return f"{self.nickname or self.openid} - {self.user.username}"


class WxLoginLog(CoreModel):
    """微信登录日志"""
    
    wx_user = models.ForeignKey(
        WxUser,
        on_delete=models.CASCADE,
        related_name='login_logs',
        verbose_name='微信用户'
    )
    
    login_time = models.DateTimeField(
        auto_now_add=True,
        verbose_name='登录时间'
    )
    
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name='IP地址'
    )
    
    user_agent = models.TextField(
        null=True,
        blank=True,
        verbose_name='用户代理'
    )
    
    login_result = models.BooleanField(
        default=True,
        verbose_name='登录结果'
    )
    
    error_message = models.TextField(
        null=True,
        blank=True,
        verbose_name='错误信息'
    )
    
    class Meta:
        db_table = 'plant_wx_login_log'
        verbose_name = '微信登录日志'
        verbose_name_plural = verbose_name
        ordering = ['-login_time']
    
    def __str__(self):
        return f"{self.wx_user.nickname} - {self.login_time}"
