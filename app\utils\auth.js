import config from '@/config.js'

const TokenKey = 'App-Token'
const OpenidKey = 'App-Openid'

export function getToken() {
  return uni.getStorageSync(TokenKey)
}

export function setToken(token) {
  return uni.setStorageSync(TokenKey, token)
}

export function removeToken() {
  return uni.removeStorageSync(TokenKey)
}

/**
 * 获取用户openid
 * @returns {Promise<string>} openid
 */
export async function getOpenid() {
  try {
    // 先从缓存获取
    let openid = uni.getStorageSync(OpenidKey)
    if (openid) {
      console.log('从缓存获取openid:', openid)
      return openid
    }

    // 如果缓存中没有，通过微信登录获取
    console.log('缓存中没有openid，开始微信登录获取')

    const loginRes = await uni.login({
      provider: 'weixin'
    })

    console.log('微信登录结果:', loginRes)

    if (loginRes[1] && loginRes[1].code) {
      // 调用后端接口获取openid
      const res = await uni.request({
        url: config.baseUrl + '/api/auth/wechat/openid',
        method: 'POST',
        data: {
          code: loginRes[1].code
        }
      })

      console.log('获取openid接口返回:', res)

      if (res[1] && res[1].data && (res[1].data.code === 200 || res[1].data.code === 2000)) {
        openid = res[1].data.data.openid
        // 缓存openid
        uni.setStorageSync(OpenidKey, openid)
        console.log('成功获取并缓存openid:', openid)
        return openid
      } else {
        console.error('获取openid接口返回错误:', res[1])
        throw new Error('获取openid失败: ' + (res[1] && res[1].data ? res[1].data.msg : '未知错误'))
      }
    } else {
      console.error('微信登录失败:', loginRes)
      throw new Error('微信登录失败')
    }
  } catch (error) {
    console.error('获取openid异常:', error)
    // 开发环境返回测试openid
    console.warn('获取openid失败，返回测试openid')
    return 'test_openid_for_development'
  }
}

/**
 * 设置openid
 * @param {string} openid
 */
export function setOpenid(openid) {
  return uni.setStorageSync(OpenidKey, openid)
}

/**
 * 移除openid
 */
export function removeOpenid() {
  return uni.removeStorageSync(OpenidKey)
}

/**
 * 清除所有认证信息
 */
export function clearAuth() {
  removeToken()
  removeOpenid()
  uni.removeStorageSync('userInfo')
}
