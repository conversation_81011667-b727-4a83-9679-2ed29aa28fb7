{"version": 3, "sources": ["webpack:///D:/hanqc/project/pyProject/PlantHome/app/uni_modules/uni-title/components/uni-title/uni-title.vue?c47f", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/uni_modules/uni-title/components/uni-title/uni-title.vue?840c", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/uni_modules/uni-title/components/uni-title/uni-title.vue?a325", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/uni_modules/uni-title/components/uni-title/uni-title.vue?b787", "uni-app:///uni_modules/uni-title/components/uni-title/uni-title.vue", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/uni_modules/uni-title/components/uni-title/uni-title.vue?b4e2", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/uni_modules/uni-title/components/uni-title/uni-title.vue?2a54"], "names": ["name", "props", "type", "default", "title", "align", "color", "stat", "data", "computed", "textAlign", "watch", "uni", "mounted", "methods", "isOpenStat"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AAC8L;AAC9L,gBAAgB,yLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6uB,CAAgB,+qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACOjwB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,eAkBA;EACAA;EACAC;IACAC;MACAA;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK;IACA,QAEA;EACA;EACAC;IACAC;MACA;MACA;QACA;UACAL;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;MAAA;MAEA;IACA;EACA;EACAM;IACAP;MACA;QACA;QACA;UACAQ;QACA;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;QACAD;MACA;IACA;EACA;EACAE;IACAC;MACA;QACA;MACA;MACA,8GACA;MACA;QACA;QACA;UACA;QACA;MACA;MAEA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAAsjC,CAAgB,47BAAG,EAAC,C;;;;;;;;;;;ACA1kC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-title/components/uni-title/uni-title.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-title.vue?vue&type=template&id=9db29972&\"\nvar renderjs\nimport script from \"./uni-title.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-title.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-title.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-title/components/uni-title/uni-title.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-title.vue?vue&type=template&id=9db29972&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-title.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-title.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-title__box\" :style=\"{'align-items':textAlign}\">\r\n\t\t<text class=\"uni-title__base\" :class=\"['uni-'+type]\" :style=\"{'color':color}\">{{title}}</text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\n\t/**\n\t * Title 标题\n\t * @description 标题，通常用于记录页面标题，使用当前组件，uni-app 如果开启统计，将会自动统计页面标题\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=1066\n\t * @property {String} type = [h1|h2|h3|h4|h5] 标题类型\n\t * \t@value h1 一级标题\n\t * \t@value h2 二级标题\n\t * \t@value h3 三级标题\n\t * \t@value h4 四级标题\n\t * \t@value h5 五级标题\n\t * @property {String} title 标题内容\n\t * @property {String} align = [left|center|right] 对齐方式\n\t * \t@value left 做对齐\n\t * \t@value center 居中对齐\n\t * \t@value right 右对齐\n\t * @property {String} color 字体颜色\n\t * @property {Boolean} stat = [true|false] 是否开启统计功能呢，如不填写type值，默认为开启，填写 type 属性，默认为关闭\n\t */\r\n\texport default {\n\t\tname:\"UniTitle\",\r\n\t\tprops: {\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\talign: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'left'\r\n\t\t\t},\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#333333'\r\n\t\t\t},\r\n\t\t\tstat: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\ttextAlign() {\r\n\t\t\t\tlet align = 'center';\r\n\t\t\t\tswitch (this.align) {\r\n\t\t\t\t\tcase 'left':\r\n\t\t\t\t\t\talign = 'flex-start'\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'center':\r\n\t\t\t\t\t\talign = 'center'\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'right':\r\n\t\t\t\t\t\talign = 'flex-end'\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\treturn align\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\ttitle(newVal) {\r\n\t\t\t\tif (this.isOpenStat()) {\r\n\t\t\t\t\t// 上报数据\r\n\t\t\t\t\tif (uni.report) {\r\n\t\t\t\t\t\tuni.report('title', this.title)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tif (this.isOpenStat()) {\r\n\t\t\t\t// 上报数据\r\n\t\t\t\tif (uni.report) {\n\t\t\t\t\tuni.report('title', this.title)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tisOpenStat() {\r\n\t\t\t\tif (this.stat === '') {\r\n\t\t\t\t\tthis.isStat = false\r\n\t\t\t\t}\r\n\t\t\t\tlet stat_type = (typeof(this.stat) === 'boolean' && this.stat) || (typeof(this.stat) === 'string' && this.stat !==\r\n\t\t\t\t\t'')\r\n\t\t\t\tif (this.type === \"\") {\r\n\t\t\t\t\tthis.isStat = true\r\n\t\t\t\t\tif (this.stat.toString() === 'false') {\r\n\t\t\t\t\t\tthis.isStat = false\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.type !== '') {\r\n\t\t\t\t\tthis.isStat = true\r\n\t\t\t\t\tif (stat_type) {\r\n\t\t\t\t\t\tthis.isStat = true\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.isStat = false\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn this.isStat\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* .uni-title {\r\n\r\n\t} */\r\n\t.uni-title__box {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: column;\r\n\t\talign-items: flex-start;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 8px 0;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.uni-title__base {\r\n\t\tfont-size: 15px;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.uni-h1 {\r\n\t\tfont-size: 20px;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.uni-h2 {\r\n\t\tfont-size: 18px;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.uni-h3 {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t\t/* font-weight: 400; */\r\n\t}\r\n\r\n\t.uni-h4 {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t\t/* font-weight: 300; */\r\n\t}\r\n\r\n\t.uni-h5 {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t\t/* font-weight: 200; */\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-title.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-title.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751643652903\n      var cssReload = require(\"D:/System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}