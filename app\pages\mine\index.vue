<template>
  <view class="mine-container" :style="{height: `${windowHeight}px`}">
    <!--顶部个人信息栏-->
    <view class="header-section">
      <view class="flex padding justify-between">
        <view class="flex align-center">
          <view v-if="!avatar" class="cu-avatar xl round bg-white">
            <view class="iconfont icon-people text-gray icon"></view>
          </view>
          <image v-if="avatar" @click="handleToAvatar" :src="avatar" class="cu-avatar xl round" mode="widthFix">
          </image>
          <view v-if="!name" @click="handleToLogin" class="login-tip">
            点击登录
          </view>
          <view v-if="name" @click="handleToInfo" class="user-info">
            <view class="u_title">
              {{ name }}
            </view>
          </view>
        </view>
        <view @click="handleToInfo" class="flex align-center">
          <text>个人信息</text>
          <view class="iconfont icon-right"></view>
        </view>
      </view>
    </view>

    <view class="content-section">
      <!-- 订单管理 -->
      <view class="order-section">
        <view class="section-header">
          <text class="title">我的订单</text>
          <view class="more" @click="handleViewAllOrders">
            <text>查看全部</text>
            <view class="iconfont icon-right"></view>
          </view>
        </view>
        <view class="order-actions grid col-5 text-center">
          <view class="action-item" @click="handleOrderStatus(0)">
            <view class="iconfont icon-pay text-orange icon"></view>
            <text class="text">待付款</text>
          </view>
          <view class="action-item" @click="handleOrderStatus(1)">
            <view class="iconfont icon-deliver text-blue icon"></view>
            <text class="text">待发货</text>
          </view>
          <view class="action-item" @click="handleOrderStatus(2)">
            <view class="iconfont icon-send text-green icon"></view>
            <text class="text">待收货</text>
          </view>
          <view class="action-item" @click="handleOrderStatus(3)">
            <view class="iconfont icon-evaluate text-purple icon"></view>
            <text class="text">待评价</text>
          </view>
          <view class="action-item" @click="handleAfterSale">
            <view class="iconfont icon-service text-red icon"></view>
            <text class="text">售后</text>
          </view>
        </view>
      </view>

      <view class="menu-list">
        <view class="list-cell list-cell-arrow" @click="handleToProfile">
          <view class="menu-item-box">
            <view class="iconfont icon-people menu-icon"></view>
            <view>个人资料</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleToAddress">
          <view class="menu-item-box">
            <view class="iconfont icon-location menu-icon"></view>
            <view>收货地址</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleToCollect">
          <view class="menu-item-box">
            <view class="iconfont icon-favor menu-icon"></view>
            <view>我的收藏</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleToCoupon">
          <view class="menu-item-box">
            <view class="iconfont icon-ticket menu-icon"></view>
            <view>优惠券</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleHelp">
          <view class="menu-item-box">
            <view class="iconfont icon-help menu-icon"></view>
            <view>常见问题</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleAbout">
          <view class="menu-item-box">
            <view class="iconfont icon-info menu-icon"></view>
            <view>关于我们</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleToSetting">
          <view class="menu-item-box">
            <view class="iconfont icon-setting menu-icon"></view>
            <view>应用设置</view>
          </view>
        </view>
      </view>

    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        name: this.$store.state.user.name
      }
    },
    computed: {
      avatar() {
        return this.$store.state.user.avatar
      },
      windowHeight() {
        return uni.getSystemInfoSync().windowHeight - 50
      }
    },
    methods: {
      handleToInfo() {
        this.$tab.navigateTo('/pages/mine/info/index')
      },
      handleToProfile() {
        this.$tab.navigateTo('/pages/mine/profile/index')
      },
      handleToSetting() {
        this.$tab.navigateTo('/pages/mine/setting/index')
      },
      handleToLogin() {
        this.$tab.reLaunch('/pages/login')
      },
      handleToAvatar() {
        this.$tab.navigateTo('/pages/mine/avatar/index')
      },
      handleHelp() {
        this.$tab.navigateTo('/pages/mine/help/index')
      },
      handleAbout() {
        this.$tab.navigateTo('/pages/mine/about/index')
      },
      // 植物相关功能
      handleMyPlants() {
        this.$modal.showToast('我的植物')
      },
      handlePlantCare() {
        this.$modal.showToast('养护记录')
      },
      handlePlantEncyclopedia() {
        this.$modal.showToast('植物百科')
      },
      // 收藏
      handleToCollect() {
        this.$modal.showToast('我的收藏')
      }
    }
  }
</script>

<style lang="scss" scoped>
  page {
    background-color: #f5f5f7;
  }

  .mine-container {
    width: 100%;
    height: 100%;

    .header-section {
      padding: 15px 15px 45px 15px;
      background-color: #3c96f3;
      color: white;

      .login-tip {
        font-size: 18px;
        margin-left: 10px;
      }

      .cu-avatar {
        border: 2px solid #eaeaea;

        .icon {
          font-size: 40px;
        }
      }

      .user-info {
        margin-left: 15px;

        .u_title {
          font-size: 18px;
          line-height: 30px;
        }
      }
    }

    .content-section {
      position: relative;
      top: -50px;
      
      .order-section {
        margin: 15px 15px;
        padding: 15px;
        border-radius: 8px;
        background-color: white;
        
        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-bottom: 15px;
          
          .title {
            font-size: 16px;
            font-weight: bold;
          }
          
          .more {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #999;
          }
        }
        
        .order-actions {
          .action-item {
            .icon {
              font-size: 24px;
            }

            .text {
              display: block;
              font-size: 12px;
              margin: 8px 0px;
            }
          }
        }
      }
      
      .menu-list {
        margin: 15px;
        border-radius: 8px;
        background-color: white;
        
        .list-cell {
          position: relative;
          padding: 15px;
          border-bottom: 1px solid #f5f5f7;
          
          &.list-cell-arrow::after {
            content: "\e6a3";
            font-family: "iconfont";
            position: absolute;
            right: 15px;
            color: #999;
          }
          
          .menu-item-box {
            display: flex;
            align-items: center;
            
            .menu-icon {
              margin-right: 10px;
              font-size: 18px;
              color: #3c96f3;
            }
          }
        }
      }
    }
  }
</style>
