@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.profile-container.data-v-b1b03f2e {
  padding: 30rpx;
  background-color: #ffffff;
  min-height: 100vh;
}
.profile-container .avatar-section.data-v-b1b03f2e {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50rpx;
  padding-top: 50rpx;
}
.profile-container .avatar-section .avatar-button.data-v-b1b03f2e {
  padding: 0;
  background: none;
  border: none;
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  transition: -webkit-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s;
}
.profile-container .avatar-section .avatar-button.data-v-b1b03f2e::after {
  border: none;
}
.profile-container .avatar-section .avatar-button.data-v-b1b03f2e:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.profile-container .avatar-section .avatar-button .avatar.data-v-b1b03f2e {
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  border: 4rpx solid #f0f0f0;
  transition: border-color 0.2s;
}
.profile-container .avatar-section .avatar-button:hover .avatar.data-v-b1b03f2e {
  border-color: #4CAF50;
}
.profile-container .avatar-section .tip.data-v-b1b03f2e {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}
.profile-container .avatar-section .hidden-avatar-button.data-v-b1b03f2e {
  position: fixed;
  top: -200rpx;
  left: -200rpx;
  width: 1rpx;
  height: 1rpx;
  opacity: 0;
  pointer-events: none;
}
.profile-container .form-section .form-item.data-v-b1b03f2e {
  margin-bottom: 30rpx;
}
.profile-container .form-section .form-item .label.data-v-b1b03f2e {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.profile-container .form-section .form-item input.data-v-b1b03f2e {
  width: 100%;
  height: 90rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
}
.profile-container .form-section .save-btn.data-v-b1b03f2e {
  margin-top: 50rpx;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
}
