from django.contrib import admin
from .models import WxUser, WxLoginLog


@admin.register(WxUser)
class WxUserAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'nickname', 'openid', 'user', 'gender', 
        'is_active', 'last_login_time', 'create_datetime'
    ]
    list_filter = ['gender', 'is_active', 'country', 'province', 'create_datetime']
    search_fields = ['nickname', 'openid', 'user__username', 'user__name']
    readonly_fields = ['openid', 'unionid', 'session_key', 'create_datetime', 'update_datetime']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'openid', 'unionid', 'session_key')
        }),
        ('用户信息', {
            'fields': ('nickname', 'avatar_url', 'gender')
        }),
        ('地理信息', {
            'fields': ('country', 'province', 'city')
        }),
        ('状态信息', {
            'fields': ('is_active', 'last_login_time')
        }),
        ('时间信息', {
            'fields': ('create_datetime', 'update_datetime')
        }),
    )


@admin.register(WxLoginLog)
class WxLoginLogAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'wx_user', 'login_time', 'ip_address', 
        'login_result', 'error_message'
    ]
    list_filter = ['login_result', 'login_time']
    search_fields = ['wx_user__nickname', 'wx_user__openid', 'ip_address']
    readonly_fields = ['wx_user', 'login_time', 'ip_address', 'user_agent', 'login_result', 'error_message']
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
