<template>
  <view class="profile-container">
    <view class="avatar-section">
      <button open-type="chooseAvatar" @chooseavatar="onChooseAvatar" class="avatar-button">
        <image :src="userInfo.avatar || defaultAvatar" class="avatar"></image>
      </button>
      <text class="tip">点击更换头像</text>
    </view>
    
    <view class="form-section">
      <view class="form-item">
        <text class="label">昵称</text>
        <input type="nickname" @change="onInputNickname" :value="userInfo.nickname" placeholder="请输入您的昵称" />
      </view>
      
      <button @click="saveProfile" class="save-btn cu-btn block bg-green lg">保存</button>
    </view>
  </view>
</template>

<script>
import { updateUserInfo } from '@/api/auth'
import { getToken } from '@/utils/auth'
import config from '@/config'

export default {
  data() {
    return {
      userInfo: {
        avatar: '',
        nickname: ''
      },
      defaultAvatar: '/static/images/default-avatar.png',
      isFirstLogin: false,
      baseUrl: config.baseUrl || 'http://localhost:8080'
    }
  },
  onLoad(options) {
    // 获取当前用户信息
    const userInfo = uni.getStorageSync('userInfo') || {}
    this.userInfo = {
      ...userInfo,
      nickname: userInfo.nickName || '',
      avatar: userInfo.avatarUrl || ''
    }
    
    // 检查是否首次登录
    this.isFirstLogin = options.first === '1'
  },
  methods: {
    // 选择头像回调
    onChooseAvatar(e) {
      const { avatarUrl } = e.detail
      this.userInfo.avatar = avatarUrl
      
      // 上传头像到服务器
      this.uploadAvatar(avatarUrl)
    },
    
    // 输入昵称回调
    onInputNickname(e) {
      this.userInfo.nickname = e.detail.value
    },
    
    // 上传头像
    uploadAvatar(filePath) {
      uni.showLoading({ title: '上传中...' })
      
      // 使用配置的baseUrl
      const uploadUrl = this.baseUrl + '/api/common/upload';
      console.log('上传URL:', uploadUrl);
      
      // 将头像上传到临时文件
      uni.uploadFile({
        url: uploadUrl,
        filePath: filePath,
        name: 'file',
        header: {
          Authorization: 'Bearer ' + getToken()
        },
        success: (uploadRes) => {
          try {
            const data = JSON.parse(uploadRes.data)
            if (data.code === 200) {
              this.userInfo.avatar = data.url
              uni.hideLoading()
              uni.showToast({ title: '上传成功', icon: 'success' })
            } else {
              uni.hideLoading()
              uni.showToast({ title: data.msg || '上传失败', icon: 'none' })
            }
          } catch (e) {
            uni.hideLoading()
            uni.showToast({ title: '上传失败', icon: 'none' })
          }
        },
        fail: (err) => {
          console.error('上传失败:', err);
          uni.hideLoading()
          uni.showToast({ title: '上传失败', icon: 'none' })
        }
      })
    },
    
    // 保存个人资料
    saveProfile() {
      if (!this.userInfo.nickname) {
        return uni.showToast({ title: '请输入昵称', icon: 'none' })
      }
      
      uni.showLoading({ title: '保存中...' })
      
      const updateData = {
        nickname: this.userInfo.nickname,
        avatar: this.userInfo.avatar
      }
      
      updateUserInfo(updateData).then(res => {
        uni.hideLoading()
        if (res.code === 200) {
          // 更新本地存储
          uni.setStorageSync('userInfo', {
            nickName: this.userInfo.nickname,
            avatarUrl: this.userInfo.avatar,
            gender: this.userInfo.gender || 0
          })
          
          uni.showToast({ title: '保存成功' })
          
          // 如果是首次登录，跳转到首页，否则返回上一页
          if (this.isFirstLogin) {
            this.$tab.reLaunch('/pages/index')
          } else {
            // 延迟返回
            setTimeout(() => {
              uni.navigateBack()
            }, 1500)
          }
        } else {
          uni.showToast({ title: res.msg || '保存失败', icon: 'none' })
        }
      }).catch(() => {
        uni.hideLoading()
        uni.showToast({ title: '保存失败', icon: 'none' })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  padding: 30rpx;
  background-color: #ffffff;
  min-height: 100vh;
  
  .avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 50rpx;
    padding-top: 50rpx;
    
    .avatar-button {
      padding: 0;
      background: none;
      border: none;
      width: 180rpx;
      height: 180rpx;
      border-radius: 50%;
      overflow: hidden;
      
      &::after {
        border: none;
      }
      
      .avatar {
        width: 180rpx;
        height: 180rpx;
        border-radius: 50%;
      }
    }
    
    .tip {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #999;
    }
  }
  
  .form-section {
    .form-item {
      margin-bottom: 30rpx;
      
      .label {
        display: block;
        font-size: 30rpx;
        color: #333;
        margin-bottom: 20rpx;
      }
      
      input {
        width: 100%;
        height: 90rpx;
        border: 1px solid #eee;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 30rpx;
      }
    }
    
    .save-btn {
      margin-top: 50rpx;
      height: 90rpx;
      line-height: 90rpx;
      font-size: 32rpx;
    }
  }
}
</style> 