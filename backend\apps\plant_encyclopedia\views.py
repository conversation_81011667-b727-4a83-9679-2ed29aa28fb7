"""
植物百科视图
"""
from django.db.models import Q
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.filters import SearchFilter, OrderingFilter
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from dvadmin.utils.viewset import CustomModelViewSet
from dvadmin.utils.json_response import DetailResponse, SuccessResponse, ErrorResponse
from dvadmin.utils.filters import CustomDjangoFilterBackend
from .models import PlantEncyclopedia
from .serializers import (
    PlantEncyclopediaSerializer,
    PlantEncyclopediaListSerializer,
    PlantEncyclopediaCreateSerializer,
    PlantEncyclopediaUpdateSerializer,
    PlantEncyclopediaSearchSerializer
)


class PlantEncyclopediaViewSet(CustomModelViewSet):
    """
    植物百科视图集
    list: 查询植物百科列表
    create: 新增植物百科
    retrieve: 获取植物百科详情
    update: 修改植物百科
    destroy: 删除植物百科
    """
    
    queryset = PlantEncyclopedia.objects.all()
    serializer_class = PlantEncyclopediaSerializer
    list_serializer_class = PlantEncyclopediaListSerializer
    create_serializer_class = PlantEncyclopediaCreateSerializer
    update_serializer_class = PlantEncyclopediaUpdateSerializer

    # 过滤和搜索配置
    filter_backends = [CustomDjangoFilterBackend, SearchFilter, OrderingFilter]
    filter_fields = ['category', 'status', 'source_site']
    search_fields = ['name', 'scientific_name', 'description', 'search_keywords']
    ordering_fields = ['id', 'create_datetime', 'update_datetime', 'view_count', 'name']
    ordering = ['id']  # 按ID正序排序
    
    # 导出配置
    export_field_label = {
        'name': '植物名称',
        'scientific_name': '学名',
        'category': '分类',
        'family': '科',
        'genus': '属',
        'flowering_period': '花期',
        'source_site': '来源网站',
        'status': '状态',
        'view_count': '浏览次数',
        'create_datetime': '创建时间',
    }
    
    def retrieve(self, request, *args, **kwargs):
        """
        获取植物详情，同时增加浏览次数
        """
        instance = self.get_object()
        # 增加浏览次数
        instance.increment_view_count()
        serializer = self.get_serializer(instance)
        return DetailResponse(data=serializer.data, msg="获取成功")
    
    @swagger_auto_schema(
        operation_description="搜索植物百科",
        request_body=PlantEncyclopediaSearchSerializer,
        responses={200: PlantEncyclopediaListSerializer(many=True)}
    )
    @action(methods=['POST'], detail=False, url_path='search')
    def search_plants(self, request):
        """
        植物百科搜索接口
        """
        search_serializer = PlantEncyclopediaSearchSerializer(data=request.data)
        if not search_serializer.is_valid():
            return ErrorResponse(msg="参数错误", data=search_serializer.errors)
        
        validated_data = search_serializer.validated_data
        queryset = self.get_queryset()
        
        # 关键词搜索
        q = validated_data.get('q')
        if q:
            queryset = queryset.filter(
                Q(name__icontains=q) |
                Q(scientific_name__icontains=q) |
                Q(description__icontains=q) |
                Q(search_keywords__icontains=q) |
                Q(tags__icontains=q)
            )
        
        # 分类筛选
        category = validated_data.get('category')
        if category:
            queryset = queryset.filter(category=category)
        
        # 状态筛选
        status_filter = validated_data.get('status')
        if status_filter is not None:
            queryset = queryset.filter(status=status_filter)
        
        # 排序
        ordering = validated_data.get('ordering', 'id')  # 默认按ID正序排序
        queryset = queryset.order_by(ordering)
        
        # 分页
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = PlantEncyclopediaListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = PlantEncyclopediaListSerializer(queryset, many=True)
        return SuccessResponse(data=serializer.data, msg="搜索成功")
    
    @swagger_auto_schema(
        operation_description="获取植物分类统计",
        responses={200: openapi.Response("获取成功")}
    )
    @action(methods=['GET'], detail=False, url_path='category-stats')
    def category_stats(self, request):
        """
        获取植物分类统计信息
        """
        from django.db.models import Count
        
        stats = PlantEncyclopedia.objects.filter(status=1).values('category').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # 格式化数据
        category_dict = dict(PlantEncyclopedia.CATEGORY_CHOICES)
        result = []
        for item in stats:
            result.append({
                'category': item['category'],
                'category_display': category_dict.get(item['category'], item['category']),
                'count': item['count']
            })
        
        return SuccessResponse(data=result, msg="获取成功")
    
    @swagger_auto_schema(
        operation_description="获取热门植物",
        manual_parameters=[
            openapi.Parameter('limit', openapi.IN_QUERY, description="数量限制", type=openapi.TYPE_INTEGER, default=10)
        ],
        responses={200: PlantEncyclopediaListSerializer(many=True)}
    )
    @action(methods=['GET'], detail=False, url_path='popular')
    def popular_plants(self, request):
        """
        获取热门植物（按浏览量排序）
        """
        limit = int(request.query_params.get('limit', 10))
        queryset = self.get_queryset().filter(status=1).order_by('-view_count')[:limit]
        
        serializer = PlantEncyclopediaListSerializer(queryset, many=True)
        return SuccessResponse(data=serializer.data, msg="获取成功")
    
    @swagger_auto_schema(
        operation_description="获取最新植物",
        manual_parameters=[
            openapi.Parameter('limit', openapi.IN_QUERY, description="数量限制", type=openapi.TYPE_INTEGER, default=10)
        ],
        responses={200: PlantEncyclopediaListSerializer(many=True)}
    )
    @action(methods=['GET'], detail=False, url_path='latest')
    def latest_plants(self, request):
        """
        获取最新植物
        """
        limit = int(request.query_params.get('limit', 10))
        queryset = self.get_queryset().filter(status=1).order_by('-create_datetime')[:limit]
        
        serializer = PlantEncyclopediaListSerializer(queryset, many=True)
        return SuccessResponse(data=serializer.data, msg="获取成功")
