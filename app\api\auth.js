import request from '@/utils/request'

// 登录方法
export function login(username, password, code, uuid) {
  return request({
    url: '/api/auth/login/',
    headers: {
      isToken: false
    },
    method: 'post',
    data: { username, password, code, uuid }
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/api/auth/register/',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 微信登录
export function wxLogin(code, userInfo) {
  return request({
    url: '/api/auth/login/',
    headers: {
      isToken: false
    },
    method: 'post',
    data: { code, userInfo }
  })
}

// 更新用户信息
export function updateUserInfo(data) {
  return request({
    url: '/api/auth/update/',
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/api/auth/user/',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/api/auth/logout/',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/api/captchaImage/',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}