<template>
  <view class="search-container">
    <!-- 搜索栏 -->
    <view class="search-header">
      <view class="search-input-wrapper">
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <input 
          class="search-input" 
          v-model="searchKeyword" 
          placeholder="搜索商品" 
          confirm-type="search"
          @confirm="handleSearch"
          @input="handleInput"
          focus
        />
        <uni-icons 
          v-if="searchKeyword" 
          type="clear" 
          size="16" 
          color="#999" 
          @click="clearKeyword"
        ></uni-icons>
      </view>
      <text class="cancel-btn" @click="goBack">取消</text>
    </view>

    <!-- 搜索建议 -->
    <view class="search-suggestions" v-if="showSuggestions && suggestions.length > 0">
      <view 
        class="suggestion-item" 
        v-for="(suggestion, index) in suggestions" 
        :key="index"
        @click="selectSuggestion(suggestion)"
      >
        <uni-icons type="search" size="14" color="#999"></uni-icons>
        <text class="suggestion-text">{{ suggestion }}</text>
      </view>
    </view>

    <!-- 搜索内容区域 -->
    <scroll-view scroll-y class="search-content" v-if="!showSuggestions">
      <!-- 搜索历史 -->
      <view class="history-section" v-if="!hasSearched && searchHistory.length > 0">
        <view class="section-header">
          <text class="section-title">搜索历史</text>
          <uni-icons type="trash" size="16" color="#999" @click="clearHistory"></uni-icons>
        </view>
        <view class="history-tags">
          <view 
            class="history-tag" 
            v-for="(keyword, index) in searchHistory" 
            :key="index"
            @click="selectHistory(keyword)"
          >
            <text>{{ keyword }}</text>
            <uni-icons type="close" size="12" color="#999" @click.stop="removeHistory(keyword)"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 热门搜索 -->
      <view class="hot-section" v-if="!hasSearched && hotKeywords.length > 0">
        <view class="section-title">热门搜索</view>
        <view class="hot-tags">
          <view 
            class="hot-tag" 
            v-for="(keyword, index) in hotKeywords" 
            :key="index"
            @click="selectHotKeyword(keyword)"
          >
            <text>{{ keyword }}</text>
          </view>
        </view>
      </view>

      <!-- 搜索结果 -->
      <view class="search-results" v-if="hasSearched">
        <view class="result-header">
          <text class="result-count">找到{{ total }}个相关商品</text>
          <view class="sort-options">
            <text 
              class="sort-option" 
              :class="{ active: currentSort === 'default' }"
              @click="changeSort('default')"
            >综合</text>
            <text 
              class="sort-option" 
              :class="{ active: currentSort === 'sales' }"
              @click="changeSort('sales')"
            >销量</text>
            <text 
              class="sort-option" 
              :class="{ active: currentSort === 'price' }"
              @click="changeSort('price')"
            >价格</text>
          </view>
        </view>

        <!-- 商品列表 -->
        <view class="goods-list">
          <view 
            class="goods-item" 
            v-for="goods in searchResults" 
            :key="goods.goodsId"
            @click="goToGoodsDetail(goods)"
          >
            <image
              class="goods-image"
              :src="getGoodsImage(goods)"
              mode="aspectFill"
              @error="handleImageError"
            ></image>
            <view class="goods-info">
              <text class="goods-name" v-html="highlightKeyword(goods.goodsName)"></text>
              <text class="goods-desc">{{ goods.goodsDesc }}</text>
              <view class="price-row">
                <text class="current-price">¥{{ formatPrice(getGoodsPrice(goods)) }}</text>
                <text class="original-price" v-if="getGoodsOriginalPrice(goods) > getGoodsPrice(goods)">¥{{ formatPrice(getGoodsOriginalPrice(goods)) }}</text>
              </view>
              <view class="goods-meta">
                <text class="sales">已售{{ getGoodsSales(goods) }}</text>
                <text class="rating" v-if="goods.rating">{{ goods.rating }}分</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <uni-load-more :status="loadStatus"></uni-load-more>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && searchResults.length === 0">
          <uni-icons type="search" size="60" color="#ccc"></uni-icons>
          <text>没有找到相关商品</text>
          <text class="empty-tip">试试其他关键词吧</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { searchGoods, getHotKeywords } from '@/api/shop.js'
import { formatPrice, searchHistory, debounce } from '@/utils/shop.js'
import config from '@/config.js'

export default {
  data() {
    return {
      searchKeyword: '',
      searchHistory: [],
      hotKeywords: [],
      suggestions: [],
      searchResults: [],
      
      // 状态
      showSuggestions: false,
      hasSearched: false,
      loading: false,
      loadStatus: 'more',
      
      // 搜索参数
      currentSort: 'default',
      pageNum: 1,
      pageSize: 20,
      total: 0,
      hasMore: true
    }
  },
  onLoad(options) {
    if (options.keyword) {
      try {
        // 尝试解码关键词，处理可能的编码问题
        this.searchKeyword = decodeURIComponent(options.keyword)
      } catch (e) {
        // 如果解码失败，使用原始关键词
        this.searchKeyword = options.keyword
      }
      this.handleSearch()
    }

    this.loadInitData()
  },
  methods: {
    // 加载初始数据
    async loadInitData() {
      this.searchHistory = searchHistory.get()
      
      try {
        const res = await getHotKeywords()
        this.hotKeywords = res.data || []
      } catch (error) {
        console.error('加载热门搜索失败:', error)
        // 使用默认热门搜索
        this.hotKeywords = ['手机', '电脑', '耳机', '充电器', '数据线', '音响']
      }
    },

    // 输入处理
    handleInput: debounce(function(e) {
      const value = e.detail.value
      this.searchKeyword = value
      
      if (value.trim()) {
        this.getSuggestions(value)
      } else {
        this.showSuggestions = false
      }
    }, 300),

    // 获取搜索建议
    getSuggestions(keyword) {
      // 这里可以调用API获取搜索建议
      // 暂时使用本地模拟
      const mockSuggestions = [
        `${keyword}手机`,
        `${keyword}配件`,
        `${keyword}保护套`,
        `${keyword}充电器`
      ]
      
      this.suggestions = mockSuggestions.slice(0, 5)
      this.showSuggestions = true
    },

    // 执行搜索
    async handleSearch() {
      const keyword = this.searchKeyword.trim()
      if (!keyword) return
      
      // 添加到搜索历史
      searchHistory.add(keyword)
      this.searchHistory = searchHistory.get()
      
      this.showSuggestions = false
      this.hasSearched = true
      this.pageNum = 1
      this.hasMore = true
      this.searchResults = []
      
      await this.loadSearchResults()
    },

    // 加载搜索结果
    async loadSearchResults(loadMore = false) {
      if (this.loading) return
      
      if (!loadMore) {
        this.pageNum = 1
        this.hasMore = true
        this.searchResults = []
      }
      
      if (!this.hasMore) {
        this.loadStatus = 'noMore'
        return
      }
      
      this.loading = true
      this.loadStatus = 'loading'
      
      try {
        const params = {
          keyword: this.searchKeyword,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
        
        // 添加排序（与后端SQL映射保持一致）
        switch (this.currentSort) {
          case 'sales':
            params.orderBy = 'sales_count desc'
            break
          case 'price':
            params.orderBy = 'sell_price asc'
            break
          case 'default':
          default:
            // 默认排序：销量优先，然后按创建时间
            params.orderBy = 'default'
            break
        }
        
        const res = await searchGoods(params)
        const newResults = res.rows || []
        
        if (loadMore) {
          this.searchResults.push(...newResults)
        } else {
          this.searchResults = newResults
        }
        
        this.total = res.total || 0
        this.hasMore = newResults.length >= this.pageSize
        this.pageNum++
        
        this.loadStatus = this.hasMore ? 'more' : 'noMore'
        
      } catch (error) {
        console.error('搜索失败:', error)
        this.loadStatus = 'more'
        uni.showToast({
          title: '搜索失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 选择搜索建议
    selectSuggestion(suggestion) {
      this.searchKeyword = suggestion
      this.handleSearch()
    },

    // 选择搜索历史
    selectHistory(keyword) {
      this.searchKeyword = keyword
      this.handleSearch()
    },

    // 选择热门搜索
    selectHotKeyword(keyword) {
      this.searchKeyword = keyword
      this.handleSearch()
    },

    // 删除搜索历史
    removeHistory(keyword) {
      searchHistory.remove(keyword)
      this.searchHistory = searchHistory.get()
    },

    // 清空搜索历史
    clearHistory() {
      uni.showModal({
        title: '提示',
        content: '确定要清空搜索历史吗？',
        success: (res) => {
          if (res.confirm) {
            searchHistory.clear()
            this.searchHistory = []
          }
        }
      })
    },

    // 清空关键词
    clearKeyword() {
      this.searchKeyword = ''
      this.showSuggestions = false
      this.hasSearched = false
    },

    // 改变排序
    changeSort(sort) {
      this.currentSort = sort
      this.loadSearchResults()
    },

    // 高亮关键词
    highlightKeyword(text) {
      if (!this.searchKeyword || !text) return text
      const keyword = this.searchKeyword.trim()
      const regex = new RegExp(`(${keyword})`, 'gi')
      return text.replace(regex, '<span style="color: #ff6700;">$1</span>')
    },

    // 跳转到商品详情
    goToGoodsDetail(goods) {
      uni.navigateTo({
        url: `/pages/shop/goods/detail?goodsId=${goods.goodsId}`
      })
    },

    // 返回
    goBack() {
      uni.navigateBack()
    },

    // 格式化价格
    formatPrice,

    // 获取商品图片
    getGoodsImage(goods) {
      // 优先使用实体类字段名，然后是API文档字段名
      const imageUrl = goods.coverImg || goods.mainPic || goods.image
      return this.getFullImageUrl(imageUrl)
    },

    // 获取完整的图片URL
    getFullImageUrl(imageUrl) {
      if (!imageUrl) return '/static/images/goods/default.svg'
      if (imageUrl.startsWith('http')) return imageUrl
      return config.baseUrl + imageUrl
    },

    // 获取商品价格（优先使用实体类字段名）
    getGoodsPrice(goods) {
      return goods.price || goods.sellPrice || 0
    },

    // 获取商品原价（优先使用实体类字段名）
    getGoodsOriginalPrice(goods) {
      return goods.originalPrice || goods.marketPrice || 0
    },

    // 获取商品销量（优先使用实体类字段名）
    getGoodsSales(goods) {
      return goods.sales || goods.salesCount || 0
    },

    // 图片加载错误处理
    handleImageError(e) {
      e.target.src = '/static/images/goods/default.svg'
    }
  },
  
  // 触底加载更多
  onReachBottom() {
    if (this.hasSearched && this.hasMore && !this.loading) {
      this.loadSearchResults(true)
    }
  }
}
</script>

<style lang="scss" scoped>
.search-container {
  height: 100vh;
  background-color: #f5f5f7;
  display: flex;
  flex-direction: column;
}

.search-header {
  background-color: #fff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #eee;

  .search-input-wrapper {
    flex: 1;
    background-color: #f5f5f5;
    border-radius: 20rpx;
    padding: 15rpx 20rpx;
    display: flex;
    align-items: center;
    margin-right: 20rpx;

    .search-input {
      flex: 1;
      font-size: 28rpx;
      margin: 0 15rpx;
    }
  }

  .cancel-btn {
    font-size: 28rpx;
    color: #007aff;
  }
}

.search-suggestions {
  background-color: #fff;
  border-bottom: 1px solid #eee;

  .suggestion-item {
    display: flex;
    align-items: center;
    padding: 25rpx 30rpx;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .suggestion-text {
      margin-left: 15rpx;
      font-size: 28rpx;
      color: #333;
    }
  }
}

.search-content {
  flex: 1;
  padding: 30rpx;
}

.history-section, .hot-section {
  margin-bottom: 40rpx;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
  }

  .section-title {
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
  }
}

.history-tags, .hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.history-tag, .hot-tag {
  background-color: #f5f5f5;
  border-radius: 20rpx;
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;

  text {
    margin-right: 10rpx;
  }
}

.hot-tag {
  background-color: #fff;
  border: 1px solid #ddd;
}

.search-results {
  .result-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;

    .result-count {
      font-size: 28rpx;
      color: #666;
    }

    .sort-options {
      display: flex;
      gap: 30rpx;

      .sort-option {
        font-size: 28rpx;
        color: #666;

        &.active {
          color: #007aff;
          font-weight: bold;
        }
      }
    }
  }
}

.goods-list {
  .goods-item {
    background-color: #fff;
    border-radius: 10rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    display: flex;

    .goods-image {
      width: 200rpx;
      height: 200rpx;
      border-radius: 8rpx;
      margin-right: 20rpx;
    }

    .goods-info {
      flex: 1;

      .goods-name {
        font-size: 30rpx;
        color: #333;
        font-weight: bold;
        margin-bottom: 10rpx;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .goods-desc {
        font-size: 26rpx;
        color: #666;
        margin-bottom: 15rpx;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .price-row {
        display: flex;
        align-items: center;
        margin-bottom: 15rpx;

        .current-price {
          font-size: 36rpx;
          color: #ff6700;
          font-weight: bold;
        }

        .original-price {
          font-size: 26rpx;
          color: #999;
          text-decoration: line-through;
          margin-left: 15rpx;
        }
      }

      .goods-meta {
        display: flex;
        justify-content: space-between;
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #999;

  text {
    margin-top: 20rpx;
    font-size: 28rpx;
  }

  .empty-tip {
    font-size: 24rpx;
    color: #ccc;
  }
}
</style>
