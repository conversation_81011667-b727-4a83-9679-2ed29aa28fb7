<!DOCTYPE html>
<html>
<head>
    <title>Request 逻辑测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>Request 处理逻辑测试</h1>
    <button onclick="testRequestLogic()">测试请求处理</button>
    <div id="result"></div>

    <script>
        // 模拟 errorCode 对象
        const errorCode = {
            401: '登录状态已过期',
            500: '服务器内部错误',
            default: '系统未知错误'
        };

        // 模拟请求处理逻辑
        function processResponse(mockResponse) {
            return new Promise((resolve, reject) => {
                const code = mockResponse.code || 200;
                const msg = errorCode[code] || mockResponse.msg || errorCode['default'];
                
                console.log(`处理响应: code=${code}, msg=${msg}`);
                
                if (code === 401) {
                    console.log('401 - 登录过期');
                    reject('无效的会话，或者会话已过期，请重新登录。');
                } else if (code === 500) {
                    console.log('500 - 服务器错误');
                    reject('500');
                } else if (code !== 200 && code !== 2000) {  // 修复后的逻辑
                    console.log(`${code} - 其他错误`);
                    reject(code);
                } else {
                    console.log(`${code} - 成功`);
                    resolve(mockResponse);
                }
            });
        }

        async function testRequestLogic() {
            const resultDiv = document.getElementById('result');
            let results = [];

            // 测试用例
            const testCases = [
                { name: '成功响应 200', response: { code: 200, msg: '成功', data: {} } },
                { name: '成功响应 2000', response: { code: 2000, msg: '登录成功', data: { token: 'xxx' } } },
                { name: '登录过期 401', response: { code: 401, msg: '未授权' } },
                { name: '服务器错误 500', response: { code: 500, msg: '内部错误' } },
                { name: '其他错误 4000', response: { code: 4000, msg: '参数错误' } }
            ];

            for (const testCase of testCases) {
                try {
                    const result = await processResponse(testCase.response);
                    results.push(`✅ ${testCase.name}: 成功处理`);
                    console.log(`${testCase.name} 成功:`, result);
                } catch (error) {
                    results.push(`❌ ${testCase.name}: 错误 - ${error}`);
                    console.log(`${testCase.name} 失败:`, error);
                }
            }

            resultDiv.innerHTML = `
                <h3>测试结果:</h3>
                <ul>
                    ${results.map(result => `<li>${result}</li>`).join('')}
                </ul>
                <h3>修复说明:</h3>
                <p>修复前: <code>code !== 200</code> 会拒绝 2000 状态码</p>
                <p>修复后: <code>code !== 200 && code !== 2000</code> 接受 200 和 2000 状态码</p>
            `;
        }
    </script>
</body>
</html>
