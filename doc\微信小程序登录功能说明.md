# 微信小程序登录功能说明

## 概述

本文档说明了植物语+项目中微信小程序登录功能的实现，包括后端API接口、数据库设计、配置方法等。

## 功能特性

- ✅ 微信小程序一键登录
- ✅ 自动创建用户账号
- ✅ JWT Token认证
- ✅ 用户信息管理
- ✅ 登录日志记录
- ✅ 错误处理和日志记录

## 后端实现

### 1. 应用结构

```
backend/apps/wx/
├── __init__.py
├── apps.py          # 应用配置
├── models.py        # 数据模型
├── serializers.py   # 序列化器
├── views.py         # 视图函数
├── urls.py          # URL路由
├── utils.py         # 工具函数
├── admin.py         # 管理后台
└── migrations/      # 数据库迁移文件
```

### 2. 数据模型

#### WxUser (微信用户模型)
- `user`: 关联系统用户 (OneToOne)
- `openid`: 微信用户唯一标识
- `unionid`: 微信开放平台标识
- `session_key`: 微信会话密钥
- `nickname`: 微信昵称
- `avatar_url`: 头像URL
- `gender`: 性别 (0-未知, 1-男, 2-女)
- `country/province/city`: 地理位置信息
- `is_active`: 是否激活
- `last_login_time`: 最后登录时间

#### WxLoginLog (登录日志模型)
- `wx_user`: 关联微信用户
- `login_time`: 登录时间
- `ip_address`: IP地址
- `user_agent`: 用户代理
- `login_result`: 登录结果
- `error_message`: 错误信息

### 3. API接口

#### 登录接口
- **URL**: `POST /api/auth/login/`
- **参数**:
  ```json
  {
    "code": "微信登录凭证",
    "userInfo": {
      "nickName": "用户昵称",
      "avatarUrl": "头像URL",
      "gender": 0
    }
  }
  ```
- **返回**:
  ```json
  {
    "code": 2000,
    "msg": "登录成功",
    "data": {
      "token": "JWT访问令牌",
      "refresh_token": "刷新令牌",
      "user": {
        "id": 1,
        "openid": "微信openid",
        "nickname": "用户昵称",
        "avatar_url": "头像URL",
        "gender": 0,
        "is_active": true,
        "username": "系统用户名",
        "name": "用户姓名"
      },
      "is_new_user": true
    }
  }
  ```

#### 获取用户信息
- **URL**: `GET /api/auth/user/`
- **认证**: 需要JWT Token
- **返回**: 用户详细信息

#### 更新用户信息
- **URL**: `POST /api/auth/update/`
- **认证**: 需要JWT Token
- **参数**: 用户信息字段

#### 退出登录
- **URL**: `POST /api/auth/logout/`
- **认证**: 需要JWT Token

## 配置说明

### 1. 微信小程序配置

在 `backend/conf/env.py` 中添加微信小程序配置：

```python
# 微信小程序配置
WX_MINIPROGRAM_APP_ID = 'your_miniprogram_app_id'
WX_MINIPROGRAM_APP_SECRET = 'your_miniprogram_app_secret'
```

### 2. Django设置

已在 `backend/application/settings.py` 中完成以下配置：
- 添加 `apps.wx` 到 `INSTALLED_APPS`
- 配置微信小程序参数
- URL路由配置

### 3. 数据库迁移

```bash
# 创建迁移文件
python backend/manage.py makemigrations wx

# 执行迁移
python backend/manage.py migrate
```

## 前端集成

### 1. 登录流程

前端登录流程已在 `app/pages/login.vue` 中实现：

1. 调用 `uni.login()` 获取微信登录凭证code
2. 构建用户信息对象
3. 调用后端登录接口 `/api/auth/login`
4. 保存返回的token和用户信息
5. 跳转到主页面

### 2. API调用

使用 `app/api/auth.js` 中的 `wxLogin` 函数：

```javascript
import { wxLogin } from '@/api/auth'

// 微信登录
wxLogin(code, userInfo).then(res => {
  if (res.code === 200) {
    // 登录成功处理
    const token = res.data.token
    const user = res.data.user
    // 保存token和用户信息
  }
})
```

## 安全考虑

1. **Token安全**: 使用JWT Token进行身份认证，设置合理的过期时间
2. **数据验证**: 对所有输入数据进行严格验证
3. **错误处理**: 不暴露敏感的系统信息
4. **日志记录**: 记录所有登录尝试和错误信息
5. **IP限制**: 可根据需要添加IP访问限制

## 错误处理

### 常见错误码

- `40013`: 不合法的AppID
- `40029`: 不合法的code
- `45011`: API调用太频繁
- `40226`: 高风险用户，登录拦截

### 错误日志

所有错误都会记录到：
- Django日志系统
- WxLoginLog数据表
- 控制台输出（开发环境）

## 管理后台

在Django管理后台中可以查看和管理：
- 微信用户信息
- 登录日志
- 用户状态管理

访问地址：`/admin/wx/`

## 测试验证

### 运行测试
```bash
cd backend
python manage.py test apps.wx.tests
```

### 测试结果
✅ 所有8个测试用例全部通过：
- test_code2session_error: 测试微信API错误响应
- test_code2session_success: 测试微信API成功调用
- test_wx_login_existing_user: 测试已存在用户的微信登录
- test_wx_login_invalid_data: 测试无效数据的登录请求
- test_wx_login_new_user: 测试新用户微信登录
- test_create_default_user_info: 测试创建默认用户信息
- test_create_default_user_info_empty: 测试创建默认用户信息（空数据）
- test_generate_username_from_openid: 测试从openid生成用户名

### 系统检查
```bash
python manage.py check
# System check identified no issues (0 silenced).
```

### 测试覆盖
- 微信登录API测试
- 用户信息管理测试
- 工具函数测试
- 微信API客户端测试
- 数据库模型测试
- 错误处理测试

## 测试建议

1. **单元测试**: 为关键功能编写单元测试
2. **集成测试**: 测试完整的登录流程
3. **压力测试**: 测试高并发登录场景
4. **安全测试**: 测试各种攻击场景

## 部署注意事项

1. **环境变量**: 生产环境中使用环境变量设置微信配置
2. **HTTPS**: 生产环境必须使用HTTPS
3. **数据库**: 确保数据库连接稳定
4. **日志**: 配置合适的日志级别和存储

## 后续优化

1. **缓存优化**: 添加Redis缓存提高性能
2. **限流**: 添加API访问限流
3. **监控**: 添加系统监控和告警
4. **数据分析**: 添加用户行为分析

---

**注意**: 请确保在生产环境中正确配置微信小程序的AppID和AppSecret，并遵循微信官方的安全规范。
