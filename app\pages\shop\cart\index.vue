<template>
  <view class="cart-container">
    <!-- 购物车列表 -->
    <scroll-view scroll-y class="cart-content" v-if="cartList.length > 0">
      <view class="cart-item" v-for="(item, index) in cartList" :key="index">
        <view class="item-checkbox">
          <uni-icons 
            :type="item.selected ? 'checkbox-filled' : 'checkbox'" 
            size="20" 
            :color="item.selected ? '#007aff' : '#ddd'"
            @click="toggleSelect(index)"
          ></uni-icons>
        </view>
        
        <image class="item-image" :src="getFullImageUrl(item.goodsImage)" mode="aspectFill" @click="goToGoodsDetail(item)"></image>
        
        <view class="item-info">
          <text class="item-name">{{ item.goodsName }}</text>
          <text class="item-spec" v-if="item.skuName">{{ item.skuName }}</text>
          <view class="item-price-row">
            <text class="item-price">¥{{ formatPrice(item.goodsPrice) }}</text>
            <view class="quantity-controls">
              <view class="quantity-btn" @click="decreaseQuantity(index)">
                <uni-icons type="minus" size="14" color="#666"></uni-icons>
              </view>
              <text class="quantity-text">{{ item.quantity }}</text>
              <view class="quantity-btn" @click="increaseQuantity(index)">
                <uni-icons type="plus" size="14" color="#666"></uni-icons>
              </view>
            </view>
          </view>
        </view>
        
        <view class="item-actions">
          <uni-icons type="trash" size="18" color="#999" @click="removeItem(index)"></uni-icons>
        </view>
      </view>
    </scroll-view>

    <!-- 空购物车 -->
    <view class="empty-cart" v-else>
      <uni-icons type="cart" size="80" color="#ccc"></uni-icons>
      <text class="empty-text">购物车是空的</text>
      <text class="empty-tip">快去挑选心仪的商品吧</text>
      <button class="go-shopping-btn" @click="goShopping">去逛逛</button>
    </view>

    <!-- 底部操作栏 -->
    <view class="cart-footer" v-if="cartList.length > 0">
      <view class="footer-left">
        <view class="select-all" @click="toggleSelectAll">
          <uni-icons
            :type="isAllSelected ? 'checkbox-filled' : 'checkbox'"
            size="20"
            :color="isAllSelected ? '#007aff' : '#ddd'"
          ></uni-icons>
          <text>全选</text>
        </view>
        <view class="total-info">
          <text class="total-text">合计：</text>
          <text class="total-price">¥{{ formatPrice(totalPrice) }}</text>
        </view>
      </view>

      <view class="footer-right">
        <button class="clear-btn" @click="clearAllCart">清空</button>
        <button class="checkout-btn" :disabled="selectedCount === 0" @click="checkout">
          结算({{ selectedCount }})
        </button>
      </view>
    </view>

    <!-- 推荐商品 -->
    <view class="recommend-section" v-if="cartList.length > 0 && recommendGoods.length > 0">
      <view class="recommend-title">为你推荐</view>
      <scroll-view scroll-x class="recommend-scroll">
        <view class="recommend-list">
          <view 
            class="recommend-item" 
            v-for="goods in recommendGoods" 
            :key="goods.goodsId"
            @click="goToGoodsDetail(goods)"
          >
            <image class="recommend-image" :src="goods.mainPic" mode="aspectFill"></image>
            <text class="recommend-name">{{ goods.goodsName }}</text>
            <text class="recommend-price">¥{{ formatPrice(goods.sellPrice) }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import { getRecommendGoods, getCartList, updateCartQuantity, removeFromCart, clearCart } from '@/api/shop.js'
import { formatPrice } from '@/utils/shop.js'
import config from '@/config.js'

export default {
  data() {
    return {
      cartList: [],
      recommendGoods: [],
      loading: false
    }
  },
  computed: {
    // 是否全选
    isAllSelected() {
      return this.cartList.length > 0 && this.cartList.every(item => item.selected)
    },
    
    // 选中的商品数量
    selectedCount() {
      return this.cartList.filter(item => item.selected).length
    },
    
    // 总价
    totalPrice() {
      return this.cartList
        .filter(item => item.selected)
        .reduce((total, item) => total + (item.goodsPrice * item.quantity), 0)
    }
  },
  onLoad() {
    // 页面首次加载时，清理可能的缓存问题
    this.initCartData()
  },

  onShow() {
    // 每次显示页面时重新加载数据
    this.loadCartData()
    this.loadRecommendGoods()
  },
  methods: {
    // 初始化购物车数据
    initCartData() {
      console.log('初始化购物车数据')
      // 清空当前数据，确保从最新源加载
      this.cartList = []
      // 立即加载数据
      this.loadCartData()
    },

    // 加载购物车数据
    async loadCartData() {
      if (this.loading) return

      this.loading = true
      try {
        // 优先从API加载数据
        const res = await getCartList()
        console.log('购物车API加载结果:', res)

        if (res && res.code === 200 && res.data) {
          this.cartList = res.data.map(item => ({
            ...item,
            selected: item.selected === '1' || item.selected === true
          }))

          // 同步到本地存储
          this.saveCartToLocal()
          console.log('从API加载购物车数据成功:', this.cartList)
        } else {
          // API返回异常，使用本地数据
          console.warn('API返回异常，使用本地数据:', res)
          this.loadCartFromLocal()
        }
      } catch (error) {
        console.error('API加载购物车失败，使用本地数据:', error)
        // API调用失败，从本地存储加载
        this.loadCartFromLocal()
      } finally {
        this.loading = false
      }
    },

    // 从本地存储加载购物车数据
    loadCartFromLocal() {
      try {
        const localCart = uni.getStorageSync('cartList') || []
        this.cartList = localCart.map(item => ({
          ...item,
          selected: item.selected === true || item.selected === '1'
        }))
        console.log('从本地存储加载购物车数据:', this.cartList)
      } catch (error) {
        console.error('从本地存储加载购物车失败:', error)
        this.cartList = []
      }
    },

    // 保存购物车数据到本地存储
    saveCartToLocal() {
      try {
        uni.setStorageSync('cartList', this.cartList)
        console.log('购物车数据已保存到本地存储')
      } catch (error) {
        console.error('保存购物车到本地存储失败:', error)
      }
    },

    // 加载推荐商品
    async loadRecommendGoods() {
      try {
        const res = await getRecommendGoods({ pageSize: 10 })
        this.recommendGoods = res.rows || []
      } catch (error) {
        console.error('加载推荐商品失败:', error)
      }
    },

    // 切换选中状态
    toggleSelect(index) {
      this.cartList[index].selected = !this.cartList[index].selected
      // 保存选中状态到本地存储
      this.saveCartToLocal()
    },

    // 全选/取消全选
    toggleSelectAll() {
      const selectAll = !this.isAllSelected
      this.cartList.forEach(item => {
        item.selected = selectAll
      })
      // 保存选中状态到本地存储
      this.saveCartToLocal()
    },

    // 增加数量
    async increaseQuantity(index) {
      const item = this.cartList[index]
      const oldQuantity = item.quantity

      // 立即更新本地数据
      item.quantity++
      this.saveCartToLocal()

      // 异步更新API
      try {
        await this.updateCartItem(item.cartId, item.quantity)
      } catch (error) {
        // API更新失败，恢复数量
        item.quantity = oldQuantity
        this.saveCartToLocal()
      }
    },

    // 减少数量
    async decreaseQuantity(index) {
      const item = this.cartList[index]
      if (item.quantity > 1) {
        const oldQuantity = item.quantity

        // 立即更新本地数据
        item.quantity--
        this.saveCartToLocal()

        // 异步更新API
        try {
          await this.updateCartItem(item.cartId, item.quantity)
        } catch (error) {
          // API更新失败，恢复数量
          item.quantity = oldQuantity
          this.saveCartToLocal()
        }
      }
    },

    // 更新购物车商品
    async updateCartItem(cartId, quantity) {
      try {
        const result = await updateCartQuantity(cartId, quantity)
        console.log('更新购物车数量API结果:', result)

        if (result.code !== 200) {
          throw new Error(result.msg || '更新失败')
        }
      } catch (error) {
        console.error('更新购物车失败:', error)
        uni.showToast({
          title: '更新失败',
          icon: 'none'
        })
        throw error // 重新抛出错误，让调用方处理
      }
    },

    // 删除商品
    removeItem(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除这个商品吗？',
        success: async (res) => {
          if (res.confirm) {
            const item = this.cartList[index]
            console.log('准备删除商品:', item)

            // 检查cartId是否存在
            if (!item.cartId) {
              console.error('商品缺少cartId:', item)
              uni.showToast({
                title: '删除失败：商品ID无效',
                icon: 'none'
              })
              return
            }

            let removedItem = null

            try {
              console.log('准备删除商品，cartId:', item.cartId)

              // 先从本地列表中移除（立即响应用户操作）
              removedItem = this.cartList.splice(index, 1)[0]

              // 立即保存到本地存储
              this.saveCartToLocal()

              // 显示删除成功提示
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })

              // 异步调用API删除（不阻塞用户操作）
              try {
                console.log('准备调用删除API，参数:', {
                  cartId: item.cartId,
                  cartIdType: typeof item.cartId,
                  requestData: [item.cartId],
                  item: item
                })

                // 确保 cartId 是数字类型
                const cartIdNumber = parseInt(item.cartId)
                console.log('转换后的cartId:', cartIdNumber, '类型:', typeof cartIdNumber)

                const result = await removeFromCart([cartIdNumber])
                console.log('删除API调用成功，返回结果:', result)

                if (result && result.code === 200) {
                  console.log('数据库删除成功')
                } else {
                  console.warn('API删除返回异常:', result)
                  // 显示警告但不影响本地删除
                  uni.showToast({
                    title: '服务器同步失败',
                    icon: 'none',
                    duration: 2000
                  })
                }
              } catch (apiError) {
                console.error('API删除失败详细信息:', apiError)
                console.warn('API删除失败，但本地删除已生效')

                // 显示网络错误提示
                uni.showToast({
                  title: '网络错误，本地删除已生效',
                  icon: 'none',
                  duration: 2000
                })
              }

            } catch (error) {
              console.error('删除操作失败:', error)

              // 如果本地删除也失败了，恢复数据
              if (removedItem) {
                this.cartList.splice(index, 0, removedItem)
                this.saveCartToLocal()
              }

              uni.showToast({
                title: '删除失败',
                icon: 'none',
                duration: 3000
              })
            }
          }
        }
      })
    },

    // 清空购物车
    clearAllCart() {
      if (this.cartList.length === 0) return

      uni.showModal({
        title: '提示',
        content: '确定要清空购物车吗？',
        success: async (res) => {
          if (res.confirm) {
            // 备份当前数据，以防操作失败需要恢复
            const backupCartList = [...this.cartList]

            try {
              // 立即清空本地数据
              this.cartList = []
              this.saveCartToLocal()

              uni.showToast({
                title: '清空成功',
                icon: 'success'
              })

              // 异步调用API清空
              try {
                const result = await clearCart()
                console.log('清空购物车API调用成功:', result)
              } catch (apiError) {
                console.warn('API清空失败，但本地清空已生效:', apiError)
              }

            } catch (error) {
              console.error('清空购物车失败:', error)

              // 恢复数据
              this.cartList = backupCartList
              this.saveCartToLocal()

              uni.showToast({
                title: '清空失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 结算
    checkout() {
      if (this.selectedCount === 0) {
        uni.showToast({
          title: '请选择要结算的商品',
          icon: 'none'
        })
        return
      }

      const selectedItems = this.cartList.filter(item => item.selected)

      // 跳转到订单确认页面，传递选中的商品数据
      const goodsData = encodeURIComponent(JSON.stringify(selectedItems))
      uni.navigateTo({
        url: `/pages/shop/order/confirm?goodsData=${goodsData}`
      })
    },

    // 去购物
    goShopping() {
      uni.switchTab({
        url: '/pages/index'
      })
    },

    // 跳转到商品详情
    goToGoodsDetail(item) {
      const goodsId = item.goodsId || item.id
      uni.navigateTo({
        url: `/pages/shop/goods/detail?goodsId=${goodsId}`
      })
    },

    // 格式化价格
    formatPrice,

    // 获取完整图片URL
    getFullImageUrl(imagePath) {
      if (!imagePath) return '/static/images/default-goods.png'
      if (imagePath.startsWith('http')) return imagePath
      return config.baseUrl + imagePath
    }
  }
}
</script>

<style lang="scss" scoped>
.cart-container {
  height: 100vh;
  background-color: #f5f5f7;
  display: flex;
  flex-direction: column;
}

.cart-content {
  flex: 1;
  padding: 20rpx;
}

.cart-item {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  
  .item-checkbox {
    margin-right: 20rpx;
  }
  
  .item-image {
    width: 160rpx;
    height: 160rpx;
    border-radius: 8rpx;
    margin-right: 20rpx;
  }
  
  .item-info {
    flex: 1;
    
    .item-name {
      font-size: 30rpx;
      color: #333;
      font-weight: bold;
      display: block;
      margin-bottom: 10rpx;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .item-spec {
      font-size: 24rpx;
      color: #999;
      display: block;
      margin-bottom: 15rpx;
    }
    
    .item-price-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .item-price {
        font-size: 32rpx;
        color: #ff6700;
        font-weight: bold;
      }
      
      .quantity-controls {
        display: flex;
        align-items: center;
        border: 1px solid #ddd;
        border-radius: 6rpx;
        
        .quantity-btn {
          width: 60rpx;
          height: 60rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f5f5;
          
          &:first-child {
            border-radius: 6rpx 0 0 6rpx;
          }
          
          &:last-child {
            border-radius: 0 6rpx 6rpx 0;
          }
        }
        
        .quantity-text {
          width: 80rpx;
          text-align: center;
          font-size: 28rpx;
          color: #333;
          background-color: #fff;
        }
      }
    }
  }
  
  .item-actions {
    margin-left: 20rpx;
  }
}

.empty-cart {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  
  .empty-text {
    font-size: 32rpx;
    color: #666;
    margin: 30rpx 0 15rpx;
  }
  
  .empty-tip {
    font-size: 26rpx;
    color: #999;
    margin-bottom: 50rpx;
  }
  
  .go-shopping-btn {
    background-color: #007aff;
    color: #fff;
    border-radius: 50rpx;
    padding: 20rpx 60rpx;
    font-size: 28rpx;
    border: none;
  }
}

.cart-footer {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .footer-left {
    display: flex;
    align-items: center;
    
    .select-all {
      display: flex;
      align-items: center;
      margin-right: 40rpx;
      
      text {
        margin-left: 10rpx;
        font-size: 28rpx;
        color: #333;
      }
    }
    
    .total-info {
      .total-text {
        font-size: 28rpx;
        color: #666;
      }
      
      .total-price {
        font-size: 36rpx;
        color: #ff6700;
        font-weight: bold;
      }
    }
  }
  
  .footer-right {
    display: flex;
    gap: 20rpx;

    .clear-btn {
      background-color: #999;
      color: #fff;
      border-radius: 50rpx;
      padding: 20rpx 30rpx;
      font-size: 28rpx;
      border: none;
    }

    .checkout-btn {
      background-color: #ff6700;
      color: #fff;
      border-radius: 50rpx;
      padding: 20rpx 40rpx;
      font-size: 28rpx;
      border: none;

      &:disabled {
        background-color: #ccc;
      }
    }
  }
}

.recommend-section {
  background-color: #fff;
  margin-top: 20rpx;
  
  .recommend-title {
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
    padding: 30rpx;
    border-bottom: 1px solid #eee;
  }
  
  .recommend-scroll {
    padding: 20rpx 0;
    
    .recommend-list {
      display: flex;
      padding: 0 20rpx;
      
      .recommend-item {
        width: 200rpx;
        margin-right: 20rpx;
        
        .recommend-image {
          width: 200rpx;
          height: 200rpx;
          border-radius: 8rpx;
          margin-bottom: 10rpx;
        }
        
        .recommend-name {
          font-size: 24rpx;
          color: #333;
          display: block;
          margin-bottom: 8rpx;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .recommend-price {
          font-size: 26rpx;
          color: #ff6700;
          font-weight: bold;
        }
      }
    }
  }
}
</style>
