/**
 * 应用状态管理
 */
import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
	state: () => ({
		// 系统信息
		systemInfo: null,
		// 网络状态
		networkType: 'unknown',
		// 应用版本信息
		appInfo: {
			version: '1.0.0',
			buildNumber: '100'
		},
		// 全局加载状态
		globalLoading: false,
		// 主题设置
		theme: {
			mode: 'light', // light | dark
			primaryColor: '#4CAF50',
			accentColor: '#FF9800'
		},
		// 页面配置
		pageConfig: {
			enablePullRefresh: true,
			enableReachBottom: true,
			backgroundColor: '#f8f8f8'
		},
		// 缓存配置
		cache: {
			maxSize: 50 * 1024 * 1024, // 50MB
			expireTime: 7 * 24 * 60 * 60 * 1000 // 7天
		},
		// 消息通知
		notifications: [],
		// 错误日志
		errorLogs: []
	}),
	
	getters: {
		// 是否是暗色主题
		isDarkMode: (state) => {
			return state.theme.mode === 'dark'
		},
		
		// 获取状态栏高度
		statusBarHeight: (state) => {
			return state.systemInfo?.statusBarHeight || 0
		},
		
		// 获取导航栏高度
		navigationBarHeight: (state) => {
			// 小程序导航栏高度通常是44px
			return 44
		},
		
		// 获取安全区域
		safeArea: (state) => {
			return state.systemInfo?.safeArea || {}
		},
		
		// 是否是iPhone X系列
		isIPhoneX: (state) => {
			if (!state.systemInfo) return false
			const { model, safeArea, screenHeight } = state.systemInfo
			return model.includes('iPhone') && safeArea.bottom < screenHeight
		},
		
		// 获取未读通知数量
		unreadNotificationsCount: (state) => {
			return state.notifications.filter(n => !n.read).length
		}
	},
	
	actions: {
		// 初始化应用
		async initApp() {
			try {
				await Promise.all([
					this.getSystemInfo(),
					this.getNetworkType(),
					this.initTheme(),
					this.loadNotifications()
				])
			} catch (error) {
				console.error('初始化应用失败:', error)
				this.logError('initApp', error)
			}
		},
		
		// 获取系统信息
		async getSystemInfo() {
			return new Promise((resolve) => {
				uni.getSystemInfo({
					success: (res) => {
						this.systemInfo = res
						resolve(res)
					},
					fail: (error) => {
						console.error('获取系统信息失败:', error)
						resolve(null)
					}
				})
			})
		},
		
		// 获取网络状态
		async getNetworkType() {
			return new Promise((resolve) => {
				uni.getNetworkType({
					success: (res) => {
						this.networkType = res.networkType
						resolve(res.networkType)
					},
					fail: (error) => {
						console.error('获取网络状态失败:', error)
						resolve('unknown')
					}
				})
			})
		},
		
		// 监听网络状态变化
		watchNetworkStatus() {
			uni.onNetworkStatusChange((res) => {
				this.networkType = res.networkType
				
				if (!res.isConnected) {
					uni.showToast({
						title: '网络连接已断开',
						icon: 'none'
					})
				}
			})
		},
		
		// 初始化主题
		initTheme() {
			try {
				const savedTheme = uni.getStorageSync('appTheme')
				if (savedTheme) {
					this.theme = { ...this.theme, ...savedTheme }
				}
				
				// 应用主题
				this.applyTheme()
			} catch (error) {
				console.error('初始化主题失败:', error)
			}
		},
		
		// 应用主题
		applyTheme() {
			// 设置导航栏样式
			uni.setNavigationBarColor({
				frontColor: this.isDarkMode ? '#ffffff' : '#000000',
				backgroundColor: this.theme.primaryColor
			})
			
			// 设置tabBar样式
			uni.setTabBarStyle({
				color: this.isDarkMode ? '#999999' : '#7A7E83',
				selectedColor: this.theme.primaryColor,
				backgroundColor: this.isDarkMode ? '#1f1f1f' : '#ffffff',
				borderStyle: this.isDarkMode ? 'white' : 'black'
			})
		},
		
		// 切换主题
		toggleTheme() {
			this.theme.mode = this.isDarkMode ? 'light' : 'dark'
			this.saveTheme()
			this.applyTheme()
		},
		
		// 设置主题颜色
		setThemeColor(primaryColor, accentColor) {
			this.theme.primaryColor = primaryColor
			if (accentColor) {
				this.theme.accentColor = accentColor
			}
			this.saveTheme()
			this.applyTheme()
		},
		
		// 保存主题设置
		saveTheme() {
			uni.setStorageSync('appTheme', this.theme)
		},
		
		// 设置全局加载状态
		setGlobalLoading(loading) {
			this.globalLoading = loading
			
			if (loading) {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
			} else {
				uni.hideLoading()
			}
		},
		
		// 显示消息提示
		showToast(title, icon = 'none', duration = 2000) {
			uni.showToast({
				title,
				icon,
				duration
			})
		},
		
		// 显示确认对话框
		showConfirm(title, content = '') {
			return new Promise((resolve) => {
				uni.showModal({
					title,
					content,
					success: (res) => {
						resolve(res.confirm)
					},
					fail: () => {
						resolve(false)
					}
				})
			})
		},
		
		// 添加通知
		addNotification(notification) {
			const newNotification = {
				id: Date.now(),
				read: false,
				createTime: new Date(),
				...notification
			}
			
			this.notifications.unshift(newNotification)
			
			// 限制通知数量
			if (this.notifications.length > 100) {
				this.notifications = this.notifications.slice(0, 100)
			}
			
			this.saveNotifications()
		},
		
		// 标记通知为已读
		markNotificationRead(id) {
			const notification = this.notifications.find(n => n.id === id)
			if (notification) {
				notification.read = true
				this.saveNotifications()
			}
		},
		
		// 清除所有通知
		clearNotifications() {
			this.notifications = []
			this.saveNotifications()
		},
		
		// 加载通知
		loadNotifications() {
			try {
				const notifications = uni.getStorageSync('appNotifications')
				if (notifications) {
					this.notifications = notifications
				}
			} catch (error) {
				console.error('加载通知失败:', error)
			}
		},
		
		// 保存通知
		saveNotifications() {
			uni.setStorageSync('appNotifications', this.notifications)
		},
		
		// 记录错误日志
		logError(action, error) {
			const errorLog = {
				id: Date.now(),
				action,
				error: error.message || error,
				stack: error.stack,
				timestamp: new Date(),
				systemInfo: this.systemInfo
			}
			
			this.errorLogs.unshift(errorLog)
			
			// 限制错误日志数量
			if (this.errorLogs.length > 50) {
				this.errorLogs = this.errorLogs.slice(0, 50)
			}
			
			// 保存到本地
			uni.setStorageSync('errorLogs', this.errorLogs)
		},
		
		// 清除错误日志
		clearErrorLogs() {
			this.errorLogs = []
			uni.removeStorageSync('errorLogs')
		},
		
		// 检查应用更新
		async checkUpdate() {
			// #ifdef MP-WEIXIN
			const updateManager = uni.getUpdateManager()
			
			updateManager.onCheckForUpdate((res) => {
				if (res.hasUpdate) {
					this.addNotification({
						type: 'update',
						title: '发现新版本',
						content: '有新版本可用，请更新后使用'
					})
				}
			})
			
			updateManager.onUpdateReady(() => {
				uni.showModal({
					title: '更新提示',
					content: '新版本已经准备好，是否重启应用？',
					success: (res) => {
						if (res.confirm) {
							updateManager.applyUpdate()
						}
					}
				})
			})
			
			updateManager.onUpdateFailed(() => {
				uni.showToast({
					title: '更新失败',
					icon: 'none'
				})
			})
			// #endif
		}
	}
})
