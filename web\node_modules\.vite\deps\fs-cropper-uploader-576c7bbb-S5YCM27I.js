import {
  <PERSON><PERSON>,
  he
} from "./chunk-JWPUVG35.js";
import "./chunk-7AOKHL6I.js";
import "./chunk-TJTCSOX4.js";
import "./chunk-3XL3ODE2.js";
import "./chunk-YFT6OQ5R.js";
import {
  B
} from "./chunk-TGOZU523.js";
import "./chunk-IUY2MIZJ.js";
import "./chunk-LK7GAOJV.js";
import "./chunk-44FYVRJW.js";
import {
  Fragment,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createVNode,
  defineComponent,
  getCurrentInstance,
  mergeProps,
  normalizeClass,
  openBlock,
  reactive,
  ref,
  renderList,
  resolveComponent,
  resolveDynamicComponent,
  watch,
  withCtx
} from "./chunk-WEJJSMSC.js";
import "./chunk-LK32TJAX.js";

// node_modules/@fast-crud/fast-extends/dist/fs-cropper-uploader-576c7bbb.mjs
var re = defineComponent({
  name: "FsCropperUploader",
  props: {
    /**
     * 初始图片url,或者是数组
     */
    modelValue: {
      type: [String, Object, Array]
    },
    img: {},
    /**
     * 上传后端类型，[form, cos, qiniu , alioss]
     */
    type: {
      type: String
    },
    /**
     *  上传提示
     */
    uploadTip: {
      type: String
    },
    /**
     * 对话框标题
     */
    title: String,
    /**
     * cropper的高度，默认为浏览器可视窗口高度的40%，最小270
     */
    cropperHeight: {
      type: [String, Number]
    },
    /**
     * 对话框宽度，默认50%
     */
    dialogWidth: {
      type: [String, Number],
      default: "50%"
    },
    /**
     * 图片大小限制，单位MB
     */
    maxSize: {
      type: Number,
      default: 5
    },
    /**
     * 图片数量限制,0为不限制
     */
    limit: {
      type: Number,
      default: 1
    },
    /**
     * 可接收的文件后缀
     */
    accept: {
      type: String,
      default: ".jpg, .jpeg, .png, .gif, .webp"
    },
    /**
     * [cropperjs的参数](https://github.com/fengyuanchen/cropperjs)
     */
    cropper: {
      type: Object
    },
    /**
     * FsUploaderXXX的配置，会临时覆盖全局上传配置参数
     */
    uploader: {
      type: Object
    },
    /**
     * 压缩质量
     */
    compressQuality: {
      type: Number,
      default: 0.8
    },
    /**
     *  构建下载url方法,不影响提交的value
     */
    buildUrl: {
      type: Function,
      default: async function(e) {
        return typeof e == "object" ? e.url : e;
      }
    },
    /**
     * 返回值类型
     * 支持：`[url,key,object]`
     */
    valueType: {
      type: String,
      // url ,key, object
      default: "url"
    },
    /**
     * 是否禁用
     */
    disabled: {}
  },
  emits: ["update:modelValue", "change", "ready"],
  setup(e, a) {
    const { ui: b } = B(), g = ref(), N = ref(), U = ref(), s = ref([]), u = b.formItem.injectFormItemContext();
    let y = e.modelValue;
    i(e.modelValue);
    async function i(o) {
      const t = [];
      if (o == null || o === "") {
        s.value = t;
        return;
      }
      if (typeof o == "string")
        t.push({ url: await e.buildUrl(o), value: o, status: "done" });
      else if (Array.isArray(o))
        for (const r of o)
          t.push({ url: await e.buildUrl(r), value: r, status: "done" });
      else if (typeof o == "object")
        t.push({ url: await e.buildUrl(o), value: o, status: "done" });
      else
        for (const r of o)
          t.push({ url: await e.buildUrl(r), value: r, status: "done" });
      s.value = t;
    }
    function v() {
      e.disabled || (U.value = void 0, g.value.clear(), g.value.open());
    }
    function C(o) {
      s.value.splice(o, 1), j();
    }
    function B2() {
      const o = s.value;
      if (o && o.length > 0) {
        for (const t of o)
          if (t.status === "uploading")
            return true;
      }
      return false;
    }
    async function _(o) {
      const t = o.blob, r = o.dataUrl, T = o.file.name, L = new File([t], T, { type: t.type }), p = reactive({
        url: void 0,
        dataUrl: r,
        status: "uploading",
        progress: 0
      }), Q = (l) => {
        p.progress = l.percent;
      }, $ = (l) => {
        p.status = "error", p.message = "文件上传出错:" + l.message, console.error(l);
      }, W = {
        file: L,
        onProgress: Q,
        onError: $,
        fileName: T
      };
      s.value.push(p);
      try {
        const l = await R(W);
        let V = l;
        e.valueType !== "object" && (V = l[e.valueType]), p.url = await e.buildUrl(V), p.value = V, p.status = "done", j();
      } catch (l) {
        $(l);
      }
    }
    async function R(o) {
      o.options = e.uploader || {};
      const { getUploaderImpl: t } = Ct();
      let r = await t(o.options.type);
      if (r == null)
        throw new Error("Sorry，The component is not ready yet");
      return await (r == null ? void 0 : r.upload(o));
    }
    async function j() {
      const o = [];
      for (const r of s.value)
        typeof r == "string" ? o.push(r) : o.push(r.value);
      let t = o;
      e.limit === 1 && (t = o && o.length > 0 ? o[0] : void 0), y = t, a.emit("update:modelValue", t), await u.onChange(), await u.onBlur();
    }
    function F(o) {
      return o.dataUrl ? o.dataUrl : o.url;
    }
    const P = ref(false), k = ref();
    function A(o) {
      P.value = true, k.value = F(o);
    }
    function D() {
      P.value = false, k.value = null;
    }
    watch(
      () => e.modelValue,
      async (o) => {
        a.emit("change", o), o !== y && await i(o);
      }
    );
    const O = getCurrentInstance();
    function q(o) {
      a.emit("ready", {
        uploaderRef: O,
        ...o
      });
    }
    const H = computed(() => ({
      ...e
    }));
    return {
      ui: b,
      cropperRef: g,
      uploaderImplRef: N,
      indexRef: U,
      listRef: s,
      addNewImage: v,
      hasUploading: B2,
      cropComplete: _,
      doUpload: R,
      removeImage: C,
      getImageSrc: F,
      previewUrl: k,
      previewVisible: P,
      preview: A,
      closePreview: D,
      doReady: q,
      computedProps: H
    };
  }
});
var ie = { class: "image-list" };
var ne = { class: "image-slot" };
var se = { class: "delete" };
var le = {
  key: 0,
  class: "status-uploading"
};
var ae = {
  key: 1,
  class: "status-done"
};
var ue = { class: "fs-cropper-preview-content" };
var pe = ["src"];
function ce(e, a, b, g, N, U) {
  const s = resolveComponent("fs-loading"), u = resolveComponent("fs-icon"), y = resolveComponent("fs-cropper");
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(["fs-cropper-uploader", { "is-disabled": e.computedProps.disabled }])
  }, [
    createBaseVNode("div", ie, [
      (openBlock(), createBlock(resolveDynamicComponent(e.ui.imageGroup.name), null, {
        default: withCtx(() => [
          (openBlock(true), createElementBlock(Fragment, null, renderList(e.listRef, (i, v) => (openBlock(), createElementBlock("div", {
            key: v,
            class: "image-item"
          }, [
            (openBlock(), createBlock(resolveDynamicComponent(e.ui.image.name), mergeProps({
              class: "image",
              src: e.getImageSrc(i),
              ref_for: true
            }, e.computedProps.img), {
              placeholder: withCtx(() => [
                createBaseVNode("div", ne, [
                  createVNode(s, { loading: true })
                ])
              ]),
              _: 2
            }, 1040, ["src"])),
            createBaseVNode("div", se, [
              e.computedProps.disabled ? createCommentVNode("", true) : (openBlock(), createBlock(u, {
                key: 0,
                icon: e.ui.icons.remove,
                onClick: (C) => e.removeImage(v)
              }, null, 8, ["icon", "onClick"])),
              createVNode(u, {
                icon: e.ui.icons.search,
                onClick: (C) => e.preview(i)
              }, null, 8, ["icon", "onClick"])
            ]),
            i.status === "uploading" ? (openBlock(), createElementBlock("div", le, [
              (openBlock(), createBlock(resolveDynamicComponent(e.ui.progress.name), {
                type: "circle",
                percentage: i.progress,
                width: 70
              }, null, 8, ["percentage"]))
            ])) : i.status === "done" ? (openBlock(), createElementBlock("div", ae, [
              createVNode(u, {
                icon: e.ui.icons.check,
                class: "status-down-icon"
              }, null, 8, ["icon"])
            ])) : createCommentVNode("", true)
          ]))), 128)),
          e.computedProps.limit <= 0 || e.computedProps.limit > e.listRef.length ? (openBlock(), createElementBlock("div", {
            key: 0,
            class: "image-item image-plus",
            onClick: a[0] || (a[0] = (...i) => e.addNewImage && e.addNewImage(...i))
          }, [
            createVNode(u, {
              icon: e.ui.icons.plus,
              class: "cropper-uploader-icon"
            }, null, 8, ["icon"])
          ])) : createCommentVNode("", true)
        ]),
        _: 1
      }))
    ]),
    createVNode(y, {
      ref: "cropperRef",
      title: e.computedProps.title,
      "cropper-height": e.computedProps.cropperHeight,
      "dialog-width": e.computedProps.dialogWidth,
      accept: e.computedProps.accept,
      "upload-tip": e.computedProps.uploadTip,
      "max-size": e.computedProps.maxSize,
      cropper: e.computedProps.cropper,
      "compress-quality": e.computedProps.compressQuality,
      output: "all",
      onDone: e.cropComplete,
      onReady: e.doReady
    }, null, 8, ["title", "cropper-height", "dialog-width", "accept", "upload-tip", "max-size", "cropper", "compress-quality", "onDone", "onReady"]),
    createBaseVNode("div", {
      class: normalizeClass(["fs-cropper-preview", { open: e.previewVisible }]),
      onClick: a[1] || (a[1] = (...i) => e.closePreview && e.closePreview(...i))
    }, [
      createBaseVNode("div", ue, [
        e.previewUrl ? (openBlock(), createElementBlock("img", {
          key: 0,
          src: e.previewUrl,
          class: "preview-image"
        }, null, 8, pe)) : createCommentVNode("", true)
      ])
    ], 2)
  ], 2);
}
var ve = he(re, [["render", ce]]);
export {
  ve as default
};
//# sourceMappingURL=fs-cropper-uploader-576c7bbb-S5YCM27I.js.map
