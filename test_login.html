<!DOCTYPE html>
<html>
<head>
    <title>微信登录测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>微信登录测试</h1>
    <button onclick="testLogin()">测试登录</button>
    <div id="result"></div>

    <script>
        function testLogin() {
            // 模拟后端返回的数据
            const mockResponse = {
                "code": 2000,
                "data": {
                    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNzY0NjM4LCJpYXQiOjE3NTE2NzgyMzgsImp0aSI6ImMxMGE3MDQzYTkxOTQxYWVhZTkyMTUzNjMxYWU5YWU0IiwidXNlcl9pZCI6NH0.9yd_TE4MkQgaPYRnF8V-Mq1efPNKgQfjQQf4GKUKrWA",
                    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTc2NDYzOCwiaWF0IjoxNzUxNjc4MjM4LCJqdGkiOiJiM2U2ODkxYTkxOGQ0YzgzYmI2M2VhYTk5ODdkYjliZCIsInVzZXJfaWQiOjR9.Dok-LhBkb-VlXNo1Y5j_GKqijeBMP67Vl5UFR1ckka4",
                    "user": {
                        "id": 1,
                        "openid": "oXVgg7Xt4b55VxQukxpxg7YTGYxw",
                        "nickname": "微信用户",
                        "avatar_url": "",
                        "gender": 0,
                        "is_active": true,
                        "last_login_time": "2025-07-05 09:17:18",
                        "username": "wx_g7YTGYxw",
                        "name": "微信用户",
                        "email": null,
                        "mobile": null,
                        "create_datetime": "2025-07-05 00:26:58"
                    },
                    "is_new_user": false
                },
                "msg": "登录成功"
            };

            // 模拟前端登录逻辑
            console.log('模拟登录返回数据:', JSON.stringify(mockResponse));
            
            const resultDiv = document.getElementById('result');
            
            if (mockResponse.code === 2000) {
                // 登录成功逻辑
                let token = null;
                let userData = null;

                // 尝试从不同位置获取token
                if (mockResponse.data && mockResponse.data.token) {
                    token = mockResponse.data.token;
                } else if (mockResponse.token) {
                    token = mockResponse.token;
                }

                // 尝试从不同位置获取用户信息
                if (mockResponse.data && mockResponse.data.user) {
                    userData = mockResponse.data.user;
                } else if (mockResponse.user) {
                    userData = mockResponse.user;
                }

                if (!token) {
                    resultDiv.innerHTML = '<p style="color: red;">❌ 登录失败，未获取到授权信息</p>';
                    return;
                }

                // 保存token（模拟）
                console.log('保存token:', token);
                
                // 保存用户基本信息到本地（模拟）
                const userInfo = {
                    nickName: userData.nickname || '微信用户',
                    avatarUrl: userData.avatar_url || userData.avatar || '',
                    gender: userData.gender || 0
                };
                console.log('保存用户信息:', userInfo);

                // 检查是否需要完善个人资料
                const needComplete = !userData.nickname || userData.nickname === '微信用户' || (!userData.avatar_url && !userData.avatar);
                
                if (needComplete) {
                    resultDiv.innerHTML = `
                        <p style="color: orange;">⚠️ 登录成功，但需要完善个人资料</p>
                        <p>用户信息: ${JSON.stringify(userInfo, null, 2)}</p>
                        <p>建议: 跳转到个人资料页面完善信息</p>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <p style="color: green;">✅ 登录成功，直接进入首页</p>
                        <p>用户信息: ${JSON.stringify(userInfo, null, 2)}</p>
                    `;
                }
            } else {
                resultDiv.innerHTML = `<p style="color: red;">❌ 登录失败: ${mockResponse.msg}</p>`;
            }
        }
    </script>
</body>
</html>
