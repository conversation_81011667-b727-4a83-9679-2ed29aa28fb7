[2025-07-02 21:48:46,788][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-02 21:53:30,936][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-02 21:55:58,375][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\conf\env.py changed, reloading.
[2025-07-02 21:56:00,668][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-02 21:56:16,867][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\conf\env.py changed, reloading.
[2025-07-02 21:56:18,937][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-02 21:56:25,245][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-02 21:58:15,585][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:14:47,566][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:15:11,503][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:17:29,827][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:21:26,654][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:22:00,723][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\conf\env.py changed, reloading.
[2025-07-03 21:22:03,228][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:22:44,462][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:23:09,298][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:24:00,926][django.server.log_message():212] [INFO] "OPTIONS /api/captcha/ HTTP/1.1" 200 0
[2025-07-03 21:24:00,926][django.server.log_message():212] [INFO] "OPTIONS /api/init/settings/ HTTP/1.1" 200 0
[2025-07-03 21:24:01,028][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-03 21:24:01,107][django.server.log_message():212] [INFO] "GET /api/captcha/ HTTP/1.1" 200 5421
[2025-07-03 21:24:16,848][django.server.log_message():212] [INFO] "OPTIONS /api/login/ HTTP/1.1" 200 0
[2025-07-03 21:24:17,027][dvadmin.utils.backends.authenticate():22] [INFO] superadmin 正在使用本地登录...
[2025-07-03 21:24:17,693][django.server.log_message():212] [INFO] "POST /api/login/ HTTP/1.1" 200 744
[2025-07-03 21:24:17,701][django.server.log_message():212] [INFO] "OPTIONS /api/system/user/user_info/ HTTP/1.1" 200 0
[2025-07-03 21:24:17,701][django.server.log_message():212] [INFO] "OPTIONS /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 0
[2025-07-03 21:24:17,705][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-03 21:24:17,867][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-03 21:24:17,871][django.server.log_message():212] [INFO] "OPTIONS /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 0
[2025-07-03 21:24:17,874][django.server.log_message():212] [INFO] "OPTIONS /api/system/dept/all_dept/ HTTP/1.1" 200 0
[2025-07-03 21:24:17,874][django.server.log_message():212] [INFO] "OPTIONS /api/system/menu/web_router/ HTTP/1.1" 200 0
[2025-07-03 21:24:17,881][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-03 21:24:18,137][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-03 21:24:18,179][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1408
[2025-07-03 21:24:18,179][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-03 21:24:18,179][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 6497
[2025-07-03 21:24:18,238][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-03 21:24:18,248][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-03 21:24:18,417][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 6497
[2025-07-03 21:24:18,417][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-03 21:24:18,425][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-03 21:24:18,464][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1408
[2025-07-03 21:24:21,430][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNjM1NDU3LCJpYXQiOjE3NTE1NDkwNTcsImp0aSI6IjkzOTdhNzAwOTY4MzQzMmZhOTk2ZTRiYmRmMjFlYzA4IiwidXNlcl9pZCI6MX0._odRMmGCquhh2gYcU6OrXTclIDeH8i28kto8dWgMV9s HTTP/1.1" 500 59
[2025-07-03 21:26:57,594][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\conf\env.py changed, reloading.
[2025-07-03 21:26:59,890][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:27:36,951][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\conf\env.py changed, reloading.
[2025-07-03 21:27:39,252][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:51:39,171][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:51:47,052][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-03 21:51:47,068][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-03 21:51:47,207][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-03 21:51:47,207][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1408
[2025-07-03 21:51:47,217][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-03 21:51:47,236][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 6497
[2025-07-03 21:51:49,490][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNjM1NDU3LCJpYXQiOjE3NTE1NDkwNTcsImp0aSI6IjkzOTdhNzAwOTY4MzQzMmZhOTk2ZTRiYmRmMjFlYzA4IiwidXNlcl9pZCI6MX0._odRMmGCquhh2gYcU6OrXTclIDeH8i28kto8dWgMV9s HTTP/1.1" 500 59
[2025-07-03 21:52:37,534][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-03 21:52:37,551][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-03 21:52:37,824][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-03 21:52:37,850][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1408
[2025-07-03 21:52:37,883][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-03 21:52:37,890][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 6497
[2025-07-03 21:52:40,065][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNjM1NDU3LCJpYXQiOjE3NTE1NDkwNTcsImp0aSI6IjkzOTdhNzAwOTY4MzQzMmZhOTk2ZTRiYmRmMjFlYzA4IiwidXNlcl9pZCI6MX0._odRMmGCquhh2gYcU6OrXTclIDeH8i28kto8dWgMV9s HTTP/1.1" 500 59
[2025-07-03 21:52:46,807][django.server.log_message():212] [INFO] "OPTIONS /swagger.json HTTP/1.1" 200 0
[2025-07-03 21:52:46,807][django.server.log_message():212] [INFO] "OPTIONS /api/system/column/get_models/ HTTP/1.1" 200 0
[2025-07-03 21:52:46,807][django.server.log_message():212] [INFO] "OPTIONS /api/system/menu/ HTTP/1.1" 200 0
[2025-07-03 21:52:46,808][django.server.log_message():212] [INFO] "OPTIONS /api/system/column/get_models/ HTTP/1.1" 200 0
[2025-07-03 21:52:46,998][django.server.log_message():212] [INFO] "GET /api/system/column/get_models/ HTTP/1.1" 200 1569
[2025-07-03 21:52:47,149][django.server.log_message():212] [INFO] "GET /api/system/column/get_models/ HTTP/1.1" 200 1569
[2025-07-03 21:52:47,225][django.server.log_message():212] [INFO] "GET /api/system/menu/ HTTP/1.1" 200 2092
[2025-07-03 21:52:47,908][django.server.log_message():212] [INFO] "GET /swagger.json HTTP/1.1" 200 421857
[2025-07-03 21:53:01,791][django.server.log_message():212] [INFO] "OPTIONS /api/system/menu/?parent=1 HTTP/1.1" 200 0
[2025-07-03 21:53:02,181][django.server.log_message():212] [INFO] "GET /api/system/menu/?parent=1 HTTP/1.1" 200 6765
[2025-07-03 21:53:23,636][django.server.log_message():212] [INFO] "OPTIONS /api/system/role/ HTTP/1.1" 200 0
[2025-07-03 21:53:23,636][django.server.log_message():212] [INFO] "OPTIONS /api/system/user/?page=1&limit=20&show_all=0 HTTP/1.1" 200 0
[2025-07-03 21:53:23,636][django.server.log_message():212] [INFO] "OPTIONS /api/system/dept/ HTTP/1.1" 200 0
[2025-07-03 21:53:23,636][django.server.log_message():212] [INFO] "OPTIONS /api/system/dept/dept_info/?dept_id=&show_all=0 HTTP/1.1" 200 0
[2025-07-03 21:53:23,848][django.server.log_message():212] [INFO] "GET /api/system/dept/dept_info/?dept_id=&show_all=0 HTTP/1.1" 200 191
[2025-07-03 21:53:23,882][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-03 21:53:23,975][django.server.log_message():212] [INFO] "GET /api/system/role/ HTTP/1.1" 200 695
[2025-07-03 21:53:24,109][django.server.log_message():212] [INFO] "GET /api/system/dept/ HTTP/1.1" 200 1275
[2025-07-03 21:53:24,174][django.server.log_message():212] [INFO] "GET /api/system/user/?page=1&limit=20&show_all=0 HTTP/1.1" 200 1752
[2025-07-03 21:58:14,616][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 21:58:32,024][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-03 21:58:32,035][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-03 21:58:32,238][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1624
[2025-07-03 21:58:32,239][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-03 21:58:32,287][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 7180
[2025-07-03 21:58:32,288][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-03 21:58:32,647][django.server.log_message():212] [INFO] "GET /api/system/dept/dept_info/?dept_id=&show_all=0 HTTP/1.1" 200 191
[2025-07-03 21:58:32,687][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-03 21:58:32,778][django.server.log_message():212] [INFO] "GET /api/system/role/ HTTP/1.1" 200 719
[2025-07-03 21:58:32,883][django.server.log_message():212] [INFO] "GET /api/system/dept/ HTTP/1.1" 200 1287
[2025-07-03 21:58:32,916][django.server.log_message():212] [INFO] "GET /api/system/user/?page=1&limit=20&show_all=0 HTTP/1.1" 200 1788
[2025-07-03 21:58:34,672][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNjM1NDU3LCJpYXQiOjE3NTE1NDkwNTcsImp0aSI6IjkzOTdhNzAwOTY4MzQzMmZhOTk2ZTRiYmRmMjFlYzA4IiwidXNlcl9pZCI6MX0._odRMmGCquhh2gYcU6OrXTclIDeH8i28kto8dWgMV9s HTTP/1.1" 500 59
[2025-07-03 21:58:38,612][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 0
[2025-07-03 21:58:38,612][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/category_stats/ HTTP/1.1" 200 0
[2025-07-03 21:58:38,963][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/category_stats/ HTTP/1.1" 200 54
[2025-07-03 21:58:38,985][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 5429
[2025-07-03 21:59:30,879][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/category_stats/ HTTP/1.1" 200 54
[2025-07-03 21:59:34,183][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 5429
[2025-07-03 21:59:40,885][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20&name=%E8%93%9D%E5%88%BA%E5%A4%B4 HTTP/1.1" 200 0
[2025-07-03 21:59:41,049][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&name=%E8%93%9D%E5%88%BA%E5%A4%B4 HTTP/1.1" 200 5429
[2025-07-03 21:59:42,414][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&name=%E8%93%9D%E5%88%BA%E5%A4%B4 HTTP/1.1" 200 5429
[2025-07-03 21:59:47,640][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20&category=%E8%8D%89%E6%9C%AC%E6%A4%8D%E7%89%A9 HTTP/1.1" 200 0
[2025-07-03 21:59:47,802][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category=%E8%8D%89%E6%9C%AC%E6%A4%8D%E7%89%A9 HTTP/1.1" 200 1913
[2025-07-03 21:59:48,815][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category=%E8%8D%89%E6%9C%AC%E6%A4%8D%E7%89%A9 HTTP/1.1" 200 1913
[2025-07-03 21:59:52,613][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20&category=%E8%97%A4%E6%9C%AC%E6%A4%8D%E7%89%A9 HTTP/1.1" 200 0
[2025-07-03 21:59:52,785][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category=%E8%97%A4%E6%9C%AC%E6%A4%8D%E7%89%A9 HTTP/1.1" 200 995
[2025-07-03 21:59:58,045][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20&category=%E5%85%B0%E7%A7%91%E6%A4%8D%E7%89%A9 HTTP/1.1" 200 0
[2025-07-03 21:59:58,184][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category=%E5%85%B0%E7%A7%91%E6%A4%8D%E7%89%A9 HTTP/1.1" 200 543
[2025-07-03 22:00:01,224][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category=%E8%8D%89%E6%9C%AC%E6%A4%8D%E7%89%A9 HTTP/1.1" 200 1913
[2025-07-03 22:00:03,467][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20&category= HTTP/1.1" 200 0
[2025-07-03 22:00:04,635][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category= HTTP/1.1" 200 5429
[2025-07-03 22:00:05,291][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20&category=&status=0 HTTP/1.1" 200 0
[2025-07-03 22:00:05,416][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category=&status=0 HTTP/1.1" 200 110
[2025-07-03 22:00:08,279][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20&category=&status=1 HTTP/1.1" 200 0
[2025-07-03 22:00:08,470][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category=&status=1 HTTP/1.1" 200 5429
[2025-07-03 22:00:10,268][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20&category=&status=2 HTTP/1.1" 200 0
[2025-07-03 22:00:10,407][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category=&status=2 HTTP/1.1" 200 110
[2025-07-03 22:00:12,696][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category=&status=1 HTTP/1.1" 200 5429
[2025-07-03 22:00:20,606][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category=&status=1 HTTP/1.1" 200 5429
[2025-07-03 22:07:33,954][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category=&status=1 HTTP/1.1" 200 5429
[2025-07-03 22:07:46,991][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=5&category=&status=1 HTTP/1.1" 200 0
[2025-07-03 22:07:47,153][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=5&category=&status=1 HTTP/1.1" 200 2270
[2025-07-03 22:12:32,849][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-03 22:12:35,487][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=5&category=&status=1 HTTP/1.1" 200 2269
[2025-07-03 22:12:36,586][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=5&category=&status=1 HTTP/1.1" 200 2269
[2025-07-03 22:12:38,507][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=5&category=&status=1 HTTP/1.1" 200 2269
[2025-07-03 22:12:41,396][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=50&category=&status=1 HTTP/1.1" 200 0
[2025-07-03 22:12:41,550][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=50&category=&status=1 HTTP/1.1" 200 3154
[2025-07-04 21:42:04,820][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 21:42:30,502][django.server.log_message():212] [INFO] "OPTIONS /api/captcha/ HTTP/1.1" 200 0
[2025-07-04 21:42:30,502][django.server.log_message():212] [INFO] "OPTIONS /api/init/settings/ HTTP/1.1" 200 0
[2025-07-04 21:42:30,735][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-04 21:42:30,919][django.server.log_message():212] [INFO] "GET /api/captcha/ HTTP/1.1" 200 6389
[2025-07-04 21:42:40,279][django.server.log_message():212] [INFO] "OPTIONS /api/login/ HTTP/1.1" 200 0
[2025-07-04 21:42:40,485][dvadmin.utils.backends.authenticate():22] [INFO] superadmin 正在使用本地登录...
[2025-07-04 21:42:40,938][django.server.log_message():212] [INFO] "POST /api/login/ HTTP/1.1" 200 80
[2025-07-04 21:42:41,069][django.server.log_message():212] [INFO] "GET /api/captcha/ HTTP/1.1" 200 5877
[2025-07-04 21:42:48,403][dvadmin.utils.backends.authenticate():22] [INFO] superadmin 正在使用本地登录...
[2025-07-04 21:42:48,966][django.server.log_message():212] [INFO] "POST /api/login/ HTTP/1.1" 200 744
[2025-07-04 21:42:48,973][django.server.log_message():212] [INFO] "OPTIONS /api/system/user/user_info/ HTTP/1.1" 200 0
[2025-07-04 21:42:48,976][django.server.log_message():212] [INFO] "OPTIONS /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 0
[2025-07-04 21:42:48,978][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-04 21:42:49,153][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-04 21:42:49,153][django.server.log_message():212] [INFO] "OPTIONS /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 0
[2025-07-04 21:42:49,160][django.server.log_message():212] [INFO] "OPTIONS /api/system/dept/all_dept/ HTTP/1.1" 200 0
[2025-07-04 21:42:49,160][django.server.log_message():212] [INFO] "OPTIONS /api/system/menu/web_router/ HTTP/1.1" 200 0
[2025-07-04 21:42:49,165][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-04 21:42:49,334][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-04 21:42:49,382][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 7180
[2025-07-04 21:42:49,408][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-04 21:42:49,409][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-04 21:42:49,415][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-04 21:42:49,417][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1624
[2025-07-04 21:42:49,576][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-04 21:42:49,624][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 7180
[2025-07-04 21:42:49,624][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1624
[2025-07-04 21:42:49,650][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-04 21:42:52,686][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNzIyOTY4LCJpYXQiOjE3NTE2MzY1NjgsImp0aSI6ImMyMGYzN2NmODFlYjQ3OWViZjJlNWE4NzdjZDgyODBiIiwidXNlcl9pZCI6MX0.Hpb0o4-9enuy3BLDG94dD_jbwsT3VbGJAjVAR6aRvoU HTTP/1.1" 500 59
[2025-07-04 21:42:54,254][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 0
[2025-07-04 21:42:54,461][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 9531
[2025-07-04 21:43:17,407][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 9531
[2025-07-04 21:43:22,943][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20&search=%E7%8B%BC%E6%AF%92 HTTP/1.1" 200 0
[2025-07-04 21:43:23,125][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&search=%E7%8B%BC%E6%AF%92 HTTP/1.1" 200 2025
[2025-07-04 21:43:33,985][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&search=%E7%8B%BC%E6%AF%92 HTTP/1.1" 200 2025
[2025-07-04 21:43:40,779][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&search=%E7%8B%BC%E6%AF%92 HTTP/1.1" 200 2025
[2025-07-04 21:43:45,203][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 9531
[2025-07-04 21:43:47,279][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&search=%E7%8B%BC%E6%AF%92 HTTP/1.1" 200 2025
[2025-07-04 21:43:51,640][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20&category=%E8%8D%89%E6%9C%AC%E6%A4%8D%E7%89%A9 HTTP/1.1" 200 0
[2025-07-04 21:43:51,829][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category=%E8%8D%89%E6%9C%AC%E6%A4%8D%E7%89%A9 HTTP/1.1" 200 9531
[2025-07-04 21:43:53,967][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 9531
[2025-07-04 21:43:57,837][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&category=%E8%8D%89%E6%9C%AC%E6%A4%8D%E7%89%A9 HTTP/1.1" 200 9531
[2025-07-04 21:43:59,492][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 9531
[2025-07-04 21:44:12,941][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&search=%E7%8B%BC%E6%AF%92 HTTP/1.1" 200 2025
[2025-07-04 21:45:41,680][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&search=%E7%8B%BC%E6%AF%92 HTTP/1.1" 200 2025
[2025-07-04 21:48:19,975][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\apps\plant_encyclopedia\views.py changed, reloading.
[2025-07-04 21:48:22,419][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 21:48:35,656][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\apps\plant_encyclopedia\views.py changed, reloading.
[2025-07-04 21:48:37,854][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 21:49:15,637][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 21:50:04,019][django.server.log_message():212] [INFO] "POST /api/plant-encyclopedia/plants/search/ HTTP/1.1" 200 64
[2025-07-04 21:50:04,617][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\apps\plant_encyclopedia\views.py changed, reloading.
[2025-07-04 21:50:04,987][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\apps\plant_encyclopedia\views.py changed, reloading.
[2025-07-04 21:50:05,837][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 21:50:07,342][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 21:50:07,712][django.server.log_message():212] [INFO] "POST /api/plant-encyclopedia/plants/search/ HTTP/1.1" 200 64
[2025-07-04 21:50:07,945][django.server.log_message():212] [INFO] "POST /api/plant-encyclopedia/plants/search/ HTTP/1.1" 200 64
[2025-07-04 21:50:08,161][django.server.log_message():212] [INFO] "POST /api/plant-encyclopedia/plants/search/ HTTP/1.1" 200 64
[2025-07-04 21:50:08,367][django.server.log_message():212] [INFO] "POST /api/plant-encyclopedia/plants/search/ HTTP/1.1" 200 64
[2025-07-04 21:50:26,129][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20&search=%E5%8F%91%E8%B4%A2%E6%A0%91 HTTP/1.1" 200 0
[2025-07-04 21:50:26,378][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&search=%E5%8F%91%E8%B4%A2%E6%A0%91 HTTP/1.1" 200 1128
[2025-07-04 21:53:31,867][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20&search=%E5%8F%91%E8%B4%A2%E6%A0%91 HTTP/1.1" 200 588
[2025-07-04 22:00:15,304][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-04 22:00:15,314][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-04 22:00:15,495][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 619
[2025-07-04 22:00:15,495][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1624
[2025-07-04 22:00:15,585][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 7159
[2025-07-04 22:00:15,878][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 9531
[2025-07-04 22:00:16,482][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-04 22:00:17,877][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNzIyOTY4LCJpYXQiOjE3NTE2MzY1NjgsImp0aSI6ImMyMGYzN2NmODFlYjQ3OWViZjJlNWE4NzdjZDgyODBiIiwidXNlcl9pZCI6MX0.Hpb0o4-9enuy3BLDG94dD_jbwsT3VbGJAjVAR6aRvoU HTTP/1.1" 500 59
[2025-07-04 22:02:35,497][django.server.log_message():212] [INFO] "OPTIONS /api/system/system_config/?limit=999&parent__isnull=True HTTP/1.1" 200 0
[2025-07-04 22:02:36,047][django.server.log_message():212] [INFO] "GET /api/system/system_config/?limit=999&parent__isnull=True HTTP/1.1" 200 13751
[2025-07-04 22:02:36,089][django.server.log_message():212] [INFO] "OPTIONS /api/system/system_config/?parent=6&limit=999 HTTP/1.1" 200 0
[2025-07-04 22:02:36,089][django.server.log_message():212] [INFO] "OPTIONS /api/system/system_config/?parent=16&limit=999 HTTP/1.1" 200 0
[2025-07-04 22:02:36,089][django.server.log_message():212] [INFO] "OPTIONS /api/system/system_config/?parent__isnull=True&limit=999 HTTP/1.1" 200 0
[2025-07-04 22:02:36,090][django.server.log_message():212] [INFO] "OPTIONS /api/system/system_config/?parent=1&limit=999 HTTP/1.1" 200 0
[2025-07-04 22:02:36,396][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=1&limit=999 HTTP/1.1" 200 1987
[2025-07-04 22:02:36,561][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=6&limit=999 HTTP/1.1" 200 4390
[2025-07-04 22:02:36,707][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=16&limit=999 HTTP/1.1" 200 6646
[2025-07-04 22:02:36,835][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent__isnull=True&limit=999 HTTP/1.1" 200 13751
[2025-07-04 22:02:56,581][django.server.log_message():212] [INFO] "OPTIONS /api/system/system_config/save_content/ HTTP/1.1" 200 0
[2025-07-04 22:02:57,038][django.server.log_message():212] [INFO] "PUT /api/system/system_config/save_content/ HTTP/1.1" 200 46
[2025-07-04 22:02:57,674][django.server.log_message():212] [INFO] "GET /api/system/system_config/?limit=999&parent__isnull=True HTTP/1.1" 200 13753
[2025-07-04 22:02:58,051][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=1&limit=999 HTTP/1.1" 200 1989
[2025-07-04 22:02:58,164][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=6&limit=999 HTTP/1.1" 200 4390
[2025-07-04 22:02:58,238][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=16&limit=999 HTTP/1.1" 200 6646
[2025-07-04 22:02:58,346][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent__isnull=True&limit=999 HTTP/1.1" 200 13753
[2025-07-04 22:02:59,149][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-04 22:02:59,164][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-04 22:02:59,287][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 621
[2025-07-04 22:02:59,294][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1624
[2025-07-04 22:02:59,338][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-04 22:02:59,357][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 7159
[2025-07-04 22:02:59,924][django.server.log_message():212] [INFO] "GET /api/system/system_config/?limit=999&parent__isnull=True HTTP/1.1" 200 13753
[2025-07-04 22:03:00,349][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=1&limit=999 HTTP/1.1" 200 1989
[2025-07-04 22:03:00,430][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=6&limit=999 HTTP/1.1" 200 4390
[2025-07-04 22:03:00,684][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent__isnull=True&limit=999 HTTP/1.1" 200 13753
[2025-07-04 22:03:00,697][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=16&limit=999 HTTP/1.1" 200 6646
[2025-07-04 22:03:01,523][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNzIyOTY4LCJpYXQiOjE3NTE2MzY1NjgsImp0aSI6ImMyMGYzN2NmODFlYjQ3OWViZjJlNWE4NzdjZDgyODBiIiwidXNlcl9pZCI6MX0.Hpb0o4-9enuy3BLDG94dD_jbwsT3VbGJAjVAR6aRvoU HTTP/1.1" 500 59
[2025-07-04 22:04:01,007][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-04 22:04:01,020][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-04 22:04:01,207][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 7159
[2025-07-04 22:04:01,207][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1624
[2025-07-04 22:04:01,243][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-04 22:04:01,780][django.server.log_message():212] [INFO] "GET /api/system/system_config/?limit=999&parent__isnull=True HTTP/1.1" 200 13753
[2025-07-04 22:04:02,128][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 621
[2025-07-04 22:04:02,183][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=1&limit=999 HTTP/1.1" 200 1989
[2025-07-04 22:04:02,289][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=6&limit=999 HTTP/1.1" 200 4390
[2025-07-04 22:04:02,453][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent__isnull=True&limit=999 HTTP/1.1" 200 13753
[2025-07-04 22:04:03,399][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=16&limit=999 HTTP/1.1" 200 6646
[2025-07-04 22:04:03,589][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNzIyOTY4LCJpYXQiOjE3NTE2MzY1NjgsImp0aSI6ImMyMGYzN2NmODFlYjQ3OWViZjJlNWE4NzdjZDgyODBiIiwidXNlcl9pZCI6MX0.Hpb0o4-9enuy3BLDG94dD_jbwsT3VbGJAjVAR6aRvoU HTTP/1.1" 500 59
[2025-07-04 22:04:53,247][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-04 22:04:53,257][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-04 22:04:53,411][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 621
[2025-07-04 22:04:53,442][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1624
[2025-07-04 22:04:53,442][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 7159
[2025-07-04 22:04:53,482][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-04 22:04:54,013][django.server.log_message():212] [INFO] "GET /api/system/system_config/?limit=999&parent__isnull=True HTTP/1.1" 200 13752
[2025-07-04 22:04:54,408][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=1&limit=999 HTTP/1.1" 200 1989
[2025-07-04 22:04:54,477][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=6&limit=999 HTTP/1.1" 200 4389
[2025-07-04 22:04:54,608][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=16&limit=999 HTTP/1.1" 200 6646
[2025-07-04 22:04:54,640][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent__isnull=True&limit=999 HTTP/1.1" 200 13752
[2025-07-04 22:04:55,647][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNzIyOTY4LCJpYXQiOjE3NTE2MzY1NjgsImp0aSI6ImMyMGYzN2NmODFlYjQ3OWViZjJlNWE4NzdjZDgyODBiIiwidXNlcl9pZCI6MX0.Hpb0o4-9enuy3BLDG94dD_jbwsT3VbGJAjVAR6aRvoU HTTP/1.1" 500 59
[2025-07-04 22:05:16,701][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:05:34,537][django.server.log_message():212] [INFO] "OPTIONS /api/system/user/user_info/ HTTP/1.1" 200 0
[2025-07-04 22:05:34,689][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-04 22:05:34,695][django.server.log_message():212] [INFO] "OPTIONS /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 0
[2025-07-04 22:05:34,695][django.server.log_message():212] [INFO] "OPTIONS /api/system/menu/web_router/ HTTP/1.1" 200 0
[2025-07-04 22:05:34,695][django.server.log_message():212] [INFO] "OPTIONS /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 0
[2025-07-04 22:05:34,695][django.server.log_message():212] [INFO] "OPTIONS /api/init/settings/ HTTP/1.1" 200 0
[2025-07-04 22:05:34,695][django.server.log_message():212] [INFO] "OPTIONS /api/system/dept/all_dept/ HTTP/1.1" 200 0
[2025-07-04 22:05:34,705][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-04 22:05:34,856][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 621
[2025-07-04 22:05:34,891][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 7159
[2025-07-04 22:05:34,901][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-04 22:05:34,939][django.server.log_message():212] [INFO] "OPTIONS /api/system/system_config/?limit=999&parent__isnull=True HTTP/1.1" 200 0
[2025-07-04 22:05:35,814][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1624
[2025-07-04 22:05:36,474][django.server.log_message():212] [INFO] "GET /api/system/system_config/?limit=999&parent__isnull=True HTTP/1.1" 200 13752
[2025-07-04 22:05:36,515][django.server.log_message():212] [INFO] "OPTIONS /api/system/system_config/?parent=1&limit=999 HTTP/1.1" 200 0
[2025-07-04 22:05:36,515][django.server.log_message():212] [INFO] "OPTIONS /api/system/system_config/?parent=6&limit=999 HTTP/1.1" 200 0
[2025-07-04 22:05:36,515][django.server.log_message():212] [INFO] "OPTIONS /api/system/system_config/?parent__isnull=True&limit=999 HTTP/1.1" 200 0
[2025-07-04 22:05:36,515][django.server.log_message():212] [INFO] "OPTIONS /api/system/system_config/?parent=16&limit=999 HTTP/1.1" 200 0
[2025-07-04 22:05:36,787][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=1&limit=999 HTTP/1.1" 200 1989
[2025-07-04 22:05:36,900][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=6&limit=999 HTTP/1.1" 200 4389
[2025-07-04 22:05:36,977][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=16&limit=999 HTTP/1.1" 200 6646
[2025-07-04 22:05:37,063][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNzIyOTY4LCJpYXQiOjE3NTE2MzY1NjgsImp0aSI6ImMyMGYzN2NmODFlYjQ3OWViZjJlNWE4NzdjZDgyODBiIiwidXNlcl9pZCI6MX0.Hpb0o4-9enuy3BLDG94dD_jbwsT3VbGJAjVAR6aRvoU HTTP/1.1" 500 59
[2025-07-04 22:05:37,096][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent__isnull=True&limit=999 HTTP/1.1" 200 13752
[2025-07-04 22:06:08,516][django.server.log_message():212] [INFO] "OPTIONS /api/system/system_config/save_content/ HTTP/1.1" 200 0
[2025-07-04 22:06:09,354][django.server.log_message():212] [INFO] "PUT /api/system/system_config/save_content/ HTTP/1.1" 200 46
[2025-07-04 22:06:09,927][django.server.log_message():212] [INFO] "GET /api/system/system_config/?limit=999&parent__isnull=True HTTP/1.1" 200 13738
[2025-07-04 22:06:10,335][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=1&limit=999 HTTP/1.1" 200 1989
[2025-07-04 22:06:10,434][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=6&limit=999 HTTP/1.1" 200 4375
[2025-07-04 22:06:10,596][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent__isnull=True&limit=999 HTTP/1.1" 200 13738
[2025-07-04 22:06:10,602][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=16&limit=999 HTTP/1.1" 200 6646
[2025-07-04 22:06:12,323][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-04 22:06:12,337][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-04 22:06:12,490][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 606
[2025-07-04 22:06:12,521][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1624
[2025-07-04 22:06:12,547][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 7159
[2025-07-04 22:06:12,567][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-04 22:06:13,146][django.server.log_message():212] [INFO] "GET /api/system/system_config/?limit=999&parent__isnull=True HTTP/1.1" 200 13738
[2025-07-04 22:06:13,543][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=1&limit=999 HTTP/1.1" 200 1989
[2025-07-04 22:06:13,635][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=6&limit=999 HTTP/1.1" 200 4375
[2025-07-04 22:06:13,787][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=16&limit=999 HTTP/1.1" 200 6646
[2025-07-04 22:06:13,836][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent__isnull=True&limit=999 HTTP/1.1" 200 13738
[2025-07-04 22:06:14,716][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNzIyOTY4LCJpYXQiOjE3NTE2MzY1NjgsImp0aSI6ImMyMGYzN2NmODFlYjQ3OWViZjJlNWE4NzdjZDgyODBiIiwidXNlcl9pZCI6MX0.Hpb0o4-9enuy3BLDG94dD_jbwsT3VbGJAjVAR6aRvoU HTTP/1.1" 500 59
[2025-07-04 22:06:25,197][django.server.log_message():212] [INFO] "OPTIONS /api/captcha/ HTTP/1.1" 200 0
[2025-07-04 22:06:25,285][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 606
[2025-07-04 22:06:25,338][django.server.log_message():212] [INFO] "GET /api/captcha/ HTTP/1.1" 200 5253
[2025-07-04 22:06:36,880][django.server.log_message():212] [INFO] "OPTIONS /api/login/ HTTP/1.1" 200 0
[2025-07-04 22:06:37,146][django.server.log_message():212] [INFO] "POST /api/login/ HTTP/1.1" 200 55
[2025-07-04 22:06:37,339][django.server.log_message():212] [INFO] "GET /api/captcha/ HTTP/1.1" 200 4809
[2025-07-04 22:06:41,551][dvadmin.utils.backends.authenticate():22] [INFO] superadmin 正在使用本地登录...
[2025-07-04 22:06:42,045][django.server.log_message():212] [INFO] "POST /api/login/ HTTP/1.1" 200 744
[2025-07-04 22:06:42,053][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-04 22:06:42,182][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-04 22:06:42,192][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-04 22:06:42,356][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 606
[2025-07-04 22:06:42,375][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1624
[2025-07-04 22:06:42,426][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 7159
[2025-07-04 22:06:42,428][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-04 22:06:42,430][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-04 22:06:42,472][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-04 22:06:42,604][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 606
[2025-07-04 22:06:42,630][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1624
[2025-07-04 22:06:42,649][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 7159
[2025-07-04 22:06:42,677][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-04 22:06:43,263][django.server.log_message():212] [INFO] "GET /api/system/system_config/?limit=999&parent__isnull=True HTTP/1.1" 200 13738
[2025-07-04 22:06:43,596][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=1&limit=999 HTTP/1.1" 200 1989
[2025-07-04 22:06:43,763][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=6&limit=999 HTTP/1.1" 200 4375
[2025-07-04 22:06:43,872][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent=16&limit=999 HTTP/1.1" 200 6646
[2025-07-04 22:06:43,879][django.server.log_message():212] [INFO] "GET /api/system/system_config/?parent__isnull=True&limit=999 HTTP/1.1" 200 13738
[2025-07-04 22:06:44,827][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNzI0NDAxLCJpYXQiOjE3NTE2MzgwMDEsImp0aSI6IjdlNmQ1NDZmZTRjMDRjOTdhZWI3ZDg1YTU0MTQzMjkxIiwidXNlcl9pZCI6MX0.EcHcEJI1HHxsZ56ZVeRkEHMjfoaxehzEhyqjK-Ff8LY HTTP/1.1" 500 59
[2025-07-04 22:19:49,087][django.server.log_message():212] [INFO] "OPTIONS /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 0
[2025-07-04 22:19:49,286][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 9531
[2025-07-04 22:24:40,165][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\dvadmin\system\models.py changed, reloading.
[2025-07-04 22:24:40,744][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\dvadmin\system\models.py changed, reloading.
[2025-07-04 22:24:42,225][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:24:42,648][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:46:17,609][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\application\urls.py changed, reloading.
[2025-07-04 22:46:17,730][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\application\urls.py changed, reloading.
[2025-07-04 22:46:19,160][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:46:22,432][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:46:39,616][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\apps\miniprogram_api.py changed, reloading.
[2025-07-04 22:46:39,788][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\apps\miniprogram_api.py changed, reloading.
[2025-07-04 22:46:40,892][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:46:41,942][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:47:17,338][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\apps\miniprogram_api.py changed, reloading.
[2025-07-04 22:47:17,742][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\apps\miniprogram_api.py changed, reloading.
[2025-07-04 22:47:18,495][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:47:19,983][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:49:46,858][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\dvadmin\system\urls.py changed, reloading.
[2025-07-04 22:49:47,521][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\dvadmin\system\urls.py changed, reloading.
[2025-07-04 22:49:48,702][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:49:49,112][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:50:42,722][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\dvladmin\system\views\miniprogram.py changed, reloading.
[2025-07-04 22:50:42,741][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\dvladmin\system\views\miniprogram.py changed, reloading.
[2025-07-04 22:50:43,895][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:50:44,917][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:53:07,916][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:54:13,500][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\dvadmin\system\urls.py changed, reloading.
[2025-07-04 22:54:16,081][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:54:25,900][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:54:38,889][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\dvadmin\system\urls.py changed, reloading.
[2025-07-04 22:54:41,177][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:54:47,356][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:54:58,590][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
MySQLdb.OperationalError: (1054, "Unknown column 'plant_system_users.city' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 497, in dispatch
    self.initial(request, *args, **kwargs)
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 414, in initial
    self.perform_authentication(request)
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 324, in perform_authentication
    request.user
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\request.py", line 231, in user
    self._authenticate()
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\request.py", line 384, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 51, in authenticate
    return self.get_user(validated_token), validated_token
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 130, in get_user
    user = self.user_model.objects.get(**{api_settings.USER_ID_FIELD: user_id})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
          ^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
django.db.utils.OperationalError: (1054, "Unknown column 'plant_system_users.city' in 'field list'")
Traceback (most recent call last):
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
MySQLdb.OperationalError: (1054, "Unknown column 'plant_system_users.city' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 497, in dispatch
    self.initial(request, *args, **kwargs)
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 414, in initial
    self.perform_authentication(request)
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 324, in perform_authentication
    request.user
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\request.py", line 231, in user
    self._authenticate()
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\request.py", line 384, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 51, in authenticate
    return self.get_user(validated_token), validated_token
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 130, in get_user
    user = self.user_model.objects.get(**{api_settings.USER_ID_FIELD: user_id})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
          ^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
django.db.utils.OperationalError: (1054, "Unknown column 'plant_system_users.city' in 'field list'")
[2025-07-04 22:54:58,593][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 102
[2025-07-04 22:55:36,939][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
MySQLdb.OperationalError: (1054, "Unknown column 'plant_system_users.city' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 497, in dispatch
    self.initial(request, *args, **kwargs)
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 414, in initial
    self.perform_authentication(request)
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 324, in perform_authentication
    request.user
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\request.py", line 231, in user
    self._authenticate()
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\request.py", line 384, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 51, in authenticate
    return self.get_user(validated_token), validated_token
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 130, in get_user
    user = self.user_model.objects.get(**{api_settings.USER_ID_FIELD: user_id})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
          ^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
django.db.utils.OperationalError: (1054, "Unknown column 'plant_system_users.city' in 'field list'")
Traceback (most recent call last):
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
MySQLdb.OperationalError: (1054, "Unknown column 'plant_system_users.city' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 497, in dispatch
    self.initial(request, *args, **kwargs)
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 414, in initial
    self.perform_authentication(request)
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 324, in perform_authentication
    request.user
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\request.py", line 231, in user
    self._authenticate()
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\request.py", line 384, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 51, in authenticate
    return self.get_user(validated_token), validated_token
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 130, in get_user
    user = self.user_model.objects.get(**{api_settings.USER_ID_FIELD: user_id})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
          ^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
django.db.utils.OperationalError: (1054, "Unknown column 'plant_system_users.city' in 'field list'")
[2025-07-04 22:55:36,950][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 102
[2025-07-04 22:59:01,040][django.utils.autoreload.trigger_reload():266] [INFO] D:\hanqc\project\pyProject\PlantHome\backend\dvadmin\system\models.py changed, reloading.
[2025-07-04 22:59:03,380][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 22:59:42,589][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 23:00:00,963][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 23:00:14,325][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
MySQLdb.OperationalError: (1054, "Unknown column 'plant_system_users.province' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 497, in dispatch
    self.initial(request, *args, **kwargs)
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 414, in initial
    self.perform_authentication(request)
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 324, in perform_authentication
    request.user
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\request.py", line 231, in user
    self._authenticate()
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\request.py", line 384, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 51, in authenticate
    return self.get_user(validated_token), validated_token
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 130, in get_user
    user = self.user_model.objects.get(**{api_settings.USER_ID_FIELD: user_id})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
          ^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
django.db.utils.OperationalError: (1054, "Unknown column 'plant_system_users.province' in 'field list'")
Traceback (most recent call last):
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
MySQLdb.OperationalError: (1054, "Unknown column 'plant_system_users.province' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 497, in dispatch
    self.initial(request, *args, **kwargs)
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 414, in initial
    self.perform_authentication(request)
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\views.py", line 324, in perform_authentication
    request.user
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\request.py", line 231, in user
    self._authenticate()
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework\request.py", line 384, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 51, in authenticate
    return self.get_user(validated_token), validated_token
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 130, in get_user
    user = self.user_model.objects.get(**{api_settings.USER_ID_FIELD: user_id})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
          ^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\System\python\anaconda\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
django.db.utils.OperationalError: (1054, "Unknown column 'plant_system_users.province' in 'field list'")
[2025-07-04 23:00:14,330][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 106
[2025-07-04 23:01:39,270][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-07-04 23:01:59,409][django.server.log_message():212] [INFO] "GET /api/system/user/user_info/ HTTP/1.1" 200 371
[2025-07-04 23:01:59,422][django.server.log_message():212] [INFO] "GET /api/init/dictionary/?dictionary_key=all HTTP/1.1" 200 2802
[2025-07-04 23:01:59,545][django.server.log_message():212] [INFO] "GET /api/system/menu_button/menu_button_all_permission/ HTTP/1.1" 200 1624
[2025-07-04 23:01:59,555][django.server.log_message():212] [INFO] "GET /api/init/settings/ HTTP/1.1" 200 606
[2025-07-04 23:01:59,582][django.server.log_message():212] [INFO] "GET /api/system/dept/all_dept/ HTTP/1.1" 200 167
[2025-07-04 23:01:59,592][django.server.log_message():212] [INFO] "GET /api/system/menu/web_router/ HTTP/1.1" 200 7159
[2025-07-04 23:02:00,031][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 9531
[2025-07-04 23:02:02,202][django.server.log_message():212] [ERROR] "GET //sse/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNzI0NDAxLCJpYXQiOjE3NTE2MzgwMDEsImp0aSI6IjdlNmQ1NDZmZTRjMDRjOTdhZWI3ZDg1YTU0MTQzMjkxIiwidXNlcl9pZCI6MX0.EcHcEJI1HHxsZ56ZVeRkEHMjfoaxehzEhyqjK-Ff8LY HTTP/1.1" 500 59
[2025-07-04 23:02:08,237][django.server.log_message():212] [INFO] "GET /api/plant-encyclopedia/plants/?page=1&limit=20 HTTP/1.1" 200 9531
[2025-07-04 23:51:54,332][apps.wx.utils.code2session():44] [INFO] 调用微信code2session接口，code: invalid_code
[2025-07-04 23:51:54,332][apps.wx.utils.code2session():49] [INFO] 微信API返回数据: {'errcode': 40029, 'errmsg': 'invalid code'}
[2025-07-04 23:51:54,332][apps.wx.utils.code2session():54] [ERROR] 微信API调用失败: 不合法的 code, errcode: 40029
[2025-07-04 23:51:54,367][apps.wx.utils.code2session():44] [INFO] 调用微信code2session接口，code: test_code
[2025-07-04 23:51:54,367][apps.wx.utils.code2session():49] [INFO] 微信API返回数据: {'openid': 'test_openid', 'session_key': 'test_session_key'}
[2025-07-04 23:51:55,688][apps.wx.views.wx_login():58] [INFO] 微信登录，openid: test_openid_123456
[2025-07-04 23:51:55,773][apps.wx.views.wx_login():136] [ERROR] 微信登录失败: (1048, "Column 'user_id' cannot be null")
[2025-07-04 23:53:30,565][apps.wx.views.wx_login():58] [INFO] 微信登录，openid: test_openid_123456
[2025-07-04 23:53:30,594][apps.wx.views.wx_login():135] [ERROR] 微信登录失败: (1364, "Field 'level' doesn't have a default value")
[2025-07-04 23:54:54,583][apps.wx.views.wx_login():58] [INFO] 微信登录，openid: test_openid_123456
[2025-07-04 23:54:54,623][apps.wx.views.wx_login():88] [INFO] 创建新用户: wx_d_123456, openid: test_openid_123456
[2025-07-04 23:55:20,107][apps.wx.utils.code2session():44] [INFO] 调用微信code2session接口，code: invalid_code
[2025-07-04 23:55:20,107][apps.wx.utils.code2session():49] [INFO] 微信API返回数据: {'errcode': 40029, 'errmsg': 'invalid code'}
[2025-07-04 23:55:20,107][apps.wx.utils.code2session():54] [ERROR] 微信API调用失败: 不合法的 code, errcode: 40029
[2025-07-04 23:55:20,143][apps.wx.utils.code2session():44] [INFO] 调用微信code2session接口，code: test_code
[2025-07-04 23:55:20,143][apps.wx.utils.code2session():49] [INFO] 微信API返回数据: {'openid': 'test_openid', 'session_key': 'test_session_key'}
[2025-07-04 23:55:20,360][apps.wx.views.wx_login():58] [INFO] 微信登录，openid: existing_openid_123456
[2025-07-04 23:55:20,413][apps.wx.views.wx_login():105] [INFO] 用户登录: wx_123456, openid: existing_openid_123456
[2025-07-04 23:55:20,502][apps.wx.views.wx_login():58] [INFO] 微信登录，openid: test_openid_123456
[2025-07-04 23:55:20,538][apps.wx.views.wx_login():88] [INFO] 创建新用户: wx_d_123456, openid: test_openid_123456
[2025-07-05 00:01:22,898][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
