<template>
	<view class="home-container">
		<!-- 顶部搜索栏 -->
		<view class="search-header">
			<view class="search-box" @click="goToSearch">
				<image class="search-icon" src="/static/icons/search.png"></image>
				<text class="search-placeholder">搜索植物名称、学名...</text>
			</view>
			<view class="user-avatar" @click="goToProfile">
				<image class="avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'"></image>
			</view>
		</view>
		
		<!-- 轮播图 -->
		<view class="banner-section">
			<swiper 
				class="banner-swiper" 
				indicator-dots 
				autoplay 
				circular
				indicator-color="rgba(255,255,255,0.5)"
				indicator-active-color="#4CAF50"
			>
				<swiper-item v-for="(banner, index) in banners" :key="index">
					<image class="banner-image" :src="banner.image" mode="aspectFill"></image>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-grid">
				<view 
					class="menu-item" 
					v-for="(menu, index) in menus" 
					:key="index"
					@click="handleMenuClick(menu)"
				>
					<view class="menu-icon">
						<image :src="menu.icon" mode="aspectFit"></image>
					</view>
					<text class="menu-text">{{ menu.name }}</text>
				</view>
			</view>
		</view>
		
		<!-- 推荐植物 -->
		<view class="recommend-section">
			<view class="section-header">
				<text class="section-title">推荐植物</text>
				<text class="section-more" @click="goToEncyclopedia">更多 ></text>
			</view>
			
			<scroll-view class="plant-scroll" scroll-x>
				<view class="plant-list">
					<view 
						class="plant-card" 
						v-for="(plant, index) in recommendPlants" 
						:key="index"
						@click="goToPlantDetail(plant)"
					>
						<image class="plant-image" :src="plant.main_image" mode="aspectFill"></image>
						<view class="plant-info">
							<text class="plant-name">{{ plant.name }}</text>
							<text class="plant-category">{{ plant.category }}</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 最新动态 -->
		<view class="news-section">
			<view class="section-header">
				<text class="section-title">植物资讯</text>
				<text class="section-more" @click="goToNews">更多 ></text>
			</view>
			
			<view class="news-list">
				<view 
					class="news-item" 
					v-for="(news, index) in newsList" 
					:key="index"
					@click="goToNewsDetail(news)"
				>
					<image class="news-image" :src="news.image" mode="aspectFill"></image>
					<view class="news-content">
						<text class="news-title">{{ news.title }}</text>
						<text class="news-summary">{{ news.summary }}</text>
						<text class="news-time">{{ formatTime(news.create_time) }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { useUserStore } from '@/store/user'
import { usePlantStore } from '@/store/plant'

export default {
	name: 'Home',
	data() {
		return {
			// 轮播图数据
			banners: [
				{
					image: '/static/images/banner1.jpg',
					title: '发现植物之美'
				},
				{
					image: '/static/images/banner2.jpg',
					title: '记录绿色生活'
				},
				{
					image: '/static/images/banner3.jpg',
					title: '植物养护指南'
				}
			],
			
			// 功能菜单
			menus: [
				{
					name: '植物识别',
					icon: '/static/icons/identify.png',
					path: '/pages/identify/identify'
				},
				{
					name: '植物百科',
					icon: '/static/icons/encyclopedia.png',
					path: '/pages/encyclopedia/encyclopedia'
				},
				{
					name: '我的植物',
					icon: '/static/icons/my-plants.png',
					path: '/pages/my-plants/my-plants'
				},
				{
					name: '养护提醒',
					icon: '/static/icons/reminder.png',
					path: '/pages/reminder/reminder'
				},
				{
					name: '植物社区',
					icon: '/static/icons/community.png',
					path: '/pages/community/community'
				},
				{
					name: '养护指南',
					icon: '/static/icons/guide.png',
					path: '/pages/guide/guide'
				}
			],
			
			// 推荐植物
			recommendPlants: [],
			
			// 资讯列表
			newsList: []
		}
	},
	
	computed: {
		userInfo() {
			const userStore = useUserStore()
			return userStore.userInfo
		}
	},
	
	onLoad() {
		this.checkLogin()
		this.loadData()
	},
	
	onShow() {
		// 页面显示时刷新数据
		this.loadData()
	},
	
	// 下拉刷新
	onPullDownRefresh() {
		this.loadData().finally(() => {
			uni.stopPullDownRefresh()
		})
	},
	
	methods: {
		// 检查登录状态
		checkLogin() {
			const token = uni.getStorageSync('token')
			if (!token) {
				uni.reLaunch({
					url: '/pages/login/login'
				})
			}
		},
		
		// 加载数据
		async loadData() {
			try {
				await Promise.all([
					this.loadRecommendPlants(),
					this.loadNews()
				])
			} catch (error) {
				console.error('加载数据失败:', error)
			}
		},
		
		// 加载推荐植物
		async loadRecommendPlants() {
			try {
				const plantStore = usePlantStore()
				this.recommendPlants = await plantStore.getRecommendPlants()
			} catch (error) {
				console.error('加载推荐植物失败:', error)
			}
		},
		
		// 加载资讯
		async loadNews() {
			try {
				// 这里调用资讯API
				this.newsList = [
					{
						id: 1,
						title: '春季植物养护要点',
						summary: '春季是植物生长的关键时期，需要注意浇水、施肥等养护要点...',
						image: '/static/images/news1.jpg',
						create_time: new Date()
					},
					{
						id: 2,
						title: '室内植物推荐',
						summary: '适合室内种植的植物品种推荐，让你的家更加绿意盎然...',
						image: '/static/images/news2.jpg',
						create_time: new Date()
					}
				]
			} catch (error) {
				console.error('加载资讯失败:', error)
			}
		},
		
		// 菜单点击
		handleMenuClick(menu) {
			if (menu.path) {
				uni.navigateTo({
					url: menu.path
				})
			}
		},
		
		// 跳转到搜索页面
		goToSearch() {
			uni.navigateTo({
				url: '/pages/search/search'
			})
		},
		
		// 跳转到个人中心
		goToProfile() {
			uni.navigateTo({
				url: '/pages/profile/profile'
			})
		},
		
		// 跳转到植物百科
		goToEncyclopedia() {
			uni.navigateTo({
				url: '/pages/encyclopedia/encyclopedia'
			})
		},
		
		// 跳转到植物详情
		goToPlantDetail(plant) {
			uni.navigateTo({
				url: `/pages/plant-detail/plant-detail?id=${plant.id}`
			})
		},
		
		// 跳转到资讯列表
		goToNews() {
			uni.navigateTo({
				url: '/pages/news/news'
			})
		},
		
		// 跳转到资讯详情
		goToNewsDetail(news) {
			uni.navigateTo({
				url: `/pages/news-detail/news-detail?id=${news.id}`
			})
		},
		
		// 格式化时间
		formatTime(time) {
			const date = new Date(time)
			const now = new Date()
			const diff = now - date
			
			if (diff < 60000) {
				return '刚刚'
			} else if (diff < 3600000) {
				return Math.floor(diff / 60000) + '分钟前'
			} else if (diff < 86400000) {
				return Math.floor(diff / 3600000) + '小时前'
			} else {
				return Math.floor(diff / 86400000) + '天前'
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.home-container {
	background: #f8f8f8;
	min-height: 100vh;
}

.search-header {
	display: flex;
	align-items: center;
	padding: 20rpx 30rpx;
	background: white;
	
	.search-box {
		flex: 1;
		display: flex;
		align-items: center;
		height: 70rpx;
		background: #f5f5f5;
		border-radius: 35rpx;
		padding: 0 30rpx;
		margin-right: 20rpx;
		
		.search-icon {
			width: 32rpx;
			height: 32rpx;
			margin-right: 20rpx;
		}
		
		.search-placeholder {
			color: #999;
			font-size: 28rpx;
		}
	}
	
	.user-avatar {
		.avatar {
			width: 60rpx;
			height: 60rpx;
			border-radius: 50%;
		}
	}
}

.banner-section {
	margin: 20rpx 30rpx;
	
	.banner-swiper {
		height: 300rpx;
		border-radius: $plant-card-radius;
		overflow: hidden;
		
		.banner-image {
			width: 100%;
			height: 100%;
		}
	}
}

.menu-section {
	background: white;
	margin: 20rpx 30rpx;
	border-radius: $plant-card-radius;
	padding: 40rpx 30rpx;
	
	.menu-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 40rpx;
		
		.menu-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			
			.menu-icon {
				width: 80rpx;
				height: 80rpx;
				margin-bottom: 20rpx;
				
				image {
					width: 100%;
					height: 100%;
				}
			}
			
			.menu-text {
				font-size: 24rpx;
				color: #333;
			}
		}
	}
}

.recommend-section, .news-section {
	margin: 20rpx 30rpx;
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		
		.section-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
		
		.section-more {
			font-size: 24rpx;
			color: $plant-color-primary;
		}
	}
}

.plant-scroll {
	white-space: nowrap;
	
	.plant-list {
		display: inline-flex;
		gap: 20rpx;
		
		.plant-card {
			width: 200rpx;
			background: white;
			border-radius: $plant-card-radius;
			overflow: hidden;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
			
			.plant-image {
				width: 100%;
				height: 150rpx;
			}
			
			.plant-info {
				padding: 20rpx;
				
				.plant-name {
					display: block;
					font-size: 26rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 10rpx;
				}
				
				.plant-category {
					font-size: 22rpx;
					color: #999;
				}
			}
		}
	}
}

.news-list {
	.news-item {
		display: flex;
		background: white;
		border-radius: $plant-card-radius;
		padding: 20rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
		
		.news-image {
			width: 120rpx;
			height: 120rpx;
			border-radius: 8rpx;
			margin-right: 20rpx;
		}
		
		.news-content {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			
			.news-title {
				font-size: 28rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 10rpx;
			}
			
			.news-summary {
				font-size: 24rpx;
				color: #666;
				line-height: 1.4;
				margin-bottom: 10rpx;
			}
			
			.news-time {
				font-size: 22rpx;
				color: #999;
			}
		}
	}
}
</style>
