/**
 * API接口统一管理
 */
import http from './request.js'
import { API_CONFIG } from './config.js'

// 用户相关API
export const userApi = {
	// 微信登录
	wechatLogin(data) {
		return http.post(API_CONFIG.ENDPOINTS.USER.WECHAT_LOGIN, data)
	},
	
	// 手机号登录
	phoneLogin(data) {
		return http.post(API_CONFIG.ENDPOINTS.USER.PHONE_LOGIN, data)
	},
	
	// 发送短信验证码
	sendSms(data) {
		return http.post(API_CONFIG.ENDPOINTS.USER.SEND_SMS, data)
	},
	
	// 获取用户信息
	getProfile() {
		return http.get(API_CONFIG.ENDPOINTS.USER.GET_PROFILE)
	},
	
	// 更新用户信息
	updateProfile(data) {
		return http.put(API_CONFIG.ENDPOINTS.USER.UPDATE_PROFILE, data)
	},
	
	// 退出登录
	logout() {
		return http.post(API_CONFIG.ENDPOINTS.USER.LOGOUT)
	}
}

// 植物百科相关API
export const plantApi = {
	// 获取植物列表
	getList(params = {}) {
		return http.get(API_CONFIG.ENDPOINTS.PLANT.LIST, params)
	},
	
	// 获取植物详情
	getDetail(id) {
		const url = API_CONFIG.ENDPOINTS.PLANT.DETAIL.replace('{id}', id)
		return http.get(url)
	},
	
	// 搜索植物
	search(params) {
		return http.get(API_CONFIG.ENDPOINTS.PLANT.SEARCH, params)
	},
	
	// 获取推荐植物
	getRecommend(params = {}) {
		return http.get(API_CONFIG.ENDPOINTS.PLANT.RECOMMEND, params)
	},
	
	// 获取植物分类
	getCategories() {
		return http.get(API_CONFIG.ENDPOINTS.PLANT.CATEGORIES)
	}
}

// 植物识别相关API
export const identifyApi = {
	// 上传图片识别
	upload(filePath, formData = {}) {
		return http.upload(API_CONFIG.ENDPOINTS.IDENTIFY.UPLOAD, filePath, formData, {
			name: 'image'
		})
	},
	
	// 获取识别结果
	getResult(id) {
		const url = API_CONFIG.ENDPOINTS.IDENTIFY.RESULT.replace('{id}', id)
		return http.get(url)
	},
	
	// 获取识别历史
	getHistory(params = {}) {
		return http.get(API_CONFIG.ENDPOINTS.IDENTIFY.HISTORY, params)
	}
}

// 用户植物相关API
export const userPlantApi = {
	// 获取用户植物列表
	getList(params = {}) {
		return http.get(API_CONFIG.ENDPOINTS.USER_PLANT.LIST, params)
	},
	
	// 创建用户植物
	create(data) {
		return http.post(API_CONFIG.ENDPOINTS.USER_PLANT.CREATE, data)
	},
	
	// 获取用户植物详情
	getDetail(id) {
		const url = API_CONFIG.ENDPOINTS.USER_PLANT.DETAIL.replace('{id}', id)
		return http.get(url)
	},
	
	// 更新用户植物
	update(id, data) {
		const url = API_CONFIG.ENDPOINTS.USER_PLANT.UPDATE.replace('{id}', id)
		return http.put(url, data)
	},
	
	// 删除用户植物
	delete(id) {
		const url = API_CONFIG.ENDPOINTS.USER_PLANT.DELETE.replace('{id}', id)
		return http.delete(url)
	}
}

// 提醒相关API
export const reminderApi = {
	// 获取提醒列表
	getList(params = {}) {
		return http.get(API_CONFIG.ENDPOINTS.REMINDER.LIST, params)
	},
	
	// 创建提醒
	create(data) {
		return http.post(API_CONFIG.ENDPOINTS.REMINDER.CREATE, data)
	},
	
	// 更新提醒
	update(id, data) {
		const url = API_CONFIG.ENDPOINTS.REMINDER.UPDATE.replace('{id}', id)
		return http.put(url, data)
	},
	
	// 删除提醒
	delete(id) {
		const url = API_CONFIG.ENDPOINTS.REMINDER.DELETE.replace('{id}', id)
		return http.delete(url)
	}
}

// 社区相关API
export const communityApi = {
	// 获取帖子列表
	getPosts(params = {}) {
		return http.get(API_CONFIG.ENDPOINTS.COMMUNITY.POSTS, params)
	},
	
	// 获取帖子详情
	getPostDetail(id) {
		const url = API_CONFIG.ENDPOINTS.COMMUNITY.POST_DETAIL.replace('{id}', id)
		return http.get(url)
	},
	
	// 创建帖子
	createPost(data) {
		return http.post(API_CONFIG.ENDPOINTS.COMMUNITY.CREATE_POST, data)
	},
	
	// 点赞帖子
	likePost(id) {
		const url = API_CONFIG.ENDPOINTS.COMMUNITY.LIKE_POST.replace('{id}', id)
		return http.post(url)
	},
	
	// 获取评论列表
	getComments(postId, params = {}) {
		const url = API_CONFIG.ENDPOINTS.COMMUNITY.COMMENTS.replace('{id}', postId)
		return http.get(url, params)
	},
	
	// 创建评论
	createComment(postId, data) {
		const url = API_CONFIG.ENDPOINTS.COMMUNITY.CREATE_COMMENT.replace('{id}', postId)
		return http.post(url, data)
	}
}

// 文件上传相关API
export const uploadApi = {
	// 上传图片
	uploadImage(filePath, formData = {}) {
		return http.upload(API_CONFIG.ENDPOINTS.UPLOAD.IMAGE, filePath, formData, {
			name: 'image'
		})
	},
	
	// 上传文件
	uploadFile(filePath, formData = {}) {
		return http.upload(API_CONFIG.ENDPOINTS.UPLOAD.FILE, filePath, formData, {
			name: 'file'
		})
	}
}

// 统一导出
const api = {
	user: userApi,
	plant: plantApi,
	identify: identifyApi,
	userPlant: userPlantApi,
	reminder: reminderApi,
	community: communityApi,
	upload: uploadApi
}

export default api
