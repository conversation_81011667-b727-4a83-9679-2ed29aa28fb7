<template>
  <view class="category-container">
    <!-- 搜索栏 -->
    <view class="search-section">
      <uni-search-bar 
        v-model="searchKeyword" 
        placeholder="搜索商品" 
        @confirm="handleSearch"
        @input="handleSearchInput"
        cancelButton="none"
      ></uni-search-bar>
    </view>

    <view class="category-content">
      <!-- 左侧分类列表 -->
      <scroll-view scroll-y class="category-sidebar">
        <view
          class="category-item"
          :class="{ active: currentCategoryId === category.categoryId }"
          v-for="category in categories"
          :key="category.categoryId"
          @click="selectCategory(category)"
        >
          <text>{{ category.name || category.categoryName }}</text>
        </view>
      </scroll-view>

      <!-- 右侧子分类和商品 -->
      <scroll-view scroll-y class="category-main">
        <!-- 子分类 -->
        <view class="subcategory-section" v-if="subCategories.length > 0">
          <view class="subcategory-title">子分类</view>
          <view class="subcategory-grid">
            <view 
              class="subcategory-item" 
              v-for="subCategory in subCategories" 
              :key="subCategory.categoryId"
              @click="goToGoodsList(subCategory)"
            >
              <image
                class="subcategory-icon"
                :src="getCategoryIcon(subCategory)"
                mode="aspectFit"
                @error="handleImageError"
              ></image>
              <text class="subcategory-name">{{ subCategory.name || subCategory.categoryName }}</text>
            </view>
          </view>
        </view>

        <!-- 推荐商品 -->
        <view class="goods-section" v-if="categoryGoods.length > 0">
          <view class="goods-title">推荐商品</view>
          <view class="goods-grid">
            <view 
              class="goods-item" 
              v-for="goods in categoryGoods" 
              :key="goods.goodsId"
              @click="goToGoodsDetail(goods)"
            >
              <image class="goods-image" :src="getFullImageUrl(goods.coverImg || goods.mainPic)" mode="aspectFill"></image>
              <view class="goods-info">
                <text class="goods-name">{{ goods.goodsName }}</text>
                <view class="price-row">
                  <text class="current-price">¥{{ formatPrice(goods.price || goods.sellPrice) }}</text>
                  <text class="original-price" v-if="(goods.originalPrice || goods.marketPrice) > (goods.price || goods.sellPrice)">¥{{ formatPrice(goods.originalPrice || goods.marketPrice) }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && subCategories.length === 0 && categoryGoods.length === 0">
          <uni-icons type="shop" size="60" color="#ccc"></uni-icons>
          <text>暂无商品</text>
        </view>
      </scroll-view>
    </view>

    <!-- 加载状态 -->
    <uni-load-more :status="loadStatus" v-if="loading"></uni-load-more>
  </view>
</template>

<script>
import { getCategories, getGoodsList } from '@/api/shop.js'
import { formatPrice, debounce } from '@/utils/shop.js'
import config from '@/config.js'

export default {
  data() {
    return {
      searchKeyword: '',
      categories: [],
      subCategories: [],
      categoryGoods: [],
      currentCategoryId: null,
      loading: false,
      loadStatus: 'loading'
    }
  },
  onLoad() {
    this.loadCategories()
  },
  methods: {
    // 加载分类数据
    async loadCategories() {
      this.loading = true
      try {
        const res = await getCategories({ parentId: 0 })
        this.categories = res.rows || []

        // 调试：打印分类数据
        console.log('=== 分类页面数据调试 ===')
        console.log('分类API响应:', res)
        console.log('分类列表:', this.categories)
        console.log('========================')

        // 默认选择第一个分类
        if (this.categories.length > 0) {
          this.selectCategory(this.categories[0])
        }
      } catch (error) {
        console.error('加载分类失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 选择分类
    async selectCategory(category) {
      this.currentCategoryId = category.categoryId
      this.loading = true
      
      try {
        // 加载子分类
        await this.loadSubCategories(category.categoryId)
        // 加载该分类下的商品
        await this.loadCategoryGoods(category.categoryId)
      } catch (error) {
        console.error('加载分类数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 加载子分类
    async loadSubCategories(parentId) {
      try {
        const res = await getCategories({ parentId })
        this.subCategories = res.rows || []

        // 调试：打印子分类数据
        console.log('=== 子分类数据调试 ===')
        console.log('子分类API响应:', res)
        console.log('子分类列表:', this.subCategories)
        if (this.subCategories.length > 0) {
          console.log('第一个子分类数据:', this.subCategories[0])
          console.log('图标字段检查:')
          console.log('- icon:', this.subCategories[0].icon)
          console.log('- iconUrl:', this.subCategories[0].iconUrl)
          console.log('- 所有字段:', Object.keys(this.subCategories[0]))
        }
        console.log('========================')
      } catch (error) {
        console.error('加载子分类失败:', error)
        this.subCategories = []
      }
    },

    // 加载分类商品
    async loadCategoryGoods(categoryId) {
      try {
        const res = await getGoodsList({
          categoryId,
          pageSize: 10,
          orderBy: 'sales_count desc'
        })
        this.categoryGoods = res.rows || []

        // 调试：打印商品数据
        console.log('=== 分类商品数据调试 ===')
        console.log('商品API响应:', res)
        console.log('商品列表:', this.categoryGoods)
        if (this.categoryGoods.length > 0) {
          console.log('第一个商品数据:', this.categoryGoods[0])
          console.log('图片字段检查:')
          console.log('- coverImg:', this.categoryGoods[0].coverImg)
          console.log('- mainPic:', this.categoryGoods[0].mainPic)
        }
        console.log('========================')
      } catch (error) {
        console.error('加载分类商品失败:', error)
        this.categoryGoods = []
      }
    },

    // 搜索输入
    handleSearchInput: debounce(function(value) {
      if (value.trim()) {
        this.handleSearch()
      }
    }, 500),

    // 执行搜索
    handleSearch() {
      if (!this.searchKeyword.trim()) return

      uni.navigateTo({
        url: `/pages/shop/search/index?keyword=${this.searchKeyword}`
      })
    },

    // 跳转到商品列表
    goToGoodsList(category) {
      // 简化跳转，只传递分类ID，通过API获取分类名称
      uni.navigateTo({
        url: `/pages/shop/goods/list?categoryId=${category.categoryId}`
      })
    },

    // 跳转到商品详情
    goToGoodsDetail(goods) {
      uni.navigateTo({
        url: `/pages/shop/goods/detail?goodsId=${goods.goodsId}`
      })
    },

    // 格式化价格
    formatPrice,

    // 获取完整的图片URL（用于商品图片等后端资源）
    getFullImageUrl(imageUrl) {
      if (!imageUrl) return '/static/images/goods/default.svg'
      if (imageUrl.startsWith('http')) return imageUrl

      // 如果是本地静态资源（以/static开头），直接返回
      if (imageUrl.startsWith('/static/')) {
        return imageUrl
      }

      // 否则拼接后端服务器地址
      return config.baseUrl + imageUrl
    },

    // 获取分类图标（本地静态资源）
    getCategoryIcon(category) {
      // 优先使用iconUrl字段，然后使用icon字段
      const iconName = category.iconUrl || category.icon

      console.log('获取分类图标:', {
        category: category.name || category.categoryName,
        iconUrl: category.iconUrl,
        icon: category.icon,
        iconName: iconName
      })

      if (!iconName) {
        console.log('没有图标名称，使用默认图标')
        return '/static/images/category/default.svg'
      }

      // 如果是完整路径（包含http或以/开头），直接返回
      if (iconName.startsWith('/') || iconName.startsWith('http')) {
        console.log('使用完整路径:', iconName)
        return iconName
      }

      // 否则拼接本地分类图标路径（注意：这是本地静态资源，不需要baseUrl）
      const localPath = `/static/images/category/${iconName}.svg`
      console.log('拼接本地图标路径:', localPath)
      return localPath
    },

    // 处理图片加载错误
    handleImageError(event) {
      console.log('图片加载失败:', event.target.src)
      // 设置默认图片（本地静态资源）
      event.target.src = '/static/images/category/default.svg'
    }
  }
}
</script>

<style lang="scss" scoped>
.category-container {
  height: 100vh;
  background-color: #f5f5f7;
  display: flex;
  flex-direction: column;
}

.search-section {
  background-color: #fff;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
}

.category-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.category-sidebar {
  width: 200rpx;
  background-color: #f8f8f8;
  
  .category-item {
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #eee;
    font-size: 28rpx;
    color: #333;
    
    &.active {
      background-color: #fff;
      color: #007aff;
      border-right: 4rpx solid #007aff;
    }
  }
}

.category-main {
  flex: 1;
  background-color: #fff;
  padding: 20rpx;
}

.subcategory-section {
  margin-bottom: 40rpx;
  
  .subcategory-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    color: #333;
  }
  
  .subcategory-grid {
    display: flex;
    flex-wrap: wrap;
    
    .subcategory-item {
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20rpx 10rpx;
      
      .subcategory-icon {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 10rpx;
      }
      
      .subcategory-name {
        font-size: 24rpx;
        color: #666;
        text-align: center;
      }
    }
  }
}

.goods-section {
  .goods-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    color: #333;
  }
  
  .goods-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    
    .goods-item {
      width: 48%;
      background-color: #f9f9f9;
      border-radius: 10rpx;
      overflow: hidden;
      margin-bottom: 20rpx;
      
      .goods-image {
        width: 100%;
        height: 240rpx;
      }
      
      .goods-info {
        padding: 15rpx;
        
        .goods-name {
          font-size: 26rpx;
          color: #333;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          margin-bottom: 10rpx;
        }
        
        .price-row {
          display: flex;
          align-items: center;
          
          .current-price {
            font-size: 28rpx;
            color: #ff6700;
            font-weight: bold;
          }
          
          .original-price {
            font-size: 22rpx;
            color: #999;
            text-decoration: line-through;
            margin-left: 10rpx;
          }
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #999;
  
  text {
    margin-top: 20rpx;
    font-size: 28rpx;
  }
}
</style>
