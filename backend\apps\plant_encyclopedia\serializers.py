"""
植物百科序列化器
"""
from rest_framework import serializers
from dvadmin.utils.serializers import CustomModelSerializer
from dvadmin.utils.validator import CustomUniqueValidator
from .models import PlantEncyclopedia


class PlantEncyclopediaSerializer(CustomModelSerializer):
    """
    植物百科-序列化器
    """
    short_description = serializers.SerializerMethodField()
    main_image_url = serializers.SerializerMethodField()
    image_count = serializers.ReadOnlyField()
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = PlantEncyclopedia
        fields = [
            'id', 'name', 'scientific_name', 'category', 'category_display',
            'family', 'genus', 'description', 'short_description', 'care_tips',
            'growth_habit', 'flowering_period', 'images', 'main_image',
            'main_image_url', 'image_count', 'source_url', 'source_site',
            'status', 'status_display', 'view_count', 'tags', 'search_keywords',
            'create_datetime', 'update_datetime'
        ]
        read_only_fields = ['id', 'view_count', 'create_datetime', 'update_datetime']
    
    def get_short_description(self, obj):
        """获取简短描述"""
        return obj.get_short_description()
    
    def get_main_image_url(self, obj):
        """获取主图URL"""
        return obj.get_main_image()


class PlantEncyclopediaListSerializer(CustomModelSerializer):
    """
    植物百科列表-序列化器（用于列表展示，字段较少）
    """
    short_description = serializers.SerializerMethodField()
    main_image_url = serializers.SerializerMethodField()
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = PlantEncyclopedia
        fields = [
            'id', 'name', 'scientific_name', 'category', 'category_display',
            'short_description', 'main_image_url', 'status', 'status_display',
            'view_count', 'create_datetime'
        ]
    
    def get_short_description(self, obj):
        """获取简短描述"""
        return obj.get_short_description(50)  # 列表页面使用更短的描述
    
    def get_main_image_url(self, obj):
        """获取主图URL"""
        return obj.get_main_image()


class PlantEncyclopediaCreateSerializer(CustomModelSerializer):
    """
    植物百科创建-序列化器
    """
    name = serializers.CharField(
        max_length=100,
        validators=[
            CustomUniqueValidator(queryset=PlantEncyclopedia.objects.all(), message="植物名称已存在")
        ]
    )
    
    class Meta:
        model = PlantEncyclopedia
        fields = [
            'name', 'scientific_name', 'category', 'family', 'genus',
            'description', 'care_tips', 'growth_habit', 'flowering_period',
            'images', 'main_image', 'source_url', 'source_site',
            'status', 'tags', 'search_keywords'
        ]
    
    def validate_images(self, value):
        """验证图片列表"""
        if value and not isinstance(value, list):
            raise serializers.ValidationError("图片列表必须是数组格式")
        return value
    
    def validate_tags(self, value):
        """验证标签列表"""
        if value and not isinstance(value, list):
            raise serializers.ValidationError("标签列表必须是数组格式")
        return value


class PlantEncyclopediaUpdateSerializer(CustomModelSerializer):
    """
    植物百科更新-序列化器
    """
    
    class Meta:
        model = PlantEncyclopedia
        fields = [
            'name', 'scientific_name', 'category', 'family', 'genus',
            'description', 'care_tips', 'growth_habit', 'flowering_period',
            'images', 'main_image', 'source_url', 'source_site',
            'status', 'tags', 'search_keywords'
        ]
    
    def validate_images(self, value):
        """验证图片列表"""
        if value and not isinstance(value, list):
            raise serializers.ValidationError("图片列表必须是数组格式")
        return value
    
    def validate_tags(self, value):
        """验证标签列表"""
        if value and not isinstance(value, list):
            raise serializers.ValidationError("标签列表必须是数组格式")
        return value


class PlantEncyclopediaSearchSerializer(serializers.Serializer):
    """
    植物百科搜索-序列化器
    """
    q = serializers.CharField(required=False, help_text="搜索关键词")
    category = serializers.ChoiceField(
        choices=PlantEncyclopedia.CATEGORY_CHOICES,
        required=False,
        help_text="植物分类"
    )
    status = serializers.ChoiceField(
        choices=PlantEncyclopedia.STATUS_CHOICES,
        required=False,
        help_text="状态筛选"
    )
    ordering = serializers.ChoiceField(
        choices=[
            ('id', 'ID升序'),
            ('-id', 'ID降序'),
            ('create_datetime', '创建时间升序'),
            ('-create_datetime', '创建时间降序'),
            ('view_count', '浏览量升序'),
            ('-view_count', '浏览量降序'),
            ('name', '名称升序'),
            ('-name', '名称降序'),
        ],
        required=False,
        default='id',  # 默认按ID正序排序
        help_text="排序方式"
    )
