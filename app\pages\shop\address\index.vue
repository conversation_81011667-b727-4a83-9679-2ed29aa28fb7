<template>
  <view class="address-container">
    <!-- 地址列表 -->
    <view class="address-list" v-if="processedAddressList.length > 0">
      <view
        class="address-item"
        v-for="(item, index) in processedAddressList"
        :key="item.uniqueId"
        @click="selectAddress(item)"
      >
        <view class="address-info">
          <view class="user-info">
            <text class="name">{{ item.name }}</text>
            <text class="phone">{{ item.phone }}</text>
            <text class="default-tag" v-if="item.isDefault">默认</text>
          </view>
          <view class="address-detail">
            <text class="region">{{ item.province }} {{ item.city }} {{ item.district }}</text>
            <text class="detail">{{ item.detailAddress }}</text>
          </view>
        </view>
        <view class="address-actions">
          <button class="edit-btn" @click.stop="editAddress(item)">编辑</button>
          <button
            class="default-btn"
            v-if="!item.isDefault"
            @click.stop="setDefault(item.addressId || item.id)"
          >
            设为默认
          </button>
          <button class="delete-btn" @click.stop="deleteAddress(item.addressId || item.id)">删除</button>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image class="empty-image" src="/static/images/empty-address.png" mode="aspectFit"></image>
      <text class="empty-text">暂无收货地址</text>
      <text class="empty-tip">添加收货地址，享受便捷购物体验</text>
    </view>
    
    <!-- 添加地址按钮 -->
    <view class="add-address-section">
      <button class="add-btn" @click="addAddress">
        <uni-icons type="plus" size="20" color="#fff"></uni-icons>
        <text>添加新地址</text>
      </button>
    </view>
  </view>
</template>

<script>
import { getAddressList, deleteAddress, setDefaultAddress } from '@/api/shop.js'

export default {
  data() {
    return {
      addressList: [],
      selectMode: false, // 是否为选择模式
      loading: false
    }
  },

  computed: {
    // 处理地址列表，确保每个地址都有唯一标识
    processedAddressList() {
      return this.addressList.map((item, index) => {
        return {
          ...item,
          uniqueId: item.addressId || item.id || `temp_${index}`,
          displayId: item.addressId || item.id
        }
      })
    }
  },
  
  onLoad(options) {
    console.log('=== 地址页面加载 ===')
    console.log('页面参数:', options)

    // 检查是否为选择地址模式
    if (options.select === '1') {
      this.selectMode = true
      console.log('设置为选择地址模式')
      uni.setNavigationBarTitle({
        title: '选择收货地址'
      })
    } else {
      console.log('普通地址管理模式')
    }

    this.loadAddressList()
  },

  onShow() {
    // 页面显示时重新加载数据（从编辑页面返回时会触发）
    this.loadAddressList()
  },
  
  methods: {
    // 加载地址列表
    async loadAddressList() {
      if (this.loading) return

      this.loading = true
      try {
        const res = await getAddressList()
        console.log('地址列表加载结果:', res)

        if (res.code === 200) {
          this.addressList = res.data || []
          console.log('加载的地址列表:', this.addressList)
          console.log('地址列表长度:', this.addressList.length)

          if (this.addressList.length > 0) {
            console.log('第一个地址的字段:', Object.keys(this.addressList[0]))
            console.log('第一个地址的数据:', this.addressList[0])

            // 验证每个地址的数据完整性
            this.addressList.forEach((address, index) => {
              console.log(`地址${index + 1}:`, address)
              if (!address.name || !address.phone) {
                console.warn(`地址${index + 1}数据不完整:`, address)
              }
            })
          } else {
            console.warn('地址列表为空')
          }
        } else {
          console.warn('加载地址列表返回错误:', res.msg)
          // 如果API调用失败，使用本地存储的地址数据作为备选
          this.loadLocalAddressList()
        }
      } catch (error) {
        console.error('加载地址列表失败:', error)
        // API调用失败时，尝试加载本地数据
        this.loadLocalAddressList()

        uni.showToast({
          title: '加载失败，显示本地数据',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 加载本地地址列表（备选方案）
    loadLocalAddressList() {
      try {
        const localAddresses = uni.getStorageSync('addressList') || []
        this.addressList = localAddresses
        console.log('加载本地地址数据:', localAddresses)
      } catch (error) {
        console.error('加载本地地址数据失败:', error)
        this.addressList = []
      }
    },

    // 保存地址到本地存储
    saveToLocalStorage() {
      try {
        uni.setStorageSync('addressList', this.addressList)
      } catch (error) {
        console.error('保存地址到本地失败:', error)
      }
    },
    
    // 选择地址
    selectAddress(address) {
      console.log('=== 点击选择地址 ===')
      console.log('选择模式:', this.selectMode)
      console.log('选中的地址:', address)
      console.log('地址类型:', typeof address)
      console.log('地址是否为空:', address === null || address === undefined)

      // 验证地址数据
      if (!address) {
        console.error('地址数据为空，无法选择')
        uni.showToast({
          title: '地址数据异常',
          icon: 'none'
        })
        return
      }

      if (this.selectMode) {
        // 选择模式下，返回选中的地址
        console.log('进入选择模式处理')
        const pages = getCurrentPages()
        console.log('当前页面栈长度:', pages.length)

        const prevPage = pages[pages.length - 2]
        console.log('上一页面:', prevPage)

        if (prevPage) {
          console.log('调用上一页面的setSelectedAddress方法')
          console.log('上一页面路由:', prevPage.route)

          if (prevPage.$vm && typeof prevPage.$vm.setSelectedAddress === 'function') {
            console.log('准备传递的地址数据:', address)
            prevPage.$vm.setSelectedAddress(address)
            console.log('成功调用setSelectedAddress')
          } else {
            console.error('上一页面没有setSelectedAddress方法')
            console.error('上一页面$vm:', prevPage.$vm)
          }
        } else {
          console.error('获取上一页面失败')
        }

        console.log('准备返回上一页')
        uni.navigateBack()
      } else {
        // 非选择模式下，进入编辑页面
        console.log('非选择模式，进入编辑页面')
        this.editAddress(address)
      }
    },
    
    // 添加地址
    addAddress() {
      uni.navigateTo({
        url: '/pages/shop/address/edit'
      })
    },
    
    // 编辑地址
    editAddress(address) {
      const addressId = address.addressId || address.id
      uni.navigateTo({
        url: `/pages/shop/address/edit?id=${addressId}`
      })
    },
    
    // 删除地址
    deleteAddress(addressId) {
      console.log('=== 前端删除地址开始 ===')
      console.log('要删除的地址ID:', addressId)
      console.log('当前地址列表:', this.addressList)

      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个收货地址吗？',
        success: async (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '删除中...'
            })

            try {
              // 调用删除API
              console.log('调用删除API，地址ID:', addressId)
              const result = await deleteAddress(addressId)
              console.log('删除地址API结果:', result)

              if (result.code === 200) {
                // API删除成功，从列表中移除
                console.log('API删除成功，开始从列表中移除')
                console.log('删除前列表长度:', this.addressList.length)

                this.addressList = this.addressList.filter(item => {
                  const itemId = item.addressId || item.id
                  console.log('比较ID:', itemId, '!==', addressId, '结果:', itemId !== addressId)
                  return itemId !== addressId
                })

                console.log('删除后列表长度:', this.addressList.length)
                this.saveToLocalStorage()

                uni.hideLoading()
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                })

                console.log('=== 前端删除地址完成 ===')
              } else {
                throw new Error(result.msg || '删除失败')
              }
            } catch (error) {
              console.error('删除地址失败:', error)

              // API调用失败，仍然进行本地删除
              this.addressList = this.addressList.filter(item =>
                (item.addressId || item.id) !== addressId
              )
              this.saveToLocalStorage()

              uni.hideLoading()
              uni.showToast({
                title: '删除成功（本地）',
                icon: 'success'
              })
            }
          }
        }
      })
    },

    // 设置默认地址
    async setDefault(addressId) {
      uni.showLoading({
        title: '设置中...'
      })

      try {
        // 调用设置默认地址API
        const result = await setDefaultAddress(addressId)
        console.log('设置默认地址API结果:', result)

        if (result.code === 200) {
          // API调用成功，更新本地数据
          this.addressList.forEach(item => {
            item.isDefault = item.id === addressId
          })
          this.saveToLocalStorage()

          uni.hideLoading()
          uni.showToast({
            title: '设置成功',
            icon: 'success'
          })
        } else {
          throw new Error(result.msg || '设置失败')
        }
      } catch (error) {
        console.error('设置默认地址失败:', error)

        // API调用失败，仍然进行本地设置
        this.addressList.forEach(item => {
          item.isDefault = item.id === addressId
        })
        this.saveToLocalStorage()

        uni.hideLoading()
        uni.showToast({
          title: '设置成功（本地）',
          icon: 'success'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.address-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.address-list {
  padding: 20rpx;
}

.address-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.address-info {
  flex: 1;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 20rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.default-tag {
  background-color: #ff6700;
  color: #fff;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.address-detail {
  display: flex;
  flex-direction: column;
}

.region {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.detail {
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
}

.address-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.edit-btn, .default-btn, .delete-btn {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
}

.edit-btn {
  background-color: #007aff;
  color: #fff;
}

.default-btn {
  background-color: #ff6700;
  color: #fff;
}

.delete-btn {
  background-color: #ff3b30;
  color: #fff;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-tip {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

.add-address-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.add-btn {
  width: 100%;
  height: 88rpx;
  background-color: #ff6700;
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}
</style>
