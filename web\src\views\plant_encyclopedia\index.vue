<template>
    <fs-page class="plant-encyclopedia-page">
        <!-- 数据展示区域 -->
        <div class="data-section">
            <fs-crud ref="crudRef" v-bind="crudBinding">
                <!-- 自定义主图显示 -->
                <template #cell_main_image_url="scope">
                    <div class="plant-image-wrapper">
                        <el-image
                            v-if="scope.row.main_image_url"
                            :src="scope.row.main_image_url"
                            :preview-src-list="[scope.row.main_image_url]"
                            class="plant-image"
                            fit="cover"
                            :preview-teleported="true"
                        >
                            <template #error>
                                <div class="image-error">
                                    <el-icon><Picture /></el-icon>
                                </div>
                            </template>
                        </el-image>
                        <div v-else class="no-image">
                            <el-icon><Picture /></el-icon>
                            <span>暂无图片</span>
                        </div>
                    </div>
                </template>

                <!-- 自定义状态显示 -->
                <template #cell_status="scope">
                    <el-tag
                        :type="getStatusType(scope.row.status)"
                        size="small"
                        effect="dark"
                    >
                        <el-icon class="tag-icon">
                            <component :is="getStatusIcon(scope.row.status)" />
                        </el-icon>
                        {{ getStatusText(scope.row.status) }}
                    </el-tag>
                </template>

                <!-- 自定义分类显示 -->
                <template #cell_category="scope">
                    <el-tag
                        type="info"
                        size="small"
                        effect="plain"
                        class="category-tag"
                    >
                        <el-icon class="tag-icon"><Collection /></el-icon>
                        {{ scope.row.category }}
                    </el-tag>
                </template>

                <!-- 自定义浏览次数显示 -->
                <template #cell_view_count="scope">
                    <div class="view-count">
                        <el-icon class="view-icon"><View /></el-icon>
                        <span class="view-number">{{ formatNumber(scope.row.view_count || 0) }}</span>
                    </div>
                </template>

                <!-- 操作栏右侧按钮 - 导入数据 -->
                <template #actionbar-right>
                    <importExcel
                        api="api/plant-encyclopedia/plants/"
                        v-auth="'PlantEncyclopedia:Import'"
                    >
                        <template #default>
                            <el-button type="success" size="small">
                                <el-icon><Upload /></el-icon>
                                导入数据
                            </el-button>
                        </template>
                    </importExcel>
                </template>
            </fs-crud>
        </div>
    </fs-page>
</template>

/**
 * 植物百科管理页面
 *
 * 功能特性：
 * 1. 数据管理 - 支持植物数据的增删改查和批量导入
 * 2. 响应式布局 - 适配不同屏幕尺寸，优化移动端体验
 * 3. 数据展示 - 优化的表格显示，支持图片预览和状态标识
 *
 * 设计理念：
 * - 简洁清晰的数据展示界面
 * - 完善的响应式设计，支持多端访问
 * - 优化的用户交互体验
 */
<script lang="ts">
// Vue 3 核心功能导入
import { onMounted, getCurrentInstance, defineComponent } from 'vue';
// FastCrud 表格组件
import { useFs } from '@fast-crud/fast-crud';
// 页面配置
import createCrudOptions from './crud';
import {
    View,           // 查看图标
    Picture,        // 图片图标
    Collection,     // 收藏图标
    Upload,         // 上传图标
    Edit,           // 编辑图标
    CircleCheck,    // 成功图标
    Warning,        // 警告图标
    InfoFilled      // 信息图标
} from '@element-plus/icons-vue';

// 导入自定义组件
import importExcel from '/@/components/importExcel/index.vue';

export default defineComponent({
    name: "PlantEncyclopedia",
    // 注册所有使用的组件和图标
    components: {
        importExcel,      // Excel导入组件
        View,             // 查看图标
        Picture,          // 图片图标
        Collection,       // 收藏图标
        Upload,           // 上传图标
        Edit,             // 编辑图标
        CircleCheck,      // 成功图标
        Warning,          // 警告图标
        InfoFilled        // 信息图标
    },
    setup() {
        // 获取当前组件实例
        const instance = getCurrentInstance();

        // FastCrud 上下文配置
        const context: any = {
            componentName: instance?.type.name
        };

        // 初始化 FastCrud 配置
        const { crudBinding, crudRef, crudExpose } = useFs({
            createCrudOptions,
            context
        });





        // ==================== 辅助方法 ====================
        /**
         * 获取状态对应的Element Plus标签类型
         * @param status 状态值 (0: 草稿, 1: 已发布, 2: 已下架)
         * @returns Element Plus标签类型
         */
        const getStatusType = (status: number) => {
            const statusMap: Record<number, string> = {
                0: 'info',      // 草稿 - 信息色
                1: 'success',   // 已发布 - 成功色
                2: 'warning'    // 已下架 - 警告色
            };
            return statusMap[status] || 'info';
        };

        /**
         * 获取状态对应的中文文本
         * @param status 状态值
         * @returns 状态中文描述
         */
        const getStatusText = (status: number) => {
            const statusMap: Record<number, string> = {
                0: '草稿',
                1: '已发布',
                2: '已下架'
            };
            return statusMap[status] || '未知';
        };

        /**
         * 获取状态对应的图标名称
         * @param status 状态值
         * @returns 图标组件名称
         */
        const getStatusIcon = (status: number) => {
            const iconMap: Record<number, string> = {
                0: 'Edit',          // 草稿 - 编辑图标
                1: 'CircleCheck',   // 已发布 - 成功图标
                2: 'Warning'        // 已下架 - 警告图标
            };
            return iconMap[status] || 'InfoFilled';
        };



        /**
         * 格式化数字显示
         * 将大数字转换为更易读的格式 (如: 1000 -> 1k, 10000 -> 1w)
         * @param num 原始数字
         * @returns 格式化后的字符串
         */
        const formatNumber = (num: number) => {
            if (num >= 10000) {
                return (num / 10000).toFixed(1) + 'w';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'k';
            }
            return num.toString();
        };

        // ==================== 页面生命周期 ====================
        /**
         * 页面挂载完成后的初始化操作
         */
        onMounted(() => {
            crudExpose.doRefresh();  // 刷新表格数据
        });

        // ==================== 导出给模板使用的数据和方法 ====================
        return {
            // FastCrud 相关
            crudBinding,              // 表格配置绑定
            crudRef,                  // 表格组件引用

            // 辅助方法
            getStatusType,            // 获取状态类型方法
            getStatusText,            // 获取状态文本方法
            getStatusIcon,            // 获取状态图标方法
            formatNumber,             // 数字格式化方法
        };
    }
});
</script>

<style scoped>
/* 页面整体样式 */
.plant-encyclopedia-page {
    padding: 16px;
    background: var(--el-bg-color-page);
    min-height: 100vh;
    height: 100%;
}

/* 数据展示区域样式 */
.data-section {
    background: var(--el-color-white);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 16px;
    overflow: hidden;
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
}

/* FastCrud 表格容器样式 */
:deep(.fs-crud) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.fs-crud .el-card) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.fs-crud .el-card__body) {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

:deep(.fs-table) {
    flex: 1;
    overflow: hidden;
}

:deep(.fs-table .el-table) {
    height: 100%;
}

:deep(.fs-table .el-table__body-wrapper) {
    overflow-y: auto;
}



/* 植物图片样式 */
.plant-image-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}

.plant-image {
    width: 64px;
    height: 64px;
    border-radius: 8px;
    border: 2px solid var(--el-border-color-light);
    transition: all 0.3s ease;
}

.plant-image:hover {
    border-color: var(--el-color-primary);
    transform: scale(1.05);
}

.image-error,
.no-image {
    width: 64px;
    height: 64px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--el-fill-color-light);
    border-radius: 8px;
    color: var(--el-text-color-placeholder);
    font-size: 12px;
}

.image-error .el-icon,
.no-image .el-icon {
    font-size: 20px;
    margin-bottom: 4px;
}

/* 标签样式 */
.category-tag,
.el-tag {
    border-radius: 6px;
    font-weight: 500;
}

.tag-icon {
    margin-right: 4px;
    font-size: 12px;
}

/* 浏览量显示样式 */
.view-count {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--el-text-color-regular);
    font-weight: 500;
}

.view-icon {
    font-size: 16px;
    color: var(--el-color-primary);
}

.view-number {
    font-weight: 600;
}





/* 确保页面高度正确 */
:deep(.layout-padding-view) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.fs-page) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .plant-encyclopedia-page {
        padding: 8px;
    }

    .data-section {
        height: calc(100vh - 100px);
    }

    .plant-image {
        width: 48px;
        height: 48px;
    }

    .image-error,
    .no-image {
        width: 48px;
        height: 48px;
    }

    :deep(.el-table .el-table__row td) {
        padding: 8px 0;
    }

    :deep(.el-table td .cell) {
        padding: 0 8px;
    }

    /* 移动端搜索表单间距调整 */
    :deep(.fs-search-layout-default) {
        margin-bottom: 6px; /* 移动端进一步减少间距 */
    }

    /* 移动端按钮布局调整 - 保持单行但允许滚动，更紧凑 */
    :deep(.fs-actionbar) {
        flex-direction: row; /* 保持水平排列 */
        justify-content: flex-start;
        align-items: center;
        gap: 6px; /* 移动端进一步减少间距 */
        overflow-x: auto; /* 允许水平滚动 */
        padding: 6px 12px; /* 移动端大幅减少内边距 */
        margin-top: 0;
        flex-wrap: nowrap; /* 强制不换行 */
        background: #ffffff !important; /* 移动端也使用白色背景 */
        min-height: auto;
    }

    :deep(.fs-actionbar .fs-actionbar-left) {
        display: flex;
        align-items: center;
        gap: 6px; /* 减少按钮间距 */
        flex-shrink: 0;
    }

    :deep(.fs-actionbar .fs-actionbar-right) {
        display: flex;
        align-items: center;
        gap: 6px; /* 减少按钮间距 */
        flex-shrink: 0;
        margin-left: 0;
    }

    /* 移动端按钮样式调整 - 更紧凑 */
    :deep(.fs-actionbar .el-button) {
        font-size: 12px;
        padding: 4px 8px; /* 减少按钮内边距 */
        height: 24px; /* 减少按钮高度 */
        min-width: auto;
        flex-shrink: 0;
        line-height: 1.2;
    }
}

/* 中等屏幕适配 - 紧凑设计 */
@media (max-width: 1024px) and (min-width: 769px) {
    /* 中等屏幕搜索表单间距调整 */
    :deep(.fs-search-layout-default) {
        margin-bottom: 7px;
    }

    :deep(.fs-actionbar) {
        gap: 8px; /* 中等屏幕减少间距 */
        padding: 7px 14px; /* 中等屏幕减少内边距 */
        margin-top: 0;
        background: #ffffff !important; /* 中等屏幕也使用白色背景 */
        min-height: auto;
    }

    :deep(.fs-actionbar .fs-actionbar-left) {
        gap: 8px; /* 减少按钮间距 */
    }

    :deep(.fs-actionbar .fs-actionbar-right) {
        gap: 8px; /* 减少按钮间距 */
    }

    :deep(.fs-actionbar .el-button) {
        font-size: 13px;
        padding: 5px 10px; /* 减少按钮内边距 */
        height: 26px; /* 减少按钮高度 */
        line-height: 1.2;
    }
}

/* 动画效果 */
.stat-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}



/* 表格样式优化 */
:deep(.el-table) {
    font-size: 14px;
}

:deep(.el-table .el-table__row) {
    height: auto;
    min-height: 60px;
}

:deep(.el-table .el-table__row td) {
    padding: 12px 0;
    vertical-align: middle;
}

:deep(.el-table .el-table__row:hover) {
    background-color: var(--el-fill-color-light);
}

:deep(.el-table .el-table__header-wrapper) {
    background: var(--el-fill-color-lighter);
}

:deep(.el-table .el-table__header-wrapper th) {
    padding: 12px 0;
    font-weight: 600;
    color: var(--el-text-color-primary);
}

/* 表格单元格内容样式 */
:deep(.el-table .el-table__body-wrapper) {
    overflow-x: auto;
}

:deep(.el-table td .cell) {
    padding: 0 12px;
    line-height: 1.5;
}

/* 导入按钮样式 */
:deep(.fs-actionbar-right .import-excel-component) {
    display: inline-block;
}

/* 分页器样式 */
:deep(.fs-pagination) {
    padding: 16px;
    border-top: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-lighter);
    margin-top: auto;
}

:deep(.fs-pagination .el-pagination) {
    justify-content: center;
}

/* 搜索表单和操作栏间距调整 */
:deep(.fs-search-layout-default) {
    margin-bottom: 8px; /* 减少搜索表单底部间距 */
}

/* 操作栏样式 - 单行水平布局，紧凑设计 */
:deep(.fs-actionbar) {
    padding: 8px 12px; /* 进一步减少内边距，使工具栏更紧凑 */
    margin-top: 0; /* 移除顶部外边距 */
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: #ffffff; /* 修改为白色背景 */
    display: flex;
    justify-content: flex-start; /* 改为左对齐，让所有按钮在一行 */
    align-items: center;
    flex-wrap: nowrap; /* 强制不换行 */
    gap: 10px; /* 减少按钮间距 */
    min-height: auto; /* 允许容器高度自适应 */
}

/* 操作栏左侧区域 - 包含搜索、重置、新增、导出按钮 */
:deep(.fs-actionbar .fs-actionbar-left) {
    display: flex;
    align-items: center;
    gap: 10px; /* 减少按钮间距 */
    flex-wrap: nowrap; /* 强制不换行 */
}

/* 操作栏右侧区域 - 包含导入按钮 */
:deep(.fs-actionbar .fs-actionbar-right) {
    display: flex;
    align-items: center;
    gap: 10px; /* 减少按钮间距 */
    flex-shrink: 0;
    margin-left: 0; /* 移除左边距，让按钮紧密排列 */
}

/* 确保所有操作栏按钮样式一致 - 紧凑设计 */
:deep(.fs-actionbar .el-button) {
    white-space: nowrap;
    flex-shrink: 0;
    height: 28px; /* 减少按钮高度 */
    font-size: 13px; /* 稍微减小字体 */
    padding: 6px 12px; /* 减少按钮内边距 */
    border-radius: 4px;
    transition: all 0.3s ease;
    line-height: 1.2; /* 调整行高 */
}

/* 按钮类型样式优化 */
:deep(.fs-actionbar .el-button--primary) {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
}

:deep(.fs-actionbar .el-button--success) {
    background-color: var(--el-color-success);
    border-color: var(--el-color-success);
}

:deep(.fs-actionbar .el-button--warning) {
    background-color: var(--el-color-warning);
    border-color: var(--el-color-warning);
}

/* 大屏幕优化 - 紧凑设计 */
@media (min-width: 1025px) {
    /* 大屏幕搜索表单间距调整 */
    :deep(.fs-search-layout-default) {
        margin-bottom: 8px;
    }

    :deep(.fs-actionbar) {
        gap: 12px; /* 大屏幕适中间距 */
        padding: 8px 16px; /* 大屏幕也减少内边距 */
        margin-top: 0;
        background: #ffffff !important; /* 大屏幕也使用白色背景 */
        min-height: auto;
    }

    :deep(.fs-actionbar .fs-actionbar-left) {
        gap: 12px; /* 减少按钮间距 */
    }

    :deep(.fs-actionbar .fs-actionbar-right) {
        gap: 12px; /* 减少按钮间距 */
    }

    :deep(.fs-actionbar .el-button) {
        padding: 6px 14px; /* 减少按钮内边距 */
        font-size: 13px; /* 稍微减小字体 */
        height: 30px; /* 减少按钮高度 */
        line-height: 1.2;
    }
}

/* 卡片阴影优化 */
.data-section {
    transition: box-shadow 0.3s ease;
}

.data-section:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}
</style>
