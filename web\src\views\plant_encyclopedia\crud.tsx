import { CrudOptions, AddReq, DelReq, EditReq, dict, CrudExpose, UserPageQuery, CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import _ from 'lodash-es';
import * as api from './api';
import { request } from '/@/utils/service';
import { auth } from "/@/utils/authFunction";
import { ElMessage, ElTag, ElImage } from 'element-plus';

// 植物分类选项
const categoryOptions = [
    { value: '草本植物', label: '草本植物' },
    { value: '木本植物', label: '木本植物' },
    { value: '藤本植物', label: '藤本植物' },
    { value: '兰科植物', label: '兰科植物' },
    { value: '水生植物', label: '水生植物' },
    { value: '球根植物', label: '球根植物' },
    { value: '宿根植物', label: '宿根植物' },
];

// 状态选项
const statusOptions = [
    { value: 0, label: '草稿', color: 'info' },
    { value: 1, label: '已发布', color: 'success' },
    { value: 2, label: '已下架', color: 'warning' },
];

export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
    const pageRequest = async (query: any) => {
        // 处理搜索参数转换
        const transformedQuery = { ...query };

        // 植物名称和学名搜索转换为Django REST Framework的search参数
        if (query.name || query.scientific_name) {
            const searchTerms = [];
            if (query.name) searchTerms.push(query.name);
            if (query.scientific_name) searchTerms.push(query.scientific_name);
            transformedQuery.search = searchTerms.join(' ');

            // 删除原始的name和scientific_name参数，避免冲突
            delete transformedQuery.name;
            delete transformedQuery.scientific_name;
        }

        return await api.GetList(transformedQuery);
    };
    
    const editRequest = async ({ form, row }: EditReq) => {
        if (row.id) {
            form.id = row.id;
        }
        return await api.UpdateObj(form);
    };
    
    const delRequest = async ({ row }: DelReq) => {
        return await api.DelObj(row.id);
    };
    
    const addRequest = async ({ form }: AddReq) => {
        return await api.AddObj(form);
    };

    const exportRequest = async (query: UserPageQuery) => {
        return await api.exportData(query);
    };

    return {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest,
            },
            table: {
                // 设置默认排序：按ID从小到大（正序）
                defaultSort: {
                    prop: 'id',
                    order: 'ascending'
                }
            },
            actionbar: {
                buttons: {
                    search: {
                        show: true,
                        text: "搜索",
                        type: "primary",
                        order: 1,
                        click() {
                            // 执行搜索操作，使用当前搜索表单数据
                            crudExpose.doSearch({ form: crudExpose.getSearchFormData() });
                        }
                    },
                    reset: {
                        show: true,
                        text: "重置",
                        type: "default",
                        order: 2,
                        click() {
                            // 重置搜索表单并刷新数据
                            crudExpose.setSearchFormData({ form: {} });
                            crudExpose.doRefresh();
                        }
                    },
                    add: {
                        show: auth('PlantEncyclopedia:Create'),
                        text: "新增植物",
                        type: "success",
                        order: 3,
                    },
                    export: {
                        show: auth('PlantEncyclopedia:Export'),
                        text: "导出",
                        type: "warning",
                        order: 4,
                        title: "导出植物百科数据",
                        click() {
                            return exportRequest(crudExpose.getSearchFormData());
                        }
                    },

                }
            },
            search: {
                show: true,
                initialForm: {},
                options: {
                    layout: 'multi-line'
                },
                col: { span: 6 },
                buttons: {
                    search: { show: false }, // 隐藏搜索按钮，使用actionbar中的
                    reset: { show: false }   // 隐藏重置按钮，使用actionbar中的
                }
            },
            rowHandle: {
                fixed: 'right',
                width: 250,
                buttons: {
                    view: {
                        type: 'text',
                        order: 1,
                        show: auth('PlantEncyclopedia:Retrieve'),
                        text: '查看'
                    },
                    edit: {
                        type: 'text',
                        order: 2,
                        show: auth('PlantEncyclopedia:Update'),
                        text: '编辑'
                    },
                    copy: {
                        type: 'text',
                        order: 3,
                        show: auth('PlantEncyclopedia:Copy'),
                        text: '复制'
                    },
                    remove: {
                        type: 'text',
                        order: 4,
                        show: auth('PlantEncyclopedia:Delete'),
                        text: '删除'
                    },
                },
            },
            columns: {
                id: {
                    title: 'ID',
                    key: 'id',
                    type: 'number',
                    column: {
                        width: 80,
                        sortable: true, // 启用ID列排序
                    },
                    form: {
                        show: false,
                    },
                },
                name: {
                    title: '植物名称',
                    type: 'text',
                    search: {
                        show: true,
                        component: {
                            placeholder: '请输入植物名称进行搜索',
                        }
                    },
                    column: {
                        width: 150,
                        sortable: true,
                    },
                    form: {
                        rules: [
                            { required: true, message: '请输入植物名称' },
                            { max: 100, message: '植物名称不能超过100个字符' }
                        ],
                        component: {
                            placeholder: '请输入植物名称',
                        },
                    },
                },
                scientific_name: {
                    title: '学名',
                    type: 'text',
                    search: {
                        show: true,
                        component: {
                            placeholder: '请输入植物学名进行搜索',
                        }
                    },
                    column: {
                        width: 180,
                    },
                    form: {
                        component: {
                            placeholder: '请输入植物学名',
                        },
                    },
                },
                category: {
                    title: '分类',
                    type: 'dict-select',
                    search: {
                        show: true,
                        component: {
                            placeholder: '请选择植物分类进行筛选',
                        }
                    },
                    dict: dict({
                        data: categoryOptions
                    }),
                    column: {
                        width: 120,
                        component: {
                            name: 'fs-dict-select',
                            color: 'auto',
                        },
                    },
                    form: {
                        rules: [{ required: true, message: '请选择植物分类' }],
                        component: {
                            placeholder: '请选择植物分类',
                        },
                    },
                },
                main_image_url: {
                    title: '主图',
                    type: 'image-uploader',
                    column: {
                        width: 100,
                        component: {
                            name: 'el-image',
                            style: { width: '60px', height: '60px' },
                            fit: 'cover',
                            previewSrcList: (scope: any) => [scope.row.main_image_url],
                        },
                    },
                    form: {
                        component: {
                            name: 'fs-file-uploader',
                            accept: '.jpg,.jpeg,.png,.gif',
                            limit: 1,
                            uploader: {
                                type: 'form',
                                buildUrl: () => '/api/common/upload/',
                            },
                        },
                    },
                },
                short_description: {
                    title: '简短描述',
                    type: 'text',
                    column: {
                        width: 200,
                        ellipsis: true,
                    },
                    form: {
                        show: false,
                    },
                },
                status: {
                    title: '状态',
                    type: 'dict-select',
                    search: {
                        show: true,
                        component: {
                            placeholder: '请选择状态进行筛选',
                        }
                    },
                    dict: dict({
                        data: statusOptions
                    }),
                    column: {
                        width: 100,
                        component: {
                            name: 'fs-dict-select',
                            color: 'auto',
                        },
                    },
                    form: {
                        value: 1,
                        component: {
                            placeholder: '请选择状态',
                        },
                    },
                },
                view_count: {
                    title: '浏览次数',
                    type: 'number',
                    column: {
                        width: 100,
                        sortable: true,
                    },
                    form: {
                        show: false,
                    },
                },
                family: {
                    title: '科',
                    type: 'text',
                    column: {
                        width: 120,
                        show: false, // 默认隐藏，可在列设置中显示
                    },
                    form: {
                        component: {
                            placeholder: '请输入植物科名',
                        },
                    },
                },
                genus: {
                    title: '属',
                    type: 'text',
                    column: {
                        width: 120,
                        show: false, // 默认隐藏，可在列设置中显示
                    },
                    form: {
                        component: {
                            placeholder: '请输入植物属名',
                        },
                    },
                },
                description: {
                    title: '植物描述',
                    type: 'textarea',
                    column: {
                        show: false, // 列表中不显示
                    },
                    form: {
                        component: {
                            name: 'el-input',
                            type: 'textarea',
                            rows: 4,
                            placeholder: '请输入植物详细描述',
                        },
                    },
                },
                care_tips: {
                    title: '养护技巧',
                    type: 'textarea',
                    column: {
                        show: false, // 列表中不显示
                    },
                    form: {
                        component: {
                            name: 'el-input',
                            type: 'textarea',
                            rows: 3,
                            placeholder: '请输入养护技巧',
                        },
                    },
                },
                growth_habit: {
                    title: '生长习性',
                    type: 'textarea',
                    column: {
                        show: false, // 列表中不显示
                    },
                    form: {
                        component: {
                            name: 'el-input',
                            type: 'textarea',
                            rows: 3,
                            placeholder: '请输入生长习性',
                        },
                    },
                },
                flowering_period: {
                    title: '花期',
                    type: 'text',
                    column: {
                        width: 120,
                        show: false, // 默认隐藏，可在列设置中显示
                    },
                    form: {
                        component: {
                            placeholder: '请输入花期，如：春季、3-5月',
                        },
                    },
                },
                images: {
                    title: '图片列表',
                    type: 'file-uploader',
                    column: {
                        show: false, // 列表中不显示
                    },
                    form: {
                        component: {
                            name: 'fs-file-uploader',
                            accept: '.jpg,.jpeg,.png,.gif',
                            limit: 10,
                            multiple: true,
                            uploader: {
                                type: 'form',
                                buildUrl: () => '/api/common/upload/',
                            },
                        },
                    },
                },
                source_url: {
                    title: '来源URL',
                    type: 'text',
                    column: {
                        show: false, // 列表中不显示
                    },
                    form: {
                        component: {
                            placeholder: '请输入数据来源URL',
                        },
                    },
                },
                source_site: {
                    title: '来源网站',
                    type: 'text',
                    column: {
                        width: 100,
                        show: false, // 默认隐藏，可在列设置中显示
                    },
                    form: {
                        value: '花百科',
                        component: {
                            placeholder: '请输入来源网站',
                        },
                    },
                },
                tags: {
                    title: '标签',
                    type: 'text',
                    column: {
                        show: false, // 列表中不显示
                    },
                    form: {
                        component: {
                            placeholder: '请输入标签，多个标签用逗号分隔',
                        },
                        helper: '多个标签用逗号分隔，如：观叶植物,室内植物,净化空气',
                    },
                },
                search_keywords: {
                    title: '搜索关键词',
                    type: 'text',
                    column: {
                        show: false, // 列表中不显示
                    },
                    form: {
                        component: {
                            placeholder: '请输入搜索关键词',
                        },
                        helper: '用于提高搜索匹配度的关键词',
                    },
                },
                create_datetime: {
                    title: '创建时间',
                    type: 'datetime',
                    column: {
                        width: 160,
                        sortable: true,
                    },
                    form: {
                        show: false,
                    },
                },
            },
        },
    };
}
