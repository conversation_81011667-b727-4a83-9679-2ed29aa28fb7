{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/hanqc/project/pyProject/PlantHome/app/App.vue?1edd", "uni-app:///App.vue", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/App.vue?48bf", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/App.vue?5216"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "use", "plugins", "config", "productionTip", "prototype", "$store", "store", "getDicts", "App", "mpType", "app", "$mount", "globalData", "onLaunch", "methods", "initApp", "initConfig", "checkLogin", "uni", "url"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AACA;AACA;AACA;AAAiD;AAAA;AANjD;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAO1DC,YAAG,CAACC,GAAG,CAACC,gBAAO,CAAC;AAEhBF,YAAG,CAACG,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCJ,YAAG,CAACK,SAAS,CAACC,MAAM,GAAGC,cAAK;AAC5BP,YAAG,CAACK,SAAS,CAACG,QAAQ,GAAGA,cAAQ;AAEjCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAElB,IAAMC,GAAG,GAAG,IAAIX,YAAG,mBACdS,YAAG,EACN;AAEF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;ACrBZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACkL;AAClL,gBAAgB,yLAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAA2qB,CAAgB,yqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACC/rB;AACA;AAAA,eAEA;EACAC;IACAV;EACA;EACAW;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;;MAEA;MACA;QACAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpCA;AAAA;AAAA;AAAA;AAAkxC,CAAgB,wsCAAG,EAAC,C;;;;;;;;;;;ACAtyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import Vue from 'vue'\r\nimport App from './App'\r\nimport store from './store' // store\r\nimport plugins from './plugins' // plugins\r\nimport './permission' // permission\r\nimport { getDicts } from \"@/api/system/dict/data\"\r\n\r\nVue.use(plugins)\r\n\r\nVue.config.productionTip = false\r\nVue.prototype.$store = store\r\nVue.prototype.getDicts = getDicts\r\n\r\nApp.mpType = 'app'\r\n\r\nconst app = new Vue({\r\n  ...App\r\n})\r\n\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n  import config from './config'\r\n  import { getToken } from '@/utils/auth'\r\n\r\n  export default {\r\n    globalData: {\r\n      config: {}\r\n    },\r\n    onLaunch: function() {\r\n      this.initApp()\r\n    },\r\n    methods: {\r\n      // 初始化应用\r\n      initApp() {\r\n        // 初始化应用配置\r\n        this.initConfig()\r\n        // 检查用户登录状态\r\n        this.checkLogin()\r\n      },\r\n      initConfig() {\r\n        this.globalData.config = config\r\n      },\r\n      checkLogin() {\r\n        // 获取当前页面路径\r\n        const pages = getCurrentPages()\r\n        const currentPage = pages[pages.length - 1]\r\n        const currentRoute = currentPage ? currentPage.route : ''\r\n\r\n        // 如果没有登录且不在登录页面，跳转到登录页面\r\n        if (!getToken() && currentRoute !== 'pages/login') {\r\n          uni.reLaunch({\r\n            url: '/pages/login'\r\n          })\r\n        }\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n  @import '@/static/scss/index.scss';\r\n  \r\n  /* 全局样式 */\r\n  page {\r\n    background-color: #f5f5f5;\r\n    font-size: 28rpx;\r\n    color: #333;\r\n    font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751678035515\n      var cssReload = require(\"D:/System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}