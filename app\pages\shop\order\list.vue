<template>
  <view class="order-list-container">
    <!-- 状态筛选 -->
    <view class="status-tabs">
      <view 
        class="tab-item" 
        :class="{ active: currentStatus === item.value }"
        v-for="item in statusTabs" 
        :key="item.value"
        @click="switchStatus(item.value)"
      >
        <text class="tab-text">{{ item.label }}</text>
        <view class="tab-badge" v-if="item.count > 0">{{ item.count }}</view>
      </view>
    </view>
    
    <!-- 订单列表 -->
    <scroll-view 
      class="order-content" 
      scroll-y 
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="order-item" v-for="order in orderList" :key="order.orderId" @click="goToDetail(order.orderId)">
        <!-- 订单头部 -->
        <view class="order-header">
          <text class="order-no">订单号：{{ order.orderNo }}</text>
          <text class="order-status" :class="'status-' + order.orderStatus">{{ getStatusText(order.orderStatus) }}</text>
        </view>
        
        <!-- 商品列表 -->
        <view class="goods-list">
          <view class="goods-item" v-for="item in (order.orderItems || [])" :key="item.itemId">
            <image class="goods-image" :src="getFullImageUrl(item.picUrl)" mode="aspectFill"></image>
            <view class="goods-info">
              <text class="goods-name">{{ item.goodsName }}</text>
              <text class="goods-spec" v-if="item.skuName">{{ item.skuName }}</text>
              <view class="goods-price-qty">
                <text class="goods-price">¥{{ formatPrice(item.price) }}</text>
                <text class="goods-qty">x{{ item.quantity }}</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 订单金额 -->
        <view class="order-amount">
          <text class="amount-label">实付款：</text>
          <text class="amount-value">¥{{ formatPrice(order.payAmount) }}</text>
        </view>
        
        <!-- 操作按钮 -->
        <view class="order-actions">
          <button class="action-btn secondary" @click.stop="cancelOrder(order)" v-if="order.orderStatus === '0'">取消订单</button>
          <button class="action-btn primary" @click.stop="payOrder(order)" v-if="order.orderStatus === '0'">立即支付</button>
          <button class="action-btn secondary" @click.stop="confirmOrder(order)" v-if="order.orderStatus === '2'">确认收货</button>
          <button class="action-btn secondary" @click.stop="deleteOrder(order)" v-if="order.orderStatus === '3' || order.orderStatus === '4'">删除订单</button>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-order" v-if="orderList.length === 0 && !loading">
        <image class="empty-image" src="/static/images/empty-order.png" mode="aspectFit"></image>
        <text class="empty-text">暂无订单</text>
        <text class="empty-tip">快去选购心仪的商品吧</text>
        <button class="go-shopping-btn" @click="goShopping">去购物</button>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <text class="load-text">{{ loading ? '加载中...' : '上拉加载更多' }}</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getOrderList, cancelOrder as cancelOrderApi, confirmOrder as confirmOrderApi, deleteOrder as deleteOrderApi } from '@/api/shop.js'
import { formatPrice } from '@/utils/shop.js'
import config from '@/config.js'

export default {
  data() {
    return {
      currentStatus: '',
      orderList: [],
      loading: false,
      refreshing: false,
      hasMore: true,
      pageNum: 1,
      pageSize: 10,
      statusTabs: [
        { label: '全部', value: '', count: 0 },
        { label: '待付款', value: '0', count: 0 },
        { label: '待发货', value: '1', count: 0 },
        { label: '待收货', value: '2', count: 0 },
        { label: '已完成', value: '3', count: 0 }
      ]
    }
  },
  
  onLoad(options) {
    if (options.status) {
      this.currentStatus = options.status
    }
    this.loadOrderList()
    this.loadOrderStatistics()
  },
  
  onShow() {
    // 从其他页面返回时刷新列表
    this.refreshOrderList()
  },
  
  methods: {
    // 切换状态
    switchStatus(status) {
      if (this.currentStatus === status) return
      
      this.currentStatus = status
      this.refreshOrderList()
    },
    
    // 刷新订单列表
    refreshOrderList() {
      this.pageNum = 1
      this.hasMore = true
      this.orderList = []
      this.loadOrderList()
    },
    
    // 加载订单列表
    async loadOrderList() {
      if (this.loading || !this.hasMore) return
      
      this.loading = true
      try {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
        
        if (this.currentStatus) {
          params.orderStatus = this.currentStatus
        }
        
        const res = await getOrderList(params)
        const newList = res.rows || []
        
        if (this.pageNum === 1) {
          this.orderList = newList
        } else {
          this.orderList.push(...newList)
        }
        
        this.hasMore = newList.length === this.pageSize
        this.pageNum++
        
      } catch (error) {
        console.error('加载订单列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    // 加载订单统计
    async loadOrderStatistics() {
      try {
        // 这里可以调用统计API获取各状态的订单数量
        // const res = await getOrderStatistics()
        // 更新statusTabs中的count
      } catch (error) {
        console.error('加载订单统计失败:', error)
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.refreshing = true
      this.refreshOrderList()
    },
    
    // 加载更多
    loadMore() {
      this.loadOrderList()
    },
    
    // 跳转到订单详情
    goToDetail(orderId) {
      uni.navigateTo({
        url: `/pages/shop/order/detail?orderId=${orderId}`
      })
    },
    
    // 支付订单
    payOrder(order) {
      uni.navigateTo({
        url: `/pages/shop/order/pay?orderId=${order.orderId}`
      })
    },
    
    // 取消订单
    cancelOrder(order) {
      uni.showModal({
        title: '提示',
        content: '确定要取消这个订单吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await cancelOrderApi(order.orderId)
              order.orderStatus = '4'
              uni.showToast({
                title: '取消成功',
                icon: 'success'
              })
            } catch (error) {
              console.error('取消订单失败:', error)
              uni.showToast({
                title: '取消失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 确认收货
    confirmOrder(order) {
      uni.showModal({
        title: '提示',
        content: '确定已收到商品吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await confirmOrderApi(order.orderId)
              order.orderStatus = '3'
              uni.showToast({
                title: '确认收货成功',
                icon: 'success'
              })
            } catch (error) {
              console.error('确认收货失败:', error)
              uni.showToast({
                title: '确认收货失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 删除订单
    deleteOrder(order) {
      uni.showModal({
        title: '提示',
        content: '确定要删除这个订单吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await deleteOrderApi(order.orderId)
              const index = this.orderList.findIndex(item => item.orderId === order.orderId)
              if (index > -1) {
                this.orderList.splice(index, 1)
              }
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
            } catch (error) {
              console.error('删除订单失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 去购物
    goShopping() {
      uni.switchTab({
        url: '/pages/index/index'
      })
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '待付款',
        '1': '待发货',
        '2': '待收货',
        '3': '已完成',
        '4': '已取消'
      }
      return statusMap[status] || '未知状态'
    },


    
    // 格式化价格
    formatPrice,
    
    // 获取完整图片URL
    getFullImageUrl(imagePath) {
      if (!imagePath) return '/static/images/default-goods.png'
      if (imagePath.startsWith('http')) return imagePath
      return config.baseUrl + imagePath
    }
  }
}
</script>

<style lang="scss" scoped>
.order-list-container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.status-tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  position: relative;
  text-align: center;
  padding: 30rpx 20rpx;
  
  &.active {
    .tab-text {
      color: #ff6700;
      font-weight: 600;
    }
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 4rpx;
      background-color: #ff6700;
      border-radius: 2rpx;
    }
  }
}

.tab-text {
  font-size: 28rpx;
  color: #666;
}

.tab-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: #ff6700;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
}

.order-content {
  flex: 1;
  padding: 20rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-no {
  font-size: 26rpx;
  color: #999;
}

.order-status {
  font-size: 26rpx;
  font-weight: 600;

  &.status-0 {
    color: #ff6700; /* 待付款 */
  }

  &.status-1 {
    color: #1890ff; /* 待发货 */
  }

  &.status-2 {
    color: #1890ff; /* 待收货 */
  }

  &.status-3 {
    color: #52c41a; /* 已完成 */
  }

  &.status-4 {
    color: #999; /* 已取消 */
  }
}

.goods-list {
  margin-bottom: 20rpx;
}

.goods-item {
  display: flex;
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.goods-spec {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 30rpx;
  color: #ff6700;
  font-weight: 600;
}

.goods-qty {
  font-size: 28rpx;
  color: #666;
}

.order-amount {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
}

.amount-value {
  font-size: 32rpx;
  color: #ff6700;
  font-weight: 600;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 50rpx;
  font-size: 26rpx;
  border: 1rpx solid #ddd;
  
  &.primary {
    background-color: #ff6700;
    color: #fff;
    border-color: #ff6700;
  }
  
  &.secondary {
    background-color: #fff;
    color: #666;
  }
}

.empty-order {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.empty-tip {
  font-size: 28rpx;
  color: #ccc;
  margin-bottom: 40rpx;
}

.go-shopping-btn {
  background-color: #ff6700;
  color: #fff;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: none;
}

.load-more {
  text-align: center;
  padding: 40rpx;
}

.load-text {
  font-size: 26rpx;
  color: #999;
}
</style>
