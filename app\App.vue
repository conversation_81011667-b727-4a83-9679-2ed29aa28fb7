<template>
	<view id="app">
		<!-- 应用入口 -->
	</view>
</template>

<script>
import { useUserStore, useAppStore, usePlantStore } from '@/store/index.js'

export default {
	name: 'App',
	onLaunch: function() {
		console.log('App Launch')
		this.initApp()
	},
	onShow: function() {
		console.log('App Show')
		// 检查网络状态
		this.checkNetworkStatus()
	},
	onHide: function() {
		console.log('App Hide')
	},
	methods: {
		// 初始化应用
		async initApp() {
			try {
				// 初始化store
				const userStore = useUserStore()
				const appStore = useAppStore()
				const plantStore = usePlantStore()

				// 初始化应用状态
				await appStore.initApp()

				// 初始化用户状态
				userStore.initUserState()

				// 初始化植物数据
				plantStore.initSearchHistory()

				// 检查登录状态
				this.checkLogin()

				// 监听网络状态变化
				appStore.watchNetworkStatus()

				// 检查应用更新
				appStore.checkUpdate()

			} catch (error) {
				console.error('应用初始化失败:', error)
			}
		},

		// 检查登录状态
		checkLogin() {
			const userStore = useUserStore()

			if (!userStore.checkLoginStatus()) {
				// 如果没有登录，跳转到登录页
				uni.reLaunch({
					url: '/pages/login/login'
				})
			} else {
				// 刷新用户信息
				this.refreshUserInfo()
			}
		},

		// 刷新用户信息
		async refreshUserInfo() {
			try {
				const userStore = useUserStore()
				await userStore.getUserInfo()
			} catch (error) {
				console.error('刷新用户信息失败:', error)
				// 如果刷新失败，可能是token过期，清除登录状态
				if (error.statusCode === 401) {
					userStore.clearUserState()
				}
			}
		},

		// 检查网络状态
		checkNetworkStatus() {
			const appStore = useAppStore()
			appStore.getNetworkType()
		}
	}
}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import '@/uni.scss';
	
	/* 全局样式 */
	page {
		background-color: $uni-bg-color-grey;
		font-size: $uni-font-size-base;
		color: $uni-text-color;
	}
	
	/* 通用类 */
	.container {
		padding: 20rpx;
	}
	
	.flex {
		display: flex;
	}
	
	.flex-center {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.flex-between {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.text-center {
		text-align: center;
	}
	
	.text-right {
		text-align: right;
	}
	
	.text-primary {
		color: $plant-color-primary;
	}
	
	.text-secondary {
		color: $plant-color-secondary;
	}
	
	.bg-primary {
		background-color: $plant-color-primary;
	}
	
	.bg-light {
		background-color: $plant-color-light-green;
	}
	
	.border-radius {
		border-radius: $plant-card-radius;
	}
	
	.shadow {
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
	
	/* 按钮样式 */
	.btn {
		padding: 20rpx 40rpx;
		border-radius: $plant-button-radius;
		font-size: $uni-font-size-base;
		text-align: center;
		border: none;
	}
	
	.btn-primary {
		background-color: $plant-color-primary;
		color: white;
	}
	
	.btn-secondary {
		background-color: $plant-color-secondary;
		color: white;
	}
	
	/* 输入框样式 */
	.input {
		padding: 20rpx;
		border: 1rpx solid $uni-border-color;
		border-radius: $plant-input-radius;
		font-size: $uni-font-size-base;
	}
	
	/* 卡片样式 */
	.card {
		background-color: white;
		border-radius: $plant-card-radius;
		padding: 30rpx;
		margin: 20rpx 0;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
</style>
