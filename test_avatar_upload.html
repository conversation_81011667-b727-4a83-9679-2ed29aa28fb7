<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>头像上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-case {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .success { border-left: 4px solid #4CAF50; }
        .error { border-left: 4px solid #f44336; }
        .warning { border-left: 4px solid #ff9800; }
        .code {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🖼️ 微信头像获取 + 多选项头像上传解决方案</h1>
        
        <div class="test-case success">
            <h3>✅ 问题分析</h3>
            <p><strong>原始错误:</strong></p>
            <div class="code">
[渲染层错误] [Component] &lt;button&gt;: chooseAvatar:fail Error: ENOENT: no such file or directory, 
open 'C:\Users\<USER>\AppData\Local\微信开发者工具\User Data\1feee665d096db5fde065585ac6476cc\WeappSimulator\WeappFileSystem\o6zAJszA7AnSU5j0nNfY5wRqYke4\wx5489fe73e28e75a9\tmp\Uen6VeIl7L786a51cf3d17464a02581949c0ac082fb3.jpeg'
            </div>
            <p><strong>问题根因:</strong></p>
            <ul>
                <li>微信开发者工具的模拟器环境下，chooseAvatar API 可能存在临时文件路径问题</li>
                <li>文件路径指向的临时文件可能已被清理或不存在</li>
                <li>前端缺少错误处理和备用方案</li>
            </ul>
        </div>

        <div class="test-case success">
            <h3>🔧 智能解决方案</h3>
            <p><strong>1. 环境自适应头像选择</strong></p>
            <div class="code">
// 根据运行环境提供不同选项
chooseAvatarImage() {
  const itemList = ['从相册选择', '拍照']

  // 只在真机环境提供微信头像选项（避免开发者工具ENOENT错误）
  if (this.canUseWechatAvatar()) {
    itemList.unshift('使用微信头像')
  }

  uni.showActionSheet({
    itemList: itemList,
    success: (res) => {
      const selectedOption = itemList[res.tapIndex]

      if (selectedOption === '使用微信头像') {
        this.chooseWechatAvatar()
      } else if (selectedOption === '从相册选择') {
        this.chooseFromAlbum()
      } else if (selectedOption === '拍照') {
        this.chooseFromCamera()
      }
    }
  })
}
            </div>

            <p><strong>2. 环境自适应微信头像获取</strong></p>
            <div class="code">
// 根据环境使用不同的获取策略
chooseWechatAvatar() {
  const systemInfo = uni.getSystemInfoSync()

  if (systemInfo.platform === 'devtools') {
    // 开发者工具：使用安全方法
    this.getWechatAvatarInDevtools()
  } else {
    // 真机：使用完整功能
    this.getWechatAvatarOnDevice()
  }
}

// 开发者工具中的安全实现
getWechatAvatarInDevtools() {
  uni.getUserProfile({
    desc: '用于完善用户资料',
    success: (res) => {
      const avatarUrl = res.userInfo.avatarUrl
      if (avatarUrl) {
        this.userInfo.avatar = avatarUrl
        this.downloadAndUploadWechatAvatar(avatarUrl)
      } else {
        // 提供测试头像或使用已存储的头像
        this.tryGetStoredUserInfo()
      }
    },
    fail: () => {
      this.tryGetStoredUserInfo()
    }
  })
}
            </div>

            <p><strong>2. 添加备用头像选择方案</strong></p>
            <div class="code">
// 备用头像选择方案
chooseAvatarFallback() {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0]
      this.userInfo.avatar = tempFilePath
      this.uploadAvatar(tempFilePath)
    }
  })
}
            </div>

            <p><strong>3. 改进上传错误处理</strong></p>
            <div class="code">
fail: (err) => {
  console.error('上传请求失败:', err);
  
  // 如果是文件路径问题，尝试备用方案
  if (err.errMsg && err.errMsg.includes('ENOENT')) {
    uni.showModal({
      title: '提示',
      content: '头像文件读取失败，是否尝试重新选择？',
      success: (res) => {
        if (res.confirm) {
          this.chooseAvatarFallback()
        }
      }
    })
  }
}
            </div>
        </div>

        <div class="test-case warning">
            <h3>⚠️ 重要测试说明</h3>
            <div class="test-case success">
                <h4>🔧 开发者工具测试</h4>
                <ul>
                    <li><strong>预期行为:</strong> 显示"使用微信头像"、"从相册选择"、"拍照"三个选项</li>
                    <li><strong>微信头像处理:</strong> 使用安全的 getUserProfile 方法获取头像</li>
                    <li><strong>备选方案:</strong> 如果无法获取，提供测试头像或降级到相册选择</li>
                    <li><strong>优势:</strong> 完全避免 chooseAvatar 的 ENOENT 错误</li>
                </ul>
            </div>

            <div class="test-case success">
                <h4>📱 真机测试</h4>
                <ul>
                    <li><strong>预期行为:</strong> 显示"使用微信头像"、"从相册选择"、"拍照"三个选项</li>
                    <li><strong>微信头像功能:</strong> 可以正常获取和使用微信头像</li>
                    <li><strong>测试步骤:</strong>
                        <ol>
                            <li>使用微信扫码预览小程序</li>
                            <li>进入个人资料页面</li>
                            <li>点击头像，选择"使用微信头像"</li>
                            <li>验证头像获取和上传功能</li>
                        </ol>
                    </li>
                </ul>
            </div>

            <div class="test-case">
                <h4>🔍 调试检查</h4>
                <ul>
                    <li>确认 /api/system/file/ 端点正常工作</li>
                    <li>验证返回的头像URL格式正确</li>
                    <li>检查JWT认证是否正常</li>
                    <li>观察控制台日志输出</li>
                </ul>
            </div>
        </div>

        <div class="test-case success">
            <h3>🎯 预期结果</h3>
            <ul>
                <li>✅ <strong>全环境微信头像支持</strong> - 开发者工具和真机都显示"使用微信头像"选项</li>
                <li>✅ <strong>安全的实现方式</strong> - 避免使用有问题的 chooseAvatar API</li>
                <li>✅ <strong>多种头像来源</strong> - 微信头像、相册选择、拍照三种方式</li>
                <li>✅ <strong>智能降级处理</strong> - 开发者工具中提供测试头像备选方案</li>
                <li>✅ <strong>完整的功能流程</strong> - 选择→获取→下载→显示→上传→保存→同步</li>
            </ul>
        </div>

        <div class="test-case warning">
            <h3>💡 开发者工具中的微信头像处理（已解决下载问题）</h3>
            <p><strong>问题分析：</strong></p>
            <div class="code">
// 原始错误：微信头像URL在开发者工具中无法下载
downloadFile:fail Error: ENOENT: no such file or directory
            </div>

            <p><strong>解决方案：环境自适应处理</strong></p>
            <ol>
                <li><strong>开发者工具环境：</strong>
                    <ul>
                        <li>不使用 downloadFile 下载微信头像</li>
                        <li>直接将微信头像URL发送给后端处理</li>
                        <li>如果后端不支持，直接使用微信头像URL</li>
                    </ul>
                </li>
                <li><strong>真机环境：</strong>
                    <ul>
                        <li>正常使用 downloadFile 下载微信头像</li>
                        <li>下载成功后上传到服务器</li>
                    </ul>
                </li>
            </ol>

            <div class="code">
// 环境自适应处理
downloadAndUploadWechatAvatar(avatarUrl) {
  const systemInfo = uni.getSystemInfoSync()

  if (systemInfo.platform === 'devtools') {
    // 开发者工具：直接发送URL给后端
    this.uploadWechatAvatarUrl(avatarUrl)
  } else {
    // 真机：正常下载流程
    this.downloadWechatAvatarOnDevice(avatarUrl)
  }
}
            </div>

            <p><strong>优势：</strong></p>
            <ul>
                <li>✅ 完全解决开发者工具中的下载错误</li>
                <li>✅ 保持真机上的完整功能</li>
                <li>✅ 提供多重降级方案</li>
                <li>✅ 用户体验一致</li>
            </ul>
        </div>

        <div class="test-case">
            <h3>🔍 调试信息</h3>
            <p>在控制台查看这些关键日志：</p>
            <div class="code">
// 环境检测日志
console.log('当前环境:', systemInfo.platform);

// 微信头像获取日志
console.log('getUserProfile成功:', res);
console.log('使用已存储的头像:', storedUserInfo.avatarUrl);

// 上传相关日志
console.log('开始上传头像:', filePath);
console.log('上传URL:', uploadUrl);
console.log('上传解析后响应:', data);
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">📋 修复文件清单</h2>
        <div class="test-case">
            <h3>已修复的文件:</h3>
            <ul>
                <li><strong>app/pages/mine/profile/index.vue</strong> - 个人资料页面头像上传功能</li>
                <li><strong>app/utils/request.js</strong> - 请求工具状态码处理</li>
                <li><strong>app/utils/upload.js</strong> - 上传工具状态码处理</li>
                <li><strong>app/utils/auth.js</strong> - 认证工具状态码处理</li>
            </ul>
        </div>

        <div class="test-case warning">
            <h3>🔧 后端API扩展建议（可选）</h3>
            <p>为了更好地支持开发者工具中的微信头像功能，可以考虑在后端添加微信头像URL处理：</p>
            <div class="code">
# 在 backend/dvadmin/system/views/file_list.py 中添加
class FileViewSet(CustomModelViewSet):
    def create(self, request, *args, **kwargs):
        # 检查是否是微信头像URL上传
        if request.data.get('type') == 'wechat_avatar':
            avatar_url = request.data.get('avatar_url')
            if avatar_url:
                # 后端下载微信头像并保存
                return self.handle_wechat_avatar_url(avatar_url)

        # 正常的文件上传流程
        return super().create(request, *args, **kwargs)

    def handle_wechat_avatar_url(self, avatar_url):
        # 下载微信头像并保存到本地
        # 返回本地文件URL
        pass
            </div>
            <p><strong>注意：</strong> 这是可选的优化，即使不实现，前端也会降级到直接使用微信头像URL。</p>
        </div>
    </div>

    <script>
        console.log('头像上传修复验证页面已加载');
        console.log('请在微信开发者工具中测试头像上传功能');
    </script>
</body>
</html>
