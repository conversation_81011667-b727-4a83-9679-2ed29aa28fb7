<template>
  <view class="success-container">
    <!-- 成功状态 -->
    <view class="success-content">
      <view class="success-icon">
        <image src="/static/images/success.png" mode="aspectFit"></image>
      </view>
      <text class="success-title">支付成功</text>
      <text class="success-desc">订单支付成功，我们将尽快为您发货</text>
      
      <!-- 订单信息 -->
      <view class="order-info" v-if="orderInfo.orderNo">
        <view class="info-item">
          <text class="label">订单号：</text>
          <text class="value">{{ orderInfo.orderNo }}</text>
        </view>
        <view class="info-item">
          <text class="label">支付金额：</text>
          <text class="value amount">¥{{ formatPrice(orderInfo.payAmount || orderInfo.totalAmount) }}</text>
        </view>
        <view class="info-item">
          <text class="label">支付方式：</text>
          <text class="value">微信支付</text>
        </view>
        <view class="info-item">
          <text class="label">支付时间：</text>
          <text class="value">{{ formatTime(orderInfo.payTime) }}</text>
        </view>
        <view class="info-item" v-if="orderInfo.receiverName">
          <text class="label">收货人：</text>
          <text class="value">{{ orderInfo.receiverName }} {{ orderInfo.receiverPhone }}</text>
        </view>
        <view class="info-item" v-if="orderInfo.receiverAddress">
          <text class="label">收货地址：</text>
          <text class="value">{{ orderInfo.receiverAddress }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="secondary-btn" @click="goToOrderList">查看订单</button>
      <button class="primary-btn" @click="goToHome">继续购物</button>
    </view>
  </view>
</template>

<script>
import { getOrderInfo } from '@/api/shop.js'
import { formatPrice } from '@/utils/shop.js'

export default {
  data() {
    return {
      orderId: null,
      orderInfo: {}
    }
  },
  
  onLoad(options) {
    console.log('=== 支付成功页面加载 ===')
    console.log('页面参数:', options)

    if (options.orderId) {
      this.orderId = options.orderId
      this.loadOrderInfo()

      // 设置页面标题
      uni.setNavigationBarTitle({
        title: '支付成功'
      })
    }
  },

  onShow() {
    // 页面显示时刷新订单信息
    if (this.orderId) {
      this.loadOrderInfo()
    }
  },
  
  methods: {
    formatPrice,
    
    // 加载订单信息
    async loadOrderInfo() {
      try {
        console.log('加载订单信息:', this.orderId)

        uni.showLoading({
          title: '加载中...'
        })

        const res = await getOrderInfo(this.orderId)
        console.log('订单信息返回:', res)

        uni.hideLoading()

        if (res && res.code === 200 && res.data) {
          this.orderInfo = res.data

          // 检查支付状态
          if (this.orderInfo.payStatus !== '1') {
            console.warn('订单支付状态异常:', this.orderInfo.payStatus)
            uni.showModal({
              title: '提示',
              content: '订单支付状态异常，请联系客服处理',
              showCancel: false
            })
          }
        } else {
          console.error('获取订单信息失败:', res)
          uni.showToast({
            title: res.msg || '获取订单信息失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载订单信息异常:', error)
        uni.hideLoading()
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    },
    
    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    
    // 跳转到订单列表
    goToOrderList() {
      uni.redirectTo({
        url: '/pages/shop/order/list'
      })
    },
    
    // 跳转到首页
    goToHome() {
      uni.switchTab({
        url: '/pages/index'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.success-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60rpx 30rpx;
}

.success-content {
  background: white;
  border-radius: 20rpx;
  padding: 80rpx 40rpx 60rpx;
  text-align: center;
  margin-bottom: 60rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  
  .success-icon {
    width: 120rpx;
    height: 120rpx;
    margin: 0 auto 40rpx;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .success-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #52c41a;
    margin-bottom: 20rpx;
  }
  
  .success-desc {
    display: block;
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
    margin-bottom: 40rpx;
  }
  
  .order-info {
    text-align: left;
    border-top: 1rpx solid #eee;
    padding-top: 30rpx;
    
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        font-size: 26rpx;
        color: #666;
      }
      
      .value {
        font-size: 26rpx;
        color: #333;
        
        &.amount {
          color: #ff4757;
          font-weight: bold;
        }
      }
    }
  }
}

.action-buttons {
  display: flex;
  gap: 30rpx;
  width: 100%;
  max-width: 600rpx;
  
  button {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    border: none;
    
    &.primary-btn {
      background: linear-gradient(135deg, #007aff, #0056d3);
      color: white;
    }
    
    &.secondary-btn {
      background: #f8f9fa;
      color: #666;
      border: 1rpx solid #ddd;
    }
  }
}
</style>
