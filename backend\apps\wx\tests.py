from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from unittest.mock import patch, MagicMock
import json

from .models import WxUser, WxLoginLog
from .utils import WxApiClient, generate_username_from_openid, create_default_user_info

User = get_user_model()


class WxLoginTestCase(TestCase):
    """微信登录测试用例"""
    
    def setUp(self):
        self.client = Client()
        self.login_url = reverse('wx:wx_login')
        
    @patch('apps.wx.utils.WxApiClient.code2session')
    def test_wx_login_new_user(self, mock_code2session):
        """测试新用户微信登录"""
        # 模拟微信API返回
        mock_code2session.return_value = {
            'openid': 'test_openid_123456',
            'session_key': 'test_session_key',
            'unionid': 'test_unionid'
        }
        
        # 准备登录数据
        login_data = {
            'code': 'test_wx_code',
            'userInfo': {
                'nickName': '测试用户',
                'avatarUrl': 'https://example.com/avatar.jpg',
                'gender': 1
            }
        }
        
        # 发送登录请求
        response = self.client.post(
            self.login_url,
            data=json.dumps(login_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data['code'], 2000)
        self.assertIn('token', response_data['data'])
        self.assertIn('user', response_data['data'])
        self.assertTrue(response_data['data']['is_new_user'])
        
        # 验证数据库记录
        wx_user = WxUser.objects.get(openid='test_openid_123456')
        self.assertEqual(wx_user.nickname, '测试用户')
        self.assertEqual(wx_user.gender, 1)
        self.assertTrue(wx_user.user.is_active)
        
        # 验证登录日志
        login_log = WxLoginLog.objects.filter(wx_user=wx_user).first()
        self.assertIsNotNone(login_log)
        self.assertTrue(login_log.login_result)
    
    @patch('apps.wx.utils.WxApiClient.code2session')
    def test_wx_login_existing_user(self, mock_code2session):
        """测试已存在用户的微信登录"""
        # 创建已存在的用户
        system_user = User.objects.create(
            username='wx_123456',
            name='已存在用户',
            user_type=1
        )
        wx_user = WxUser.objects.create(
            user=system_user,
            openid='existing_openid_123456',
            nickname='已存在用户',
            session_key='old_session_key'
        )
        
        # 模拟微信API返回
        mock_code2session.return_value = {
            'openid': 'existing_openid_123456',
            'session_key': 'new_session_key',
            'unionid': 'test_unionid'
        }
        
        # 准备登录数据
        login_data = {
            'code': 'test_wx_code',
            'userInfo': {
                'nickName': '更新昵称',
                'avatarUrl': 'https://example.com/new_avatar.jpg',
                'gender': 2
            }
        }
        
        # 发送登录请求
        response = self.client.post(
            self.login_url,
            data=json.dumps(login_data),
            content_type='application/json'
        )
        
        # 验证响应
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data['code'], 2000)
        self.assertFalse(response_data['data']['is_new_user'])
        
        # 验证用户信息更新
        wx_user.refresh_from_db()
        self.assertEqual(wx_user.session_key, 'new_session_key')
        self.assertEqual(wx_user.nickname, '更新昵称')
        self.assertEqual(wx_user.gender, 2)
    
    def test_wx_login_invalid_data(self):
        """测试无效数据的登录请求"""
        # 缺少code参数
        login_data = {
            'userInfo': {
                'nickName': '测试用户'
            }
        }
        
        response = self.client.post(
            self.login_url,
            data=json.dumps(login_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data['code'], 400)
        self.assertIn('参数验证失败', response_data['msg'])


class WxUtilsTestCase(TestCase):
    """微信工具函数测试用例"""
    
    def test_generate_username_from_openid(self):
        """测试从openid生成用户名"""
        openid = 'test_openid_abcdef123456'
        username = generate_username_from_openid(openid)
        self.assertEqual(username, 'wx_ef123456')
    
    def test_create_default_user_info(self):
        """测试创建默认用户信息"""
        openid = 'test_openid'
        user_info = {
            'nickName': '测试用户',
            'avatarUrl': 'https://example.com/avatar.jpg',
            'gender': 1,
            'country': '中国',
            'province': '广东',
            'city': '深圳'
        }
        
        default_info = create_default_user_info(openid, user_info)
        
        self.assertEqual(default_info['nickname'], '测试用户')
        self.assertEqual(default_info['avatar_url'], 'https://example.com/avatar.jpg')
        self.assertEqual(default_info['gender'], 1)
        self.assertEqual(default_info['country'], '中国')
        self.assertEqual(default_info['province'], '广东')
        self.assertEqual(default_info['city'], '深圳')
    
    def test_create_default_user_info_empty(self):
        """测试创建默认用户信息（空数据）"""
        openid = 'test_openid'
        default_info = create_default_user_info(openid, None)
        
        self.assertEqual(default_info['nickname'], '微信用户')
        self.assertEqual(default_info['avatar_url'], '')
        self.assertEqual(default_info['gender'], 0)


class WxApiClientTestCase(TestCase):
    """微信API客户端测试用例"""
    
    @patch('apps.wx.utils.requests.get')
    def test_code2session_success(self, mock_get):
        """测试code2session成功调用"""
        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'openid': 'test_openid',
            'session_key': 'test_session_key'
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        client = WxApiClient()
        result = client.code2session('test_code')
        
        self.assertEqual(result['openid'], 'test_openid')
        self.assertEqual(result['session_key'], 'test_session_key')
    
    @patch('apps.wx.utils.requests.get')
    def test_code2session_error(self, mock_get):
        """测试code2session错误响应"""
        # 模拟错误响应
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'errcode': 40029,
            'errmsg': 'invalid code'
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        client = WxApiClient()
        
        with self.assertRaises(Exception):
            client.code2session('invalid_code')
