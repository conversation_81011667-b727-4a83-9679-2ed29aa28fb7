import {
  he
} from "./chunk-JWPUVG35.js";
import "./chunk-7AOKHL6I.js";
import {
  ot
} from "./chunk-TJTCSOX4.js";
import "./chunk-3XL3ODE2.js";
import "./chunk-YFT6OQ5R.js";
import {
  B
} from "./chunk-TGOZU523.js";
import "./chunk-IUY2MIZJ.js";
import "./chunk-LK7GAOJV.js";
import "./chunk-44FYVRJW.js";
import {
  Fragment,
  computed,
  createBaseVNode,
  createBlock,
  createElementBlock,
  createVNode,
  defineComponent,
  h,
  mergeProps,
  normalizeClass,
  openBlock,
  ref,
  renderList,
  resolveComponent,
  resolveDynamicComponent,
  toDisplayString,
  vShow,
  withCtx,
  withDirectives
} from "./chunk-WEJJSMSC.js";
import "./chunk-LK32TJAX.js";

// node_modules/@fast-crud/fast-extends/dist/fs-cropper-c1cf1841.mjs
function ae(a, t) {
  var i = Object.keys(a);
  if (Object.getOwnPropertySymbols) {
    var e = Object.getOwnPropertySymbols(a);
    t && (e = e.filter(function(n) {
      return Object.getOwnPropertyDescriptor(a, n).enumerable;
    })), i.push.apply(i, e);
  }
  return i;
}
function we(a) {
  for (var t = 1; t < arguments.length; t++) {
    var i = arguments[t] != null ? arguments[t] : {};
    t % 2 ? ae(Object(i), true).forEach(function(e) {
      Ke(a, e, i[e]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(a, Object.getOwnPropertyDescriptors(i)) : ae(Object(i)).forEach(function(e) {
      Object.defineProperty(a, e, Object.getOwnPropertyDescriptor(i, e));
    });
  }
  return a;
}
function Qe(a, t) {
  if (typeof a != "object" || !a)
    return a;
  var i = a[Symbol.toPrimitive];
  if (i !== void 0) {
    var e = i.call(a, t || "default");
    if (typeof e != "object")
      return e;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (t === "string" ? String : Number)(a);
}
function ye(a) {
  var t = Qe(a, "string");
  return typeof t == "symbol" ? t : t + "";
}
function Pt(a) {
  "@babel/helpers - typeof";
  return Pt = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function(t) {
    return typeof t;
  } : function(t) {
    return t && typeof Symbol == "function" && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t;
  }, Pt(a);
}
function Ze(a, t) {
  if (!(a instanceof t))
    throw new TypeError("Cannot call a class as a function");
}
function re(a, t) {
  for (var i = 0; i < t.length; i++) {
    var e = t[i];
    e.enumerable = e.enumerable || false, e.configurable = true, "value" in e && (e.writable = true), Object.defineProperty(a, ye(e.key), e);
  }
}
function qe(a, t, i) {
  return t && re(a.prototype, t), i && re(a, i), Object.defineProperty(a, "prototype", {
    writable: false
  }), a;
}
function Ke(a, t, i) {
  return t = ye(t), t in a ? Object.defineProperty(a, t, {
    value: i,
    enumerable: true,
    configurable: true,
    writable: true
  }) : a[t] = i, a;
}
function xe(a) {
  return Je(a) || ti(a) || ei(a) || ii();
}
function Je(a) {
  if (Array.isArray(a))
    return Ht(a);
}
function ti(a) {
  if (typeof Symbol < "u" && a[Symbol.iterator] != null || a["@@iterator"] != null)
    return Array.from(a);
}
function ei(a, t) {
  if (a) {
    if (typeof a == "string")
      return Ht(a, t);
    var i = Object.prototype.toString.call(a).slice(8, -1);
    if (i === "Object" && a.constructor && (i = a.constructor.name), i === "Map" || i === "Set")
      return Array.from(a);
    if (i === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))
      return Ht(a, t);
  }
}
function Ht(a, t) {
  (t == null || t > a.length) && (t = a.length);
  for (var i = 0, e = new Array(t); i < t; i++)
    e[i] = a[i];
  return e;
}
function ii() {
  throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);
}
var Nt = typeof window < "u" && typeof window.document < "u";
var V = Nt ? window : {};
var Gt = Nt && V.document.documentElement ? "ontouchstart" in V.document.documentElement : false;
var Qt = Nt ? "PointerEvent" in V : false;
var C = "cropper";
var Zt = "all";
var De = "crop";
var Ce = "move";
var Me = "zoom";
var J = "e";
var tt = "w";
var rt = "s";
var Q = "n";
var ut = "ne";
var ft = "nw";
var dt = "se";
var gt = "sw";
var Yt = "".concat(C, "-crop");
var oe = "".concat(C, "-disabled");
var I = "".concat(C, "-hidden");
var ne = "".concat(C, "-hide");
var ai = "".concat(C, "-invisible");
var Ot = "".concat(C, "-modal");
var Wt = "".concat(C, "-move");
var vt = "".concat(C, "Action");
var Et = "".concat(C, "Preview");
var qt = "crop";
var Ee = "move";
var Te = "none";
var Xt = "crop";
var Ut = "cropend";
var jt = "cropmove";
var Vt = "cropstart";
var se = "dblclick";
var ri = Gt ? "touchstart" : "mousedown";
var oi = Gt ? "touchmove" : "mousemove";
var ni = Gt ? "touchend touchcancel" : "mouseup";
var he2 = Qt ? "pointerdown" : ri;
var ce = Qt ? "pointermove" : oi;
var le = Qt ? "pointerup pointercancel" : ni;
var pe = "ready";
var ue = "resize";
var fe = "wheel";
var $t = "zoom";
var de = "image/jpeg";
var si = /^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/;
var hi = /^data:/;
var ci = /^data:image\/jpeg;base64,/;
var li = /^img|canvas$/i;
var Oe = 200;
var Ne = 100;
var ge = {
  // Define the view mode of the cropper
  viewMode: 0,
  // 0, 1, 2, 3
  // Define the dragging mode of the cropper
  dragMode: qt,
  // 'crop', 'move' or 'none'
  // Define the initial aspect ratio of the crop box
  initialAspectRatio: NaN,
  // Define the aspect ratio of the crop box
  aspectRatio: NaN,
  // An object with the previous cropping result data
  data: null,
  // A selector for adding extra containers to preview
  preview: "",
  // Re-render the cropper when resize the window
  responsive: true,
  // Restore the cropped area after resize the window
  restore: true,
  // Check if the current image is a cross-origin image
  checkCrossOrigin: true,
  // Check the current image's Exif Orientation information
  checkOrientation: true,
  // Show the black modal
  modal: true,
  // Show the dashed lines for guiding
  guides: true,
  // Show the center indicator for guiding
  center: true,
  // Show the white modal to highlight the crop box
  highlight: true,
  // Show the grid background
  background: true,
  // Enable to crop the image automatically when initialize
  autoCrop: true,
  // Define the percentage of automatic cropping area when initializes
  autoCropArea: 0.8,
  // Enable to move the image
  movable: true,
  // Enable to rotate the image
  rotatable: true,
  // Enable to scale the image
  scalable: true,
  // Enable to zoom the image
  zoomable: true,
  // Enable to zoom the image by dragging touch
  zoomOnTouch: true,
  // Enable to zoom the image by wheeling mouse
  zoomOnWheel: true,
  // Define zoom ratio when zoom the image by wheeling mouse
  wheelZoomRatio: 0.1,
  // Enable to move the crop box
  cropBoxMovable: true,
  // Enable to resize the crop box
  cropBoxResizable: true,
  // Toggle drag mode between "crop" and "move" when click twice on the cropper
  toggleDragModeOnDblclick: true,
  // Size limitation
  minCanvasWidth: 0,
  minCanvasHeight: 0,
  minCropBoxWidth: 0,
  minCropBoxHeight: 0,
  minContainerWidth: Oe,
  minContainerHeight: Ne,
  // Shortcuts of events
  ready: null,
  cropstart: null,
  cropmove: null,
  cropend: null,
  crop: null,
  zoom: null
};
var pi = '<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>';
var ui = Number.isNaN || V.isNaN;
function v(a) {
  return typeof a == "number" && !ui(a);
}
var me = function(t) {
  return t > 0 && t < 1 / 0;
};
function zt(a) {
  return typeof a > "u";
}
function et(a) {
  return Pt(a) === "object" && a !== null;
}
var fi = Object.prototype.hasOwnProperty;
function ot2(a) {
  if (!et(a))
    return false;
  try {
    var t = a.constructor, i = t.prototype;
    return t && i && fi.call(i, "isPrototypeOf");
  } catch {
    return false;
  }
}
function k(a) {
  return typeof a == "function";
}
var di = Array.prototype.slice;
function Se(a) {
  return Array.from ? Array.from(a) : di.call(a);
}
function N(a, t) {
  return a && k(t) && (Array.isArray(a) || v(a.length) ? Se(a).forEach(function(i, e) {
    t.call(a, i, e, a);
  }) : et(a) && Object.keys(a).forEach(function(i) {
    t.call(a, a[i], i, a);
  })), a;
}
var M = Object.assign || function(t) {
  for (var i = arguments.length, e = new Array(i > 1 ? i - 1 : 0), n = 1; n < i; n++)
    e[n - 1] = arguments[n];
  return et(t) && e.length > 0 && e.forEach(function(r) {
    et(r) && Object.keys(r).forEach(function(o) {
      t[o] = r[o];
    });
  }), t;
};
var gi = /\.\d*(?:0|9){12}\d*$/;
function st(a) {
  var t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1e11;
  return gi.test(a) ? Math.round(a * t) / t : a;
}
var mi = /^width|height|left|top|marginLeft|marginTop$/;
function Z(a, t) {
  var i = a.style;
  N(t, function(e, n) {
    mi.test(n) && v(e) && (e = "".concat(e, "px")), i[n] = e;
  });
}
function vi(a, t) {
  return a.classList ? a.classList.contains(t) : a.className.indexOf(t) > -1;
}
function S(a, t) {
  if (t) {
    if (v(a.length)) {
      N(a, function(e) {
        S(e, t);
      });
      return;
    }
    if (a.classList) {
      a.classList.add(t);
      return;
    }
    var i = a.className.trim();
    i ? i.indexOf(t) < 0 && (a.className = "".concat(i, " ").concat(t)) : a.className = t;
  }
}
function j(a, t) {
  if (t) {
    if (v(a.length)) {
      N(a, function(i) {
        j(i, t);
      });
      return;
    }
    if (a.classList) {
      a.classList.remove(t);
      return;
    }
    a.className.indexOf(t) >= 0 && (a.className = a.className.replace(t, ""));
  }
}
function nt(a, t, i) {
  if (t) {
    if (v(a.length)) {
      N(a, function(e) {
        nt(e, t, i);
      });
      return;
    }
    i ? S(a, t) : j(a, t);
  }
}
var bi = /([a-z\d])([A-Z])/g;
function Kt(a) {
  return a.replace(bi, "$1-$2").toLowerCase();
}
function Ft(a, t) {
  return et(a[t]) ? a[t] : a.dataset ? a.dataset[t] : a.getAttribute("data-".concat(Kt(t)));
}
function bt(a, t, i) {
  et(i) ? a[t] = i : a.dataset ? a.dataset[t] = i : a.setAttribute("data-".concat(Kt(t)), i);
}
function wi(a, t) {
  if (et(a[t]))
    try {
      delete a[t];
    } catch {
      a[t] = void 0;
    }
  else if (a.dataset)
    try {
      delete a.dataset[t];
    } catch {
      a.dataset[t] = void 0;
    }
  else
    a.removeAttribute("data-".concat(Kt(t)));
}
var Re = /\s\s*/;
var Be = function() {
  var a = false;
  if (Nt) {
    var t = false, i = function() {
    }, e = Object.defineProperty({}, "once", {
      get: function() {
        return a = true, t;
      },
      /**
       * This setter can fix a `TypeError` in strict mode
       * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Errors/Getter_only}
       * @param {boolean} value - The value to set
       */
      set: function(r) {
        t = r;
      }
    });
    V.addEventListener("test", i, e), V.removeEventListener("test", i, e);
  }
  return a;
}();
function X(a, t, i) {
  var e = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {}, n = i;
  t.trim().split(Re).forEach(function(r) {
    if (!Be) {
      var o = a.listeners;
      o && o[r] && o[r][i] && (n = o[r][i], delete o[r][i], Object.keys(o[r]).length === 0 && delete o[r], Object.keys(o).length === 0 && delete a.listeners);
    }
    a.removeEventListener(r, n, e);
  });
}
function Y(a, t, i) {
  var e = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {}, n = i;
  t.trim().split(Re).forEach(function(r) {
    if (e.once && !Be) {
      var o = a.listeners, s = o === void 0 ? {} : o;
      n = function() {
        delete s[r][i], a.removeEventListener(r, n, e);
        for (var l = arguments.length, h2 = new Array(l), c = 0; c < l; c++)
          h2[c] = arguments[c];
        i.apply(a, h2);
      }, s[r] || (s[r] = {}), s[r][i] && a.removeEventListener(r, s[r][i], e), s[r][i] = n, a.listeners = s;
    }
    a.addEventListener(r, n, e);
  });
}
function ht(a, t, i) {
  var e;
  return k(Event) && k(CustomEvent) ? e = new CustomEvent(t, {
    detail: i,
    bubbles: true,
    cancelable: true
  }) : (e = document.createEvent("CustomEvent"), e.initCustomEvent(t, true, true, i)), a.dispatchEvent(e);
}
function Ae(a) {
  var t = a.getBoundingClientRect();
  return {
    left: t.left + (window.pageXOffset - document.documentElement.clientLeft),
    top: t.top + (window.pageYOffset - document.documentElement.clientTop)
  };
}
var Lt = V.location;
var yi = /^(\w+:)\/\/([^:/?#]*):?(\d*)/i;
function ve(a) {
  var t = a.match(yi);
  return t !== null && (t[1] !== Lt.protocol || t[2] !== Lt.hostname || t[3] !== Lt.port);
}
function be(a) {
  var t = "timestamp=".concat((/* @__PURE__ */ new Date()).getTime());
  return a + (a.indexOf("?") === -1 ? "?" : "&") + t;
}
function mt(a) {
  var t = a.rotate, i = a.scaleX, e = a.scaleY, n = a.translateX, r = a.translateY, o = [];
  v(n) && n !== 0 && o.push("translateX(".concat(n, "px)")), v(r) && r !== 0 && o.push("translateY(".concat(r, "px)")), v(t) && t !== 0 && o.push("rotate(".concat(t, "deg)")), v(i) && i !== 1 && o.push("scaleX(".concat(i, ")")), v(e) && e !== 1 && o.push("scaleY(".concat(e, ")"));
  var s = o.length ? o.join(" ") : "none";
  return {
    WebkitTransform: s,
    msTransform: s,
    transform: s
  };
}
function xi(a) {
  var t = we({}, a), i = 0;
  return N(a, function(e, n) {
    delete t[n], N(t, function(r) {
      var o = Math.abs(e.startX - r.startX), s = Math.abs(e.startY - r.startY), p = Math.abs(e.endX - r.endX), l = Math.abs(e.endY - r.endY), h2 = Math.sqrt(o * o + s * s), c = Math.sqrt(p * p + l * l), u = (c - h2) / h2;
      Math.abs(u) > Math.abs(i) && (i = u);
    });
  }), i;
}
function Tt(a, t) {
  var i = a.pageX, e = a.pageY, n = {
    endX: i,
    endY: e
  };
  return t ? n : we({
    startX: i,
    startY: e
  }, n);
}
function Di(a) {
  var t = 0, i = 0, e = 0;
  return N(a, function(n) {
    var r = n.startX, o = n.startY;
    t += r, i += o, e += 1;
  }), t /= e, i /= e, {
    pageX: t,
    pageY: i
  };
}
function q(a) {
  var t = a.aspectRatio, i = a.height, e = a.width, n = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "contain", r = me(e), o = me(i);
  if (r && o) {
    var s = i * t;
    n === "contain" && s > e || n === "cover" && s < e ? i = e / t : e = i * t;
  } else
    r ? i = e / t : o && (e = i * t);
  return {
    width: e,
    height: i
  };
}
function Ci(a) {
  var t = a.width, i = a.height, e = a.degree;
  if (e = Math.abs(e) % 180, e === 90)
    return {
      width: i,
      height: t
    };
  var n = e % 90 * Math.PI / 180, r = Math.sin(n), o = Math.cos(n), s = t * o + i * r, p = t * r + i * o;
  return e > 90 ? {
    width: p,
    height: s
  } : {
    width: s,
    height: p
  };
}
function Mi(a, t, i, e) {
  var n = t.aspectRatio, r = t.naturalWidth, o = t.naturalHeight, s = t.rotate, p = s === void 0 ? 0 : s, l = t.scaleX, h2 = l === void 0 ? 1 : l, c = t.scaleY, u = c === void 0 ? 1 : c, g = i.aspectRatio, m = i.naturalWidth, y = i.naturalHeight, b = e.fillColor, E = b === void 0 ? "transparent" : b, O = e.imageSmoothingEnabled, x = O === void 0 ? true : O, z = e.imageSmoothingQuality, B2 = z === void 0 ? "low" : z, f = e.maxWidth, w = f === void 0 ? 1 / 0 : f, T = e.maxHeight, A = T === void 0 ? 1 / 0 : T, L = e.minWidth, $ = L === void 0 ? 0 : L, W = e.minHeight, P = W === void 0 ? 0 : W, _ = document.createElement("canvas"), R = _.getContext("2d"), F = q({
    aspectRatio: g,
    width: w,
    height: A
  }), K = q({
    aspectRatio: g,
    width: $,
    height: P
  }, "cover"), it = Math.min(F.width, Math.max(K.width, m)), ct = Math.min(F.height, Math.max(K.height, y)), wt = q({
    aspectRatio: n,
    width: w,
    height: A
  }), yt = q({
    aspectRatio: n,
    width: $,
    height: P
  }, "cover"), lt = Math.min(wt.width, Math.max(yt.width, r)), at = Math.min(wt.height, Math.max(yt.height, o)), St = [-lt / 2, -at / 2, lt, at];
  return _.width = st(it), _.height = st(ct), R.fillStyle = E, R.fillRect(0, 0, it, ct), R.save(), R.translate(it / 2, ct / 2), R.rotate(p * Math.PI / 180), R.scale(h2, u), R.imageSmoothingEnabled = x, R.imageSmoothingQuality = B2, R.drawImage.apply(R, [a].concat(xe(St.map(function(Rt) {
    return Math.floor(st(Rt));
  })))), R.restore(), _;
}
var _e = String.fromCharCode;
function Ei(a, t, i) {
  var e = "";
  i += t;
  for (var n = t; n < i; n += 1)
    e += _e(a.getUint8(n));
  return e;
}
var Ti = /^data:.*,/;
function Oi(a) {
  var t = a.replace(Ti, ""), i = atob(t), e = new ArrayBuffer(i.length), n = new Uint8Array(e);
  return N(n, function(r, o) {
    n[o] = i.charCodeAt(o);
  }), e;
}
function Ni(a, t) {
  for (var i = [], e = 8192, n = new Uint8Array(a); n.length > 0; )
    i.push(_e.apply(null, Se(n.subarray(0, e)))), n = n.subarray(e);
  return "data:".concat(t, ";base64,").concat(btoa(i.join("")));
}
function Si(a) {
  var t = new DataView(a), i;
  try {
    var e, n, r;
    if (t.getUint8(0) === 255 && t.getUint8(1) === 216)
      for (var o = t.byteLength, s = 2; s + 1 < o; ) {
        if (t.getUint8(s) === 255 && t.getUint8(s + 1) === 225) {
          n = s;
          break;
        }
        s += 1;
      }
    if (n) {
      var p = n + 4, l = n + 10;
      if (Ei(t, p, 4) === "Exif") {
        var h2 = t.getUint16(l);
        if (e = h2 === 18761, (e || h2 === 19789) && t.getUint16(l + 2, e) === 42) {
          var c = t.getUint32(l + 4, e);
          c >= 8 && (r = l + c);
        }
      }
    }
    if (r) {
      var u = t.getUint16(r, e), g, m;
      for (m = 0; m < u; m += 1)
        if (g = r + m * 12 + 2, t.getUint16(g, e) === 274) {
          g += 8, i = t.getUint16(g, e), t.setUint16(g, 1, e);
          break;
        }
    }
  } catch {
    i = 1;
  }
  return i;
}
function Ri(a) {
  var t = 0, i = 1, e = 1;
  switch (a) {
    case 2:
      i = -1;
      break;
    case 3:
      t = -180;
      break;
    case 4:
      e = -1;
      break;
    case 5:
      t = 90, e = -1;
      break;
    case 6:
      t = 90;
      break;
    case 7:
      t = 90, i = -1;
      break;
    case 8:
      t = -90;
      break;
  }
  return {
    rotate: t,
    scaleX: i,
    scaleY: e
  };
}
var Bi = {
  render: function() {
    this.initContainer(), this.initCanvas(), this.initCropBox(), this.renderCanvas(), this.cropped && this.renderCropBox();
  },
  initContainer: function() {
    var t = this.element, i = this.options, e = this.container, n = this.cropper, r = Number(i.minContainerWidth), o = Number(i.minContainerHeight);
    S(n, I), j(t, I);
    var s = {
      width: Math.max(e.offsetWidth, r >= 0 ? r : Oe),
      height: Math.max(e.offsetHeight, o >= 0 ? o : Ne)
    };
    this.containerData = s, Z(n, {
      width: s.width,
      height: s.height
    }), S(t, I), j(n, I);
  },
  // Canvas (image wrapper)
  initCanvas: function() {
    var t = this.containerData, i = this.imageData, e = this.options.viewMode, n = Math.abs(i.rotate) % 180 === 90, r = n ? i.naturalHeight : i.naturalWidth, o = n ? i.naturalWidth : i.naturalHeight, s = r / o, p = t.width, l = t.height;
    t.height * s > t.width ? e === 3 ? p = t.height * s : l = t.width / s : e === 3 ? l = t.width / s : p = t.height * s;
    var h2 = {
      aspectRatio: s,
      naturalWidth: r,
      naturalHeight: o,
      width: p,
      height: l
    };
    this.canvasData = h2, this.limited = e === 1 || e === 2, this.limitCanvas(true, true), h2.width = Math.min(Math.max(h2.width, h2.minWidth), h2.maxWidth), h2.height = Math.min(Math.max(h2.height, h2.minHeight), h2.maxHeight), h2.left = (t.width - h2.width) / 2, h2.top = (t.height - h2.height) / 2, h2.oldLeft = h2.left, h2.oldTop = h2.top, this.initialCanvasData = M({}, h2);
  },
  limitCanvas: function(t, i) {
    var e = this.options, n = this.containerData, r = this.canvasData, o = this.cropBoxData, s = e.viewMode, p = r.aspectRatio, l = this.cropped && o;
    if (t) {
      var h2 = Number(e.minCanvasWidth) || 0, c = Number(e.minCanvasHeight) || 0;
      s > 1 ? (h2 = Math.max(h2, n.width), c = Math.max(c, n.height), s === 3 && (c * p > h2 ? h2 = c * p : c = h2 / p)) : s > 0 && (h2 ? h2 = Math.max(h2, l ? o.width : 0) : c ? c = Math.max(c, l ? o.height : 0) : l && (h2 = o.width, c = o.height, c * p > h2 ? h2 = c * p : c = h2 / p));
      var u = q({
        aspectRatio: p,
        width: h2,
        height: c
      });
      h2 = u.width, c = u.height, r.minWidth = h2, r.minHeight = c, r.maxWidth = 1 / 0, r.maxHeight = 1 / 0;
    }
    if (i)
      if (s > (l ? 0 : 1)) {
        var g = n.width - r.width, m = n.height - r.height;
        r.minLeft = Math.min(0, g), r.minTop = Math.min(0, m), r.maxLeft = Math.max(0, g), r.maxTop = Math.max(0, m), l && this.limited && (r.minLeft = Math.min(o.left, o.left + (o.width - r.width)), r.minTop = Math.min(o.top, o.top + (o.height - r.height)), r.maxLeft = o.left, r.maxTop = o.top, s === 2 && (r.width >= n.width && (r.minLeft = Math.min(0, g), r.maxLeft = Math.max(0, g)), r.height >= n.height && (r.minTop = Math.min(0, m), r.maxTop = Math.max(0, m))));
      } else
        r.minLeft = -r.width, r.minTop = -r.height, r.maxLeft = n.width, r.maxTop = n.height;
  },
  renderCanvas: function(t, i) {
    var e = this.canvasData, n = this.imageData;
    if (i) {
      var r = Ci({
        width: n.naturalWidth * Math.abs(n.scaleX || 1),
        height: n.naturalHeight * Math.abs(n.scaleY || 1),
        degree: n.rotate || 0
      }), o = r.width, s = r.height, p = e.width * (o / e.naturalWidth), l = e.height * (s / e.naturalHeight);
      e.left -= (p - e.width) / 2, e.top -= (l - e.height) / 2, e.width = p, e.height = l, e.aspectRatio = o / s, e.naturalWidth = o, e.naturalHeight = s, this.limitCanvas(true, false);
    }
    (e.width > e.maxWidth || e.width < e.minWidth) && (e.left = e.oldLeft), (e.height > e.maxHeight || e.height < e.minHeight) && (e.top = e.oldTop), e.width = Math.min(Math.max(e.width, e.minWidth), e.maxWidth), e.height = Math.min(Math.max(e.height, e.minHeight), e.maxHeight), this.limitCanvas(false, true), e.left = Math.min(Math.max(e.left, e.minLeft), e.maxLeft), e.top = Math.min(Math.max(e.top, e.minTop), e.maxTop), e.oldLeft = e.left, e.oldTop = e.top, Z(this.canvas, M({
      width: e.width,
      height: e.height
    }, mt({
      translateX: e.left,
      translateY: e.top
    }))), this.renderImage(t), this.cropped && this.limited && this.limitCropBox(true, true);
  },
  renderImage: function(t) {
    var i = this.canvasData, e = this.imageData, n = e.naturalWidth * (i.width / i.naturalWidth), r = e.naturalHeight * (i.height / i.naturalHeight);
    M(e, {
      width: n,
      height: r,
      left: (i.width - n) / 2,
      top: (i.height - r) / 2
    }), Z(this.image, M({
      width: e.width,
      height: e.height
    }, mt(M({
      translateX: e.left,
      translateY: e.top
    }, e)))), t && this.output();
  },
  initCropBox: function() {
    var t = this.options, i = this.canvasData, e = t.aspectRatio || t.initialAspectRatio, n = Number(t.autoCropArea) || 0.8, r = {
      width: i.width,
      height: i.height
    };
    e && (i.height * e > i.width ? r.height = r.width / e : r.width = r.height * e), this.cropBoxData = r, this.limitCropBox(true, true), r.width = Math.min(Math.max(r.width, r.minWidth), r.maxWidth), r.height = Math.min(Math.max(r.height, r.minHeight), r.maxHeight), r.width = Math.max(r.minWidth, r.width * n), r.height = Math.max(r.minHeight, r.height * n), r.left = i.left + (i.width - r.width) / 2, r.top = i.top + (i.height - r.height) / 2, r.oldLeft = r.left, r.oldTop = r.top, this.initialCropBoxData = M({}, r);
  },
  limitCropBox: function(t, i) {
    var e = this.options, n = this.containerData, r = this.canvasData, o = this.cropBoxData, s = this.limited, p = e.aspectRatio;
    if (t) {
      var l = Number(e.minCropBoxWidth) || 0, h2 = Number(e.minCropBoxHeight) || 0, c = s ? Math.min(n.width, r.width, r.width + r.left, n.width - r.left) : n.width, u = s ? Math.min(n.height, r.height, r.height + r.top, n.height - r.top) : n.height;
      l = Math.min(l, n.width), h2 = Math.min(h2, n.height), p && (l && h2 ? h2 * p > l ? h2 = l / p : l = h2 * p : l ? h2 = l / p : h2 && (l = h2 * p), u * p > c ? u = c / p : c = u * p), o.minWidth = Math.min(l, c), o.minHeight = Math.min(h2, u), o.maxWidth = c, o.maxHeight = u;
    }
    i && (s ? (o.minLeft = Math.max(0, r.left), o.minTop = Math.max(0, r.top), o.maxLeft = Math.min(n.width, r.left + r.width) - o.width, o.maxTop = Math.min(n.height, r.top + r.height) - o.height) : (o.minLeft = 0, o.minTop = 0, o.maxLeft = n.width - o.width, o.maxTop = n.height - o.height));
  },
  renderCropBox: function() {
    var t = this.options, i = this.containerData, e = this.cropBoxData;
    (e.width > e.maxWidth || e.width < e.minWidth) && (e.left = e.oldLeft), (e.height > e.maxHeight || e.height < e.minHeight) && (e.top = e.oldTop), e.width = Math.min(Math.max(e.width, e.minWidth), e.maxWidth), e.height = Math.min(Math.max(e.height, e.minHeight), e.maxHeight), this.limitCropBox(false, true), e.left = Math.min(Math.max(e.left, e.minLeft), e.maxLeft), e.top = Math.min(Math.max(e.top, e.minTop), e.maxTop), e.oldLeft = e.left, e.oldTop = e.top, t.movable && t.cropBoxMovable && bt(this.face, vt, e.width >= i.width && e.height >= i.height ? Ce : Zt), Z(this.cropBox, M({
      width: e.width,
      height: e.height
    }, mt({
      translateX: e.left,
      translateY: e.top
    }))), this.cropped && this.limited && this.limitCanvas(true, true), this.disabled || this.output();
  },
  output: function() {
    this.preview(), ht(this.element, Xt, this.getData());
  }
};
var Ai = {
  initPreview: function() {
    var t = this.element, i = this.crossOrigin, e = this.options.preview, n = i ? this.crossOriginUrl : this.url, r = t.alt || "The image to preview", o = document.createElement("img");
    if (i && (o.crossOrigin = i), o.src = n, o.alt = r, this.viewBox.appendChild(o), this.viewBoxImage = o, !!e) {
      var s = e;
      typeof e == "string" ? s = t.ownerDocument.querySelectorAll(e) : e.querySelector && (s = [e]), this.previews = s, N(s, function(p) {
        var l = document.createElement("img");
        bt(p, Et, {
          width: p.offsetWidth,
          height: p.offsetHeight,
          html: p.innerHTML
        }), i && (l.crossOrigin = i), l.src = n, l.alt = r, l.style.cssText = 'display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"', p.innerHTML = "", p.appendChild(l);
      });
    }
  },
  resetPreview: function() {
    N(this.previews, function(t) {
      var i = Ft(t, Et);
      Z(t, {
        width: i.width,
        height: i.height
      }), t.innerHTML = i.html, wi(t, Et);
    });
  },
  preview: function() {
    var t = this.imageData, i = this.canvasData, e = this.cropBoxData, n = e.width, r = e.height, o = t.width, s = t.height, p = e.left - i.left - t.left, l = e.top - i.top - t.top;
    !this.cropped || this.disabled || (Z(this.viewBoxImage, M({
      width: o,
      height: s
    }, mt(M({
      translateX: -p,
      translateY: -l
    }, t)))), N(this.previews, function(h2) {
      var c = Ft(h2, Et), u = c.width, g = c.height, m = u, y = g, b = 1;
      n && (b = u / n, y = r * b), r && y > g && (b = g / r, m = n * b, y = g), Z(h2, {
        width: m,
        height: y
      }), Z(h2.getElementsByTagName("img")[0], M({
        width: o * b,
        height: s * b
      }, mt(M({
        translateX: -p * b,
        translateY: -l * b
      }, t))));
    }));
  }
};
var _i = {
  bind: function() {
    var t = this.element, i = this.options, e = this.cropper;
    k(i.cropstart) && Y(t, Vt, i.cropstart), k(i.cropmove) && Y(t, jt, i.cropmove), k(i.cropend) && Y(t, Ut, i.cropend), k(i.crop) && Y(t, Xt, i.crop), k(i.zoom) && Y(t, $t, i.zoom), Y(e, he2, this.onCropStart = this.cropStart.bind(this)), i.zoomable && i.zoomOnWheel && Y(e, fe, this.onWheel = this.wheel.bind(this), {
      passive: false,
      capture: true
    }), i.toggleDragModeOnDblclick && Y(e, se, this.onDblclick = this.dblclick.bind(this)), Y(t.ownerDocument, ce, this.onCropMove = this.cropMove.bind(this)), Y(t.ownerDocument, le, this.onCropEnd = this.cropEnd.bind(this)), i.responsive && Y(window, ue, this.onResize = this.resize.bind(this));
  },
  unbind: function() {
    var t = this.element, i = this.options, e = this.cropper;
    k(i.cropstart) && X(t, Vt, i.cropstart), k(i.cropmove) && X(t, jt, i.cropmove), k(i.cropend) && X(t, Ut, i.cropend), k(i.crop) && X(t, Xt, i.crop), k(i.zoom) && X(t, $t, i.zoom), X(e, he2, this.onCropStart), i.zoomable && i.zoomOnWheel && X(e, fe, this.onWheel, {
      passive: false,
      capture: true
    }), i.toggleDragModeOnDblclick && X(e, se, this.onDblclick), X(t.ownerDocument, ce, this.onCropMove), X(t.ownerDocument, le, this.onCropEnd), i.responsive && X(window, ue, this.onResize);
  }
};
var ki = {
  resize: function() {
    if (!this.disabled) {
      var t = this.options, i = this.container, e = this.containerData, n = i.offsetWidth / e.width, r = i.offsetHeight / e.height, o = Math.abs(n - 1) > Math.abs(r - 1) ? n : r;
      if (o !== 1) {
        var s, p;
        t.restore && (s = this.getCanvasData(), p = this.getCropBoxData()), this.render(), t.restore && (this.setCanvasData(N(s, function(l, h2) {
          s[h2] = l * o;
        })), this.setCropBoxData(N(p, function(l, h2) {
          p[h2] = l * o;
        })));
      }
    }
  },
  dblclick: function() {
    this.disabled || this.options.dragMode === Te || this.setDragMode(vi(this.dragBox, Yt) ? Ee : qt);
  },
  wheel: function(t) {
    var i = this, e = Number(this.options.wheelZoomRatio) || 0.1, n = 1;
    this.disabled || (t.preventDefault(), !this.wheeling && (this.wheeling = true, setTimeout(function() {
      i.wheeling = false;
    }, 50), t.deltaY ? n = t.deltaY > 0 ? 1 : -1 : t.wheelDelta ? n = -t.wheelDelta / 120 : t.detail && (n = t.detail > 0 ? 1 : -1), this.zoom(-n * e, t)));
  },
  cropStart: function(t) {
    var i = t.buttons, e = t.button;
    if (!(this.disabled || (t.type === "mousedown" || t.type === "pointerdown" && t.pointerType === "mouse") && // No primary button (Usually the left button)
    (v(i) && i !== 1 || v(e) && e !== 0 || t.ctrlKey))) {
      var n = this.options, r = this.pointers, o;
      t.changedTouches ? N(t.changedTouches, function(s) {
        r[s.identifier] = Tt(s);
      }) : r[t.pointerId || 0] = Tt(t), Object.keys(r).length > 1 && n.zoomable && n.zoomOnTouch ? o = Me : o = Ft(t.target, vt), si.test(o) && ht(this.element, Vt, {
        originalEvent: t,
        action: o
      }) !== false && (t.preventDefault(), this.action = o, this.cropping = false, o === De && (this.cropping = true, S(this.dragBox, Ot)));
    }
  },
  cropMove: function(t) {
    var i = this.action;
    if (!(this.disabled || !i)) {
      var e = this.pointers;
      t.preventDefault(), ht(this.element, jt, {
        originalEvent: t,
        action: i
      }) !== false && (t.changedTouches ? N(t.changedTouches, function(n) {
        M(e[n.identifier] || {}, Tt(n, true));
      }) : M(e[t.pointerId || 0] || {}, Tt(t, true)), this.change(t));
    }
  },
  cropEnd: function(t) {
    if (!this.disabled) {
      var i = this.action, e = this.pointers;
      t.changedTouches ? N(t.changedTouches, function(n) {
        delete e[n.identifier];
      }) : delete e[t.pointerId || 0], i && (t.preventDefault(), Object.keys(e).length || (this.action = ""), this.cropping && (this.cropping = false, nt(this.dragBox, Ot, this.cropped && this.options.modal)), ht(this.element, Ut, {
        originalEvent: t,
        action: i
      }));
    }
  }
};
var Ii = {
  change: function(t) {
    var i = this.options, e = this.canvasData, n = this.containerData, r = this.cropBoxData, o = this.pointers, s = this.action, p = i.aspectRatio, l = r.left, h2 = r.top, c = r.width, u = r.height, g = l + c, m = h2 + u, y = 0, b = 0, E = n.width, O = n.height, x = true, z;
    !p && t.shiftKey && (p = c && u ? c / u : 1), this.limited && (y = r.minLeft, b = r.minTop, E = y + Math.min(n.width, e.width, e.left + e.width), O = b + Math.min(n.height, e.height, e.top + e.height));
    var B2 = o[Object.keys(o)[0]], f = {
      x: B2.endX - B2.startX,
      y: B2.endY - B2.startY
    }, w = function(A) {
      switch (A) {
        case J:
          g + f.x > E && (f.x = E - g);
          break;
        case tt:
          l + f.x < y && (f.x = y - l);
          break;
        case Q:
          h2 + f.y < b && (f.y = b - h2);
          break;
        case rt:
          m + f.y > O && (f.y = O - m);
          break;
      }
    };
    switch (s) {
      case Zt:
        l += f.x, h2 += f.y;
        break;
      case J:
        if (f.x >= 0 && (g >= E || p && (h2 <= b || m >= O))) {
          x = false;
          break;
        }
        w(J), c += f.x, c < 0 && (s = tt, c = -c, l -= c), p && (u = c / p, h2 += (r.height - u) / 2);
        break;
      case Q:
        if (f.y <= 0 && (h2 <= b || p && (l <= y || g >= E))) {
          x = false;
          break;
        }
        w(Q), u -= f.y, h2 += f.y, u < 0 && (s = rt, u = -u, h2 -= u), p && (c = u * p, l += (r.width - c) / 2);
        break;
      case tt:
        if (f.x <= 0 && (l <= y || p && (h2 <= b || m >= O))) {
          x = false;
          break;
        }
        w(tt), c -= f.x, l += f.x, c < 0 && (s = J, c = -c, l -= c), p && (u = c / p, h2 += (r.height - u) / 2);
        break;
      case rt:
        if (f.y >= 0 && (m >= O || p && (l <= y || g >= E))) {
          x = false;
          break;
        }
        w(rt), u += f.y, u < 0 && (s = Q, u = -u, h2 -= u), p && (c = u * p, l += (r.width - c) / 2);
        break;
      case ut:
        if (p) {
          if (f.y <= 0 && (h2 <= b || g >= E)) {
            x = false;
            break;
          }
          w(Q), u -= f.y, h2 += f.y, c = u * p;
        } else
          w(Q), w(J), f.x >= 0 ? g < E ? c += f.x : f.y <= 0 && h2 <= b && (x = false) : c += f.x, f.y <= 0 ? h2 > b && (u -= f.y, h2 += f.y) : (u -= f.y, h2 += f.y);
        c < 0 && u < 0 ? (s = gt, u = -u, c = -c, h2 -= u, l -= c) : c < 0 ? (s = ft, c = -c, l -= c) : u < 0 && (s = dt, u = -u, h2 -= u);
        break;
      case ft:
        if (p) {
          if (f.y <= 0 && (h2 <= b || l <= y)) {
            x = false;
            break;
          }
          w(Q), u -= f.y, h2 += f.y, c = u * p, l += r.width - c;
        } else
          w(Q), w(tt), f.x <= 0 ? l > y ? (c -= f.x, l += f.x) : f.y <= 0 && h2 <= b && (x = false) : (c -= f.x, l += f.x), f.y <= 0 ? h2 > b && (u -= f.y, h2 += f.y) : (u -= f.y, h2 += f.y);
        c < 0 && u < 0 ? (s = dt, u = -u, c = -c, h2 -= u, l -= c) : c < 0 ? (s = ut, c = -c, l -= c) : u < 0 && (s = gt, u = -u, h2 -= u);
        break;
      case gt:
        if (p) {
          if (f.x <= 0 && (l <= y || m >= O)) {
            x = false;
            break;
          }
          w(tt), c -= f.x, l += f.x, u = c / p;
        } else
          w(rt), w(tt), f.x <= 0 ? l > y ? (c -= f.x, l += f.x) : f.y >= 0 && m >= O && (x = false) : (c -= f.x, l += f.x), f.y >= 0 ? m < O && (u += f.y) : u += f.y;
        c < 0 && u < 0 ? (s = ut, u = -u, c = -c, h2 -= u, l -= c) : c < 0 ? (s = dt, c = -c, l -= c) : u < 0 && (s = ft, u = -u, h2 -= u);
        break;
      case dt:
        if (p) {
          if (f.x >= 0 && (g >= E || m >= O)) {
            x = false;
            break;
          }
          w(J), c += f.x, u = c / p;
        } else
          w(rt), w(J), f.x >= 0 ? g < E ? c += f.x : f.y >= 0 && m >= O && (x = false) : c += f.x, f.y >= 0 ? m < O && (u += f.y) : u += f.y;
        c < 0 && u < 0 ? (s = ft, u = -u, c = -c, h2 -= u, l -= c) : c < 0 ? (s = gt, c = -c, l -= c) : u < 0 && (s = ut, u = -u, h2 -= u);
        break;
      case Ce:
        this.move(f.x, f.y), x = false;
        break;
      case Me:
        this.zoom(xi(o), t), x = false;
        break;
      case De:
        if (!f.x || !f.y) {
          x = false;
          break;
        }
        z = Ae(this.cropper), l = B2.startX - z.left, h2 = B2.startY - z.top, c = r.minWidth, u = r.minHeight, f.x > 0 ? s = f.y > 0 ? dt : ut : f.x < 0 && (l -= c, s = f.y > 0 ? gt : ft), f.y < 0 && (h2 -= u), this.cropped || (j(this.cropBox, I), this.cropped = true, this.limited && this.limitCropBox(true, true));
        break;
    }
    x && (r.width = c, r.height = u, r.left = l, r.top = h2, this.action = s, this.renderCropBox()), N(o, function(T) {
      T.startX = T.endX, T.startY = T.endY;
    });
  }
};
var zi = {
  // Show the crop box manually
  crop: function() {
    return this.ready && !this.cropped && !this.disabled && (this.cropped = true, this.limitCropBox(true, true), this.options.modal && S(this.dragBox, Ot), j(this.cropBox, I), this.setCropBoxData(this.initialCropBoxData)), this;
  },
  // Reset the image and crop box to their initial states
  reset: function() {
    return this.ready && !this.disabled && (this.imageData = M({}, this.initialImageData), this.canvasData = M({}, this.initialCanvasData), this.cropBoxData = M({}, this.initialCropBoxData), this.renderCanvas(), this.cropped && this.renderCropBox()), this;
  },
  // Clear the crop box
  clear: function() {
    return this.cropped && !this.disabled && (M(this.cropBoxData, {
      left: 0,
      top: 0,
      width: 0,
      height: 0
    }), this.cropped = false, this.renderCropBox(), this.limitCanvas(true, true), this.renderCanvas(), j(this.dragBox, Ot), S(this.cropBox, I)), this;
  },
  /**
   * Replace the image's src and rebuild the cropper
   * @param {string} url - The new URL.
   * @param {boolean} [hasSameSize] - Indicate if the new image has the same size as the old one.
   * @returns {Cropper} this
   */
  replace: function(t) {
    var i = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
    return !this.disabled && t && (this.isImg && (this.element.src = t), i ? (this.url = t, this.image.src = t, this.ready && (this.viewBoxImage.src = t, N(this.previews, function(e) {
      e.getElementsByTagName("img")[0].src = t;
    }))) : (this.isImg && (this.replaced = true), this.options.data = null, this.uncreate(), this.load(t))), this;
  },
  // Enable (unfreeze) the cropper
  enable: function() {
    return this.ready && this.disabled && (this.disabled = false, j(this.cropper, oe)), this;
  },
  // Disable (freeze) the cropper
  disable: function() {
    return this.ready && !this.disabled && (this.disabled = true, S(this.cropper, oe)), this;
  },
  /**
   * Destroy the cropper and remove the instance from the image
   * @returns {Cropper} this
   */
  destroy: function() {
    var t = this.element;
    return t[C] ? (t[C] = void 0, this.isImg && this.replaced && (t.src = this.originalUrl), this.uncreate(), this) : this;
  },
  /**
   * Move the canvas with relative offsets
   * @param {number} offsetX - The relative offset distance on the x-axis.
   * @param {number} [offsetY=offsetX] - The relative offset distance on the y-axis.
   * @returns {Cropper} this
   */
  move: function(t) {
    var i = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : t, e = this.canvasData, n = e.left, r = e.top;
    return this.moveTo(zt(t) ? t : n + Number(t), zt(i) ? i : r + Number(i));
  },
  /**
   * Move the canvas to an absolute point
   * @param {number} x - The x-axis coordinate.
   * @param {number} [y=x] - The y-axis coordinate.
   * @returns {Cropper} this
   */
  moveTo: function(t) {
    var i = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : t, e = this.canvasData, n = false;
    return t = Number(t), i = Number(i), this.ready && !this.disabled && this.options.movable && (v(t) && (e.left = t, n = true), v(i) && (e.top = i, n = true), n && this.renderCanvas(true)), this;
  },
  /**
   * Zoom the canvas with a relative ratio
   * @param {number} ratio - The target ratio.
   * @param {Event} _originalEvent - The original event if any.
   * @returns {Cropper} this
   */
  zoom: function(t, i) {
    var e = this.canvasData;
    return t = Number(t), t < 0 ? t = 1 / (1 - t) : t = 1 + t, this.zoomTo(e.width * t / e.naturalWidth, null, i);
  },
  /**
   * Zoom the canvas to an absolute ratio
   * @param {number} ratio - The target ratio.
   * @param {Object} pivot - The zoom pivot point coordinate.
   * @param {Event} _originalEvent - The original event if any.
   * @returns {Cropper} this
   */
  zoomTo: function(t, i, e) {
    var n = this.options, r = this.canvasData, o = r.width, s = r.height, p = r.naturalWidth, l = r.naturalHeight;
    if (t = Number(t), t >= 0 && this.ready && !this.disabled && n.zoomable) {
      var h2 = p * t, c = l * t;
      if (ht(this.element, $t, {
        ratio: t,
        oldRatio: o / p,
        originalEvent: e
      }) === false)
        return this;
      if (e) {
        var u = this.pointers, g = Ae(this.cropper), m = u && Object.keys(u).length ? Di(u) : {
          pageX: e.pageX,
          pageY: e.pageY
        };
        r.left -= (h2 - o) * ((m.pageX - g.left - r.left) / o), r.top -= (c - s) * ((m.pageY - g.top - r.top) / s);
      } else
        ot2(i) && v(i.x) && v(i.y) ? (r.left -= (h2 - o) * ((i.x - r.left) / o), r.top -= (c - s) * ((i.y - r.top) / s)) : (r.left -= (h2 - o) / 2, r.top -= (c - s) / 2);
      r.width = h2, r.height = c, this.renderCanvas(true);
    }
    return this;
  },
  /**
   * Rotate the canvas with a relative degree
   * @param {number} degree - The rotate degree.
   * @returns {Cropper} this
   */
  rotate: function(t) {
    return this.rotateTo((this.imageData.rotate || 0) + Number(t));
  },
  /**
   * Rotate the canvas to an absolute degree
   * @param {number} degree - The rotate degree.
   * @returns {Cropper} this
   */
  rotateTo: function(t) {
    return t = Number(t), v(t) && this.ready && !this.disabled && this.options.rotatable && (this.imageData.rotate = t % 360, this.renderCanvas(true, true)), this;
  },
  /**
   * Scale the image on the x-axis.
   * @param {number} scaleX - The scale ratio on the x-axis.
   * @returns {Cropper} this
   */
  scaleX: function(t) {
    var i = this.imageData.scaleY;
    return this.scale(t, v(i) ? i : 1);
  },
  /**
   * Scale the image on the y-axis.
   * @param {number} scaleY - The scale ratio on the y-axis.
   * @returns {Cropper} this
   */
  scaleY: function(t) {
    var i = this.imageData.scaleX;
    return this.scale(v(i) ? i : 1, t);
  },
  /**
   * Scale the image
   * @param {number} scaleX - The scale ratio on the x-axis.
   * @param {number} [scaleY=scaleX] - The scale ratio on the y-axis.
   * @returns {Cropper} this
   */
  scale: function(t) {
    var i = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : t, e = this.imageData, n = false;
    return t = Number(t), i = Number(i), this.ready && !this.disabled && this.options.scalable && (v(t) && (e.scaleX = t, n = true), v(i) && (e.scaleY = i, n = true), n && this.renderCanvas(true, true)), this;
  },
  /**
   * Get the cropped area position and size data (base on the original image)
   * @param {boolean} [rounded=false] - Indicate if round the data values or not.
   * @returns {Object} The result cropped data.
   */
  getData: function() {
    var t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false, i = this.options, e = this.imageData, n = this.canvasData, r = this.cropBoxData, o;
    if (this.ready && this.cropped) {
      o = {
        x: r.left - n.left,
        y: r.top - n.top,
        width: r.width,
        height: r.height
      };
      var s = e.width / e.naturalWidth;
      if (N(o, function(h2, c) {
        o[c] = h2 / s;
      }), t) {
        var p = Math.round(o.y + o.height), l = Math.round(o.x + o.width);
        o.x = Math.round(o.x), o.y = Math.round(o.y), o.width = l - o.x, o.height = p - o.y;
      }
    } else
      o = {
        x: 0,
        y: 0,
        width: 0,
        height: 0
      };
    return i.rotatable && (o.rotate = e.rotate || 0), i.scalable && (o.scaleX = e.scaleX || 1, o.scaleY = e.scaleY || 1), o;
  },
  /**
   * Set the cropped area position and size with new data
   * @param {Object} data - The new data.
   * @returns {Cropper} this
   */
  setData: function(t) {
    var i = this.options, e = this.imageData, n = this.canvasData, r = {};
    if (this.ready && !this.disabled && ot2(t)) {
      var o = false;
      i.rotatable && v(t.rotate) && t.rotate !== e.rotate && (e.rotate = t.rotate, o = true), i.scalable && (v(t.scaleX) && t.scaleX !== e.scaleX && (e.scaleX = t.scaleX, o = true), v(t.scaleY) && t.scaleY !== e.scaleY && (e.scaleY = t.scaleY, o = true)), o && this.renderCanvas(true, true);
      var s = e.width / e.naturalWidth;
      v(t.x) && (r.left = t.x * s + n.left), v(t.y) && (r.top = t.y * s + n.top), v(t.width) && (r.width = t.width * s), v(t.height) && (r.height = t.height * s), this.setCropBoxData(r);
    }
    return this;
  },
  /**
   * Get the container size data.
   * @returns {Object} The result container data.
   */
  getContainerData: function() {
    return this.ready ? M({}, this.containerData) : {};
  },
  /**
   * Get the image position and size data.
   * @returns {Object} The result image data.
   */
  getImageData: function() {
    return this.sized ? M({}, this.imageData) : {};
  },
  /**
   * Get the canvas position and size data.
   * @returns {Object} The result canvas data.
   */
  getCanvasData: function() {
    var t = this.canvasData, i = {};
    return this.ready && N(["left", "top", "width", "height", "naturalWidth", "naturalHeight"], function(e) {
      i[e] = t[e];
    }), i;
  },
  /**
   * Set the canvas position and size with new data.
   * @param {Object} data - The new canvas data.
   * @returns {Cropper} this
   */
  setCanvasData: function(t) {
    var i = this.canvasData, e = i.aspectRatio;
    return this.ready && !this.disabled && ot2(t) && (v(t.left) && (i.left = t.left), v(t.top) && (i.top = t.top), v(t.width) ? (i.width = t.width, i.height = t.width / e) : v(t.height) && (i.height = t.height, i.width = t.height * e), this.renderCanvas(true)), this;
  },
  /**
   * Get the crop box position and size data.
   * @returns {Object} The result crop box data.
   */
  getCropBoxData: function() {
    var t = this.cropBoxData, i;
    return this.ready && this.cropped && (i = {
      left: t.left,
      top: t.top,
      width: t.width,
      height: t.height
    }), i || {};
  },
  /**
   * Set the crop box position and size with new data.
   * @param {Object} data - The new crop box data.
   * @returns {Cropper} this
   */
  setCropBoxData: function(t) {
    var i = this.cropBoxData, e = this.options.aspectRatio, n, r;
    return this.ready && this.cropped && !this.disabled && ot2(t) && (v(t.left) && (i.left = t.left), v(t.top) && (i.top = t.top), v(t.width) && t.width !== i.width && (n = true, i.width = t.width), v(t.height) && t.height !== i.height && (r = true, i.height = t.height), e && (n ? i.height = i.width / e : r && (i.width = i.height * e)), this.renderCropBox()), this;
  },
  /**
   * Get a canvas drawn the cropped image.
   * @param {Object} [options={}] - The config options.
   * @returns {HTMLCanvasElement} - The result canvas.
   */
  getCroppedCanvas: function() {
    var t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    if (!this.ready || !window.HTMLCanvasElement)
      return null;
    var i = this.canvasData, e = Mi(this.image, this.imageData, i, t);
    if (!this.cropped)
      return e;
    var n = this.getData(t.rounded), r = n.x, o = n.y, s = n.width, p = n.height, l = e.width / Math.floor(i.naturalWidth);
    l !== 1 && (r *= l, o *= l, s *= l, p *= l);
    var h2 = s / p, c = q({
      aspectRatio: h2,
      width: t.maxWidth || 1 / 0,
      height: t.maxHeight || 1 / 0
    }), u = q({
      aspectRatio: h2,
      width: t.minWidth || 0,
      height: t.minHeight || 0
    }, "cover"), g = q({
      aspectRatio: h2,
      width: t.width || (l !== 1 ? e.width : s),
      height: t.height || (l !== 1 ? e.height : p)
    }), m = g.width, y = g.height;
    m = Math.min(c.width, Math.max(u.width, m)), y = Math.min(c.height, Math.max(u.height, y));
    var b = document.createElement("canvas"), E = b.getContext("2d");
    b.width = st(m), b.height = st(y), E.fillStyle = t.fillColor || "transparent", E.fillRect(0, 0, m, y);
    var O = t.imageSmoothingEnabled, x = O === void 0 ? true : O, z = t.imageSmoothingQuality;
    E.imageSmoothingEnabled = x, z && (E.imageSmoothingQuality = z);
    var B2 = e.width, f = e.height, w = r, T = o, A, L, $, W, P, _;
    w <= -s || w > B2 ? (w = 0, A = 0, $ = 0, P = 0) : w <= 0 ? ($ = -w, w = 0, A = Math.min(B2, s + w), P = A) : w <= B2 && ($ = 0, A = Math.min(s, B2 - w), P = A), A <= 0 || T <= -p || T > f ? (T = 0, L = 0, W = 0, _ = 0) : T <= 0 ? (W = -T, T = 0, L = Math.min(f, p + T), _ = L) : T <= f && (W = 0, L = Math.min(p, f - T), _ = L);
    var R = [w, T, A, L];
    if (P > 0 && _ > 0) {
      var F = m / s;
      R.push($ * F, W * F, P * F, _ * F);
    }
    return E.drawImage.apply(E, [e].concat(xe(R.map(function(K) {
      return Math.floor(st(K));
    })))), b;
  },
  /**
   * Change the aspect ratio of the crop box.
   * @param {number} aspectRatio - The new aspect ratio.
   * @returns {Cropper} this
   */
  setAspectRatio: function(t) {
    var i = this.options;
    return !this.disabled && !zt(t) && (i.aspectRatio = Math.max(0, t) || NaN, this.ready && (this.initCropBox(), this.cropped && this.renderCropBox())), this;
  },
  /**
   * Change the drag mode.
   * @param {string} mode - The new drag mode.
   * @returns {Cropper} this
   */
  setDragMode: function(t) {
    var i = this.options, e = this.dragBox, n = this.face;
    if (this.ready && !this.disabled) {
      var r = t === qt, o = i.movable && t === Ee;
      t = r || o ? t : Te, i.dragMode = t, bt(e, vt, t), nt(e, Yt, r), nt(e, Wt, o), i.cropBoxMovable || (bt(n, vt, t), nt(n, Yt, r), nt(n, Wt, o));
    }
    return this;
  }
};
var Li = V.Cropper;
var ke = function() {
  function a(t) {
    var i = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    if (Ze(this, a), !t || !li.test(t.tagName))
      throw new Error("The first argument is required and must be an <img> or <canvas> element.");
    this.element = t, this.options = M({}, ge, ot2(i) && i), this.cropped = false, this.disabled = false, this.pointers = {}, this.ready = false, this.reloading = false, this.replaced = false, this.sized = false, this.sizing = false, this.init();
  }
  return qe(a, [{
    key: "init",
    value: function() {
      var i = this.element, e = i.tagName.toLowerCase(), n;
      if (!i[C]) {
        if (i[C] = this, e === "img") {
          if (this.isImg = true, n = i.getAttribute("src") || "", this.originalUrl = n, !n)
            return;
          n = i.src;
        } else
          e === "canvas" && window.HTMLCanvasElement && (n = i.toDataURL());
        this.load(n);
      }
    }
  }, {
    key: "load",
    value: function(i) {
      var e = this;
      if (i) {
        this.url = i, this.imageData = {};
        var n = this.element, r = this.options;
        if (!r.rotatable && !r.scalable && (r.checkOrientation = false), !r.checkOrientation || !window.ArrayBuffer) {
          this.clone();
          return;
        }
        if (hi.test(i)) {
          ci.test(i) ? this.read(Oi(i)) : this.clone();
          return;
        }
        var o = new XMLHttpRequest(), s = this.clone.bind(this);
        this.reloading = true, this.xhr = o, o.onabort = s, o.onerror = s, o.ontimeout = s, o.onprogress = function() {
          o.getResponseHeader("content-type") !== de && o.abort();
        }, o.onload = function() {
          e.read(o.response);
        }, o.onloadend = function() {
          e.reloading = false, e.xhr = null;
        }, r.checkCrossOrigin && ve(i) && n.crossOrigin && (i = be(i)), o.open("GET", i, true), o.responseType = "arraybuffer", o.withCredentials = n.crossOrigin === "use-credentials", o.send();
      }
    }
  }, {
    key: "read",
    value: function(i) {
      var e = this.options, n = this.imageData, r = Si(i), o = 0, s = 1, p = 1;
      if (r > 1) {
        this.url = Ni(i, de);
        var l = Ri(r);
        o = l.rotate, s = l.scaleX, p = l.scaleY;
      }
      e.rotatable && (n.rotate = o), e.scalable && (n.scaleX = s, n.scaleY = p), this.clone();
    }
  }, {
    key: "clone",
    value: function() {
      var i = this.element, e = this.url, n = i.crossOrigin, r = e;
      this.options.checkCrossOrigin && ve(e) && (n || (n = "anonymous"), r = be(e)), this.crossOrigin = n, this.crossOriginUrl = r;
      var o = document.createElement("img");
      n && (o.crossOrigin = n), o.src = r || e, o.alt = i.alt || "The image to crop", this.image = o, o.onload = this.start.bind(this), o.onerror = this.stop.bind(this), S(o, ne), i.parentNode.insertBefore(o, i.nextSibling);
    }
  }, {
    key: "start",
    value: function() {
      var i = this, e = this.image;
      e.onload = null, e.onerror = null, this.sizing = true;
      var n = V.navigator && /(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(V.navigator.userAgent), r = function(l, h2) {
        M(i.imageData, {
          naturalWidth: l,
          naturalHeight: h2,
          aspectRatio: l / h2
        }), i.initialImageData = M({}, i.imageData), i.sizing = false, i.sized = true, i.build();
      };
      if (e.naturalWidth && !n) {
        r(e.naturalWidth, e.naturalHeight);
        return;
      }
      var o = document.createElement("img"), s = document.body || document.documentElement;
      this.sizingImage = o, o.onload = function() {
        r(o.width, o.height), n || s.removeChild(o);
      }, o.src = e.src, n || (o.style.cssText = "left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;", s.appendChild(o));
    }
  }, {
    key: "stop",
    value: function() {
      var i = this.image;
      i.onload = null, i.onerror = null, i.parentNode.removeChild(i), this.image = null;
    }
  }, {
    key: "build",
    value: function() {
      if (!(!this.sized || this.ready)) {
        var i = this.element, e = this.options, n = this.image, r = i.parentNode, o = document.createElement("div");
        o.innerHTML = pi;
        var s = o.querySelector(".".concat(C, "-container")), p = s.querySelector(".".concat(C, "-canvas")), l = s.querySelector(".".concat(C, "-drag-box")), h2 = s.querySelector(".".concat(C, "-crop-box")), c = h2.querySelector(".".concat(C, "-face"));
        this.container = r, this.cropper = s, this.canvas = p, this.dragBox = l, this.cropBox = h2, this.viewBox = s.querySelector(".".concat(C, "-view-box")), this.face = c, p.appendChild(n), S(i, I), r.insertBefore(s, i.nextSibling), j(n, ne), this.initPreview(), this.bind(), e.initialAspectRatio = Math.max(0, e.initialAspectRatio) || NaN, e.aspectRatio = Math.max(0, e.aspectRatio) || NaN, e.viewMode = Math.max(0, Math.min(3, Math.round(e.viewMode))) || 0, S(h2, I), e.guides || S(h2.getElementsByClassName("".concat(C, "-dashed")), I), e.center || S(h2.getElementsByClassName("".concat(C, "-center")), I), e.background && S(s, "".concat(C, "-bg")), e.highlight || S(c, ai), e.cropBoxMovable && (S(c, Wt), bt(c, vt, Zt)), e.cropBoxResizable || (S(h2.getElementsByClassName("".concat(C, "-line")), I), S(h2.getElementsByClassName("".concat(C, "-point")), I)), this.render(), this.ready = true, this.setDragMode(e.dragMode), e.autoCrop && this.crop(), this.setData(e.data), k(e.ready) && Y(i, pe, e.ready, {
          once: true
        }), ht(i, pe);
      }
    }
  }, {
    key: "unbuild",
    value: function() {
      if (this.ready) {
        this.ready = false, this.unbind(), this.resetPreview();
        var i = this.cropper.parentNode;
        i && i.removeChild(this.cropper), j(this.element, I);
      }
    }
  }, {
    key: "uncreate",
    value: function() {
      this.ready ? (this.unbuild(), this.ready = false, this.cropped = false) : this.sizing ? (this.sizingImage.onload = null, this.sizing = false, this.sized = false) : this.reloading ? (this.xhr.onabort = null, this.xhr.abort()) : this.image && this.stop();
    }
    /**
     * Get the no conflict cropper class.
     * @returns {Cropper} The cropper class.
     */
  }], [{
    key: "noConflict",
    value: function() {
      return window.Cropper = Li, a;
    }
    /**
     * Change the default options.
     * @param {Object} options - The new default options.
     */
  }, {
    key: "setDefaults",
    value: function(i) {
      M(ge, ot2(i) && i);
    }
  }]);
}();
M(ke.prototype, Bi, Ai, _i, ki, Ii, zi);
var Pi = typeof window > "u" ? [String, Array] : [String, Array, Element, NodeList];
var Hi = {
  render() {
    const a = this.crossorigin || void 0;
    return h("div", { style: this.containerStyle }, [
      h("img", {
        ref: "img",
        src: this.src,
        alt: this.alt || "image",
        style: [{ "max-width": "100%" }, this.imgStyle],
        crossorigin: a
      })
    ]);
  },
  props: {
    // Library props
    containerStyle: Object,
    src: {
      type: String,
      default: ""
    },
    alt: String,
    imgStyle: Object,
    // CropperJS props
    viewMode: Number,
    dragMode: String,
    initialAspectRatio: Number,
    aspectRatio: Number,
    data: Object,
    preview: Pi,
    responsive: {
      type: Boolean,
      default: true
    },
    restore: {
      type: Boolean,
      default: true
    },
    checkCrossOrigin: {
      type: Boolean,
      default: true
    },
    checkOrientation: {
      type: Boolean,
      default: true
    },
    crossorigin: {
      type: String
    },
    modal: {
      type: Boolean,
      default: true
    },
    guides: {
      type: Boolean,
      default: true
    },
    center: {
      type: Boolean,
      default: true
    },
    highlight: {
      type: Boolean,
      default: true
    },
    background: {
      type: Boolean,
      default: true
    },
    autoCrop: {
      type: Boolean,
      default: true
    },
    autoCropArea: Number,
    movable: {
      type: Boolean,
      default: true
    },
    rotatable: {
      type: Boolean,
      default: true
    },
    scalable: {
      type: Boolean,
      default: true
    },
    zoomable: {
      type: Boolean,
      default: true
    },
    zoomOnTouch: {
      type: Boolean,
      default: true
    },
    zoomOnWheel: {
      type: Boolean,
      default: true
    },
    wheelZoomRatio: Number,
    cropBoxMovable: {
      type: Boolean,
      default: true
    },
    cropBoxResizable: {
      type: Boolean,
      default: true
    },
    toggleDragModeOnDblclick: {
      type: Boolean,
      default: true
    },
    // Size limitation
    minCanvasWidth: Number,
    minCanvasHeight: Number,
    minCropBoxWidth: Number,
    minCropBoxHeight: Number,
    minContainerWidth: Number,
    minContainerHeight: Number,
    // callbacks
    ready: Function,
    cropstart: Function,
    cropmove: Function,
    cropend: Function,
    crop: Function,
    zoom: Function
  },
  mounted() {
    const { containerStyle: a, src: t, alt: i, imgStyle: e, ...n } = this.$options.props, r = {};
    for (const o in n)
      this[o] !== void 0 && (r[o] = this[o]);
    this.cropper = new ke(this.$refs.img, r);
  },
  methods: {
    // Reset the image and crop box to their initial states
    reset() {
      return this.cropper.reset();
    },
    // Clear the crop box
    clear() {
      return this.cropper.clear();
    },
    // Init crop box manually
    initCrop() {
      return this.cropper.crop();
    },
    /**
     * Replace the image's src and rebuild the cropper
     * @param {string} url - The new URL.
     * @param {boolean} [onlyColorChanged] - Indicate if the new image only changed color.
     * @returns {Object} this
     */
    replace(a, t = false) {
      return this.cropper.replace(a, t);
    },
    // Enable (unfreeze) the cropper
    enable() {
      return this.cropper.enable();
    },
    // Disable (freeze) the cropper
    disable() {
      return this.cropper.disable();
    },
    // Destroy the cropper and remove the instance from the image
    destroy() {
      return this.cropper.destroy();
    },
    /**
     * Move the canvas with relative offsets
     * @param {number} offsetX - The relative offset distance on the x-axis.
     * @param {number} offsetY - The relative offset distance on the y-axis.
     * @returns {Object} this
     */
    move(a, t) {
      return this.cropper.move(a, t);
    },
    /**
     * Move the canvas to an absolute point
     * @param {number} x - The x-axis coordinate.
     * @param {number} [y=x] - The y-axis coordinate.
     * @returns {Object} this
     */
    moveTo(a, t = a) {
      return this.cropper.moveTo(a, t);
    },
    /**
     * Zoom the canvas with a relative ratio
     * @param {number} ratio - The target ratio.
     * @param {Event} _originalEvent - The original event if any.
     * @returns {Object} this
     */
    relativeZoom(a, t) {
      return this.cropper.zoom(a, t);
    },
    /**
     * Zoom the canvas to an absolute ratio
     * @param {number} ratio - The target ratio.
     * @param {Event} _originalEvent - The original event if any.
     * @returns {Object} this
     */
    zoomTo(a, t) {
      return this.cropper.zoomTo(a, t);
    },
    /**
     * Rotate the canvas with a relative degree
     * @param {number} degree - The rotate degree.
     * @returns {Object} this
     */
    rotate(a) {
      return this.cropper.rotate(a);
    },
    /**
     * Rotate the canvas to an absolute degree
     * @param {number} degree - The rotate degree.
     * @returns {Object} this
     */
    rotateTo(a) {
      return this.cropper.rotateTo(a);
    },
    /**
     * Scale the image on the x-axis.
     * @param {number} scaleX - The scale ratio on the x-axis.
     * @returns {Object} this
     */
    scaleX(a) {
      return this.cropper.scaleX(a);
    },
    /**
     * Scale the image on the y-axis.
     * @param {number} scaleY - The scale ratio on the y-axis.
     * @returns {Object} this
     */
    scaleY(a) {
      return this.cropper.scaleY(a);
    },
    /**
     * Scale the image
     * @param {number} scaleX - The scale ratio on the x-axis.
     * @param {number} [scaleY=scaleX] - The scale ratio on the y-axis.
     * @returns {Object} this
     */
    scale(a, t = a) {
      return this.cropper.scale(a, t);
    },
    /**
     * Get the cropped area position and size data (base on the original image)
     * @param {boolean} [rounded=false] - Indicate if round the data values or not.
     * @returns {Object} The result cropped data.
     */
    getData(a = false) {
      return this.cropper.getData(a);
    },
    /**
     * Set the cropped area position and size with new data
     * @param {Object} data - The new data.
     * @returns {Object} this
     */
    setData(a) {
      return this.cropper.setData(a);
    },
    /**
     * Get the container size data.
     * @returns {Object} The result container data.
     */
    getContainerData() {
      return this.cropper.getContainerData();
    },
    /**
     * Get the image position and size data.
     * @returns {Object} The result image data.
     */
    getImageData() {
      return this.cropper.getImageData();
    },
    /**
     * Get the canvas position and size data.
     * @returns {Object} The result canvas data.
     */
    getCanvasData() {
      return this.cropper.getCanvasData();
    },
    /**
     * Set the canvas position and size with new data.
     * @param {Object} data - The new canvas data.
     * @returns {Object} this
     */
    setCanvasData(a) {
      return this.cropper.setCanvasData(a);
    },
    /**
     * Get the crop box position and size data.
     * @returns {Object} The result crop box data.
     */
    getCropBoxData() {
      return this.cropper.getCropBoxData();
    },
    /**
     * Set the crop box position and size with new data.
     * @param {Object} data - The new crop box data.
     * @returns {Object} this
     */
    setCropBoxData(a) {
      return this.cropper.setCropBoxData(a);
    },
    /**
     * Get a canvas drawn the cropped image.
     * @param {Object} [options={}] - The config options.
     * @returns {HTMLCanvasElement} - The result canvas.
     */
    getCroppedCanvas(a = {}) {
      return this.cropper.getCroppedCanvas(a);
    },
    /**
     * Change the aspect ratio of the crop box.
     * @param {number} aspectRatio - The new aspect ratio.
     * @returns {Object} this
     */
    setAspectRatio(a) {
      return this.cropper.setAspectRatio(a);
    },
    /**
     * Change the drag mode.
     * @param {string} mode - The new drag mode.
     * @returns {Object} this
     */
    setDragMode(a) {
      return this.cropper.setDragMode(a);
    }
  }
};
var Yi = defineComponent({
  name: "FsCropper",
  components: {
    VueCropper: Hi
  },
  props: {
    // 对话框标题
    title: {
      type: String
    },
    // cropper的高度，默认为浏览器可视窗口高度的40%，最小270
    cropperHeight: {
      type: [String, Number]
    },
    // 对话框宽度，默认50%
    dialogWidth: {
      type: [String, Number],
      default: "50%"
    },
    // 图片大小限制，单位MB，0为不限制
    maxSize: {
      type: Number,
      default: 5
    },
    // 上传提示
    uploadTip: {
      type: String
    },
    // cropperjs的参数，详见：https://github.com/fengyuanchen/cropperjs
    cropper: {
      type: Object
    },
    // 可接收的文件后缀
    accept: {
      type: String,
      default: ".jpg, .jpeg, .png, .gif, .webp"
    },
    // 输出类型，blob,dataUrl,all
    output: {
      type: String,
      default: "blob"
      // blob
    },
    compressQuality: {
      type: Number,
      default: 0.8
    }
  },
  emits: ["cancel", "done", "ready"],
  setup(a, t) {
    const { ui: i } = B(), { t: e } = ot(), n = ref(false), r = ref(), o = ref(), s = ref(false), p = ref(), l = ref(), h2 = ref(), c = ref({
      x: 1,
      y: 1
    });
    function u() {
      n.value = false;
    }
    function g() {
      x(), t.emit("cancel");
    }
    const m = i.dialog.buildOnClosedBind(g), y = i.dialog.customClass, b = computed(() => ({
      ...m,
      [y]: "fs-cropper-dialog",
      ...i.formWrapper.buildWidthBind(i.dialog.name, "960px"),
      ...i.formWrapper.buildInitBind(i.dialog.name),
      title: a.title || e("fs.extends.cropper.title")
    }));
    function E(d) {
      n.value = true, d != null && d !== "" && (p.value = d);
    }
    function O() {
      n.value = false;
    }
    function x() {
      s.value = false, o.value != null && (o.value.value = null, o.value = null), r.value != null && r.value.clear();
    }
    function z() {
      return r.value;
    }
    const B2 = {
      cropper: z(),
      zoom: xt,
      clear: x,
      close: O,
      open: E
    };
    function f(d) {
      t.emit("ready", {
        event: d,
        ...B2
      });
    }
    function w(d) {
      return d.preventDefault(), false;
    }
    function T() {
      o.value.click();
    }
    function A(d) {
      return d.type.indexOf("image") === -1 ? (i.message.warn("请选择合适的文件类型"), false) : a.maxSize > 0 && d.size / 1024 / 1024 > a.maxSize ? (i.message.warn(`图片大小超出最大限制（${a.maxSize}MB），请重新选择.`), false) : true;
    }
    function L(d) {
      const D = d.target.files[0];
      if (D.type.indexOf("image/") === -1) {
        i.message.warn("Please select an image file");
        return;
      }
      if (typeof FileReader == "function") {
        const U = new FileReader();
        U.onload = (pt) => {
          p.value = pt.target.result, r.value.replace(pt.target.result);
        }, U.readAsDataURL(D);
      } else
        i.message.error("Sorry, FileReader API not supported");
    }
    function $(d) {
      d.preventDefault();
      const D = d.target.files || d.dataTransfer.files;
      if (D == null)
        return;
      s.value = true;
      const U = D[0];
      A(U) && (h2.value = U, L(d));
    }
    function W(d, D) {
      return D == null && (D = a.compressQuality), r.value.getCroppedCanvas().toDataURL(d, D);
    }
    async function P(d, D) {
      return D == null && (D = a.compressQuality), new Promise((U, pt) => {
        function He(Ye) {
          U(Ye);
        }
        r.value.getCroppedCanvas().toBlob(He, d, D);
      });
    }
    function _(d) {
      t.emit("done", d);
    }
    async function R(d) {
      const D = { file: d };
      if (a.output === "all") {
        const U = await P(d.type), pt = W(d.type);
        D.blob = U, D.dataUrl = pt, _(D);
        return;
      }
      if (a.output === "blob") {
        D.blob = await P(d.type), _(D);
        return;
      }
      a.output === "dataUrl" && (D.dataUrl = W(d.type), _(D));
    }
    async function F() {
      if (!s.value) {
        i.message.warn("请先选择图片");
        return;
      }
      await R(h2.value), n.value = false;
    }
    function K() {
      r.value.scaleX(c.value.x *= -1);
    }
    function it() {
      r.value.scaleY(c.value.y *= -1);
    }
    function ct() {
      l.value = JSON.stringify(r.value.getCropBoxData(), null, 4);
    }
    function wt() {
      l.value = JSON.stringify(r.value.getData(), null, 4);
    }
    function yt(d, D) {
      r.value.move(d, D);
    }
    function lt() {
      r.value.reset();
    }
    function at(d) {
      r.value.rotate(d);
    }
    function St() {
      r.value.setCropBoxData(JSON.parse(l.value));
    }
    function Rt() {
      r.value.setData(JSON.parse(l.value));
    }
    function Ie() {
      o.value.click();
    }
    function xt(d) {
      r.value.relativeZoom(d);
    }
    const ze = computed(() => {
      const d = "small";
      return [
        {
          size: d,
          round: true,
          icon: i.icons.edit,
          text: e("fs.extends.cropper.reChoose"),
          onClick() {
            T();
          }
        },
        {
          size: d,
          round: true,
          text: e("fs.extends.cropper.flipX"),
          onClick() {
            K();
          }
        },
        {
          size: d,
          round: true,
          text: e("fs.extends.cropper.flipY"),
          onClick() {
            it();
          }
        },
        {
          size: d,
          round: true,
          icon: i.icons.zoomIn,
          onClick() {
            xt(0.1);
          }
        },
        {
          size: d,
          round: true,
          icon: i.icons.zoomOut,
          onClick() {
            xt(-0.1);
          }
        },
        {
          size: d,
          round: true,
          icon: i.icons.refreshLeft,
          onClick() {
            at(90);
          }
        },
        {
          size: d,
          round: true,
          icon: i.icons.refreshRight,
          onClick() {
            at(-90);
          }
        },
        {
          size: d,
          round: true,
          icon: i.icons.refresh,
          text: e("fs.extends.cropper.reset"),
          onClick() {
            lt();
          }
        }
      ];
    }), Le = computed(() => ({
      title: e("fs.extends.cropper.title"),
      preview: e("fs.extends.cropper.preview"),
      cancel: e("fs.extends.cropper.cancel"),
      confirm: e("fs.extends.cropper.confirm"),
      chooseImage: e("fs.extends.cropper.chooseImage")
    })), Pe = computed(() => a.uploadTip != null && a.uploadTip !== "" ? a.uploadTip : a.maxSize > 0 ? `${e("fs.extends.cropper.onlySupport")} ${a.accept.replace(/,/g, "、")},
        ${e("fs.extends.cropper.sizeLimit")} ${a.maxSize}M` : `${e("fs.extends.cropper.onlySupport")}${a.accept},${e("fs.extends.cropper.sizeNoLimit")}`);
    return {
      ui: i,
      cropperRef: r,
      fileInputRef: o,
      dialogVisible: n,
      dialogBinding: b,
      isLoaded: s,
      imgSrc: p,
      data: l,
      file: h2,
      scale: c,
      computedButtons: ze,
      handleClose: u,
      setData: Rt,
      handleClosed: g,
      close: O,
      showFileChooser: Ie,
      zoom: xt,
      setCropBoxData: St,
      rotate: at,
      reset: lt,
      move: yt,
      getData: wt,
      getCropBoxData: ct,
      flipY: it,
      flipX: K,
      doCropper: F,
      doOutput: R,
      getCropImageBlob: P,
      getCropImageDataUrl: W,
      handleChange: $,
      setImage: L,
      checkFile: A,
      handleClick: T,
      preventDefault: w,
      open: E,
      clear: x,
      getCropperRef: z,
      ready: f,
      computedTexts: Le,
      computedUploadTip: Pe
    };
  },
  data() {
    return {};
  },
  computed: {
    _cropper() {
      const a = {
        aspectRatio: 1,
        ready: this.ready
      };
      return this.cropper == null ? a : Object.assign(a, this.cropper);
    },
    _cropperHeight() {
      let a = this.cropperHeight;
      return a == null && (a = document.documentElement.clientHeight * 0.55, a < 270 && (a = 270)), typeof a == "number" ? a + "px" : a;
    },
    _dialogWidth() {
      let a = this.dialogWidth;
      return a == null && (a = "50%"), typeof a == "number" ? a + "px" : a;
    }
  }
});
var Wi = { class: "fs-cropper-dialog-wrap" };
var Xi = ["accept"];
var Ui = { class: "fs-cropper-dialog__choose fs-cropper-dialog_left" };
var ji = { class: "fs-cropper-dialog__edit fs-cropper-dialog_left" };
var Vi = { class: "fs-cropper-dialog__edit-area" };
var $i = { class: "tool-bar" };
var Fi = { class: "fs-cropper-dialog__preview" };
var Gi = { class: "fs-cropper-dialog__preview-title" };
var Qi = { class: "dialog-footer" };
function Zi(a, t, i, e, n, r) {
  const o = resolveComponent("fs-button"), s = resolveComponent("vue-cropper");
  return openBlock(), createBlock(resolveDynamicComponent(a.ui.dialog.name), mergeProps({
    ref: "cropperDialogRef",
    [a.ui.dialog.visible]: a.dialogVisible,
    ["onUpdate:" + a.ui.dialog.visible]: t[2] || (t[2] = (p) => a.dialogVisible = p),
    "append-to-body": "",
    width: "900px",
    "close-on-click-modal": true
  }, a.dialogBinding, { "destroy-on-close": false }), {
    footer: withCtx(() => [
      createBaseVNode("div", Qi, [
        createVNode(o, {
          size: "small",
          text: a.computedTexts.cancel,
          onClick: a.handleClose
        }, null, 8, ["text", "onClick"]),
        createVNode(o, {
          type: "primary",
          size: "small",
          text: a.computedTexts.confirm,
          onClick: t[1] || (t[1] = (p) => a.doCropper())
        }, null, 8, ["text"])
      ])
    ]),
    default: withCtx(() => [
      createBaseVNode("div", Wi, [
        withDirectives(createBaseVNode("input", {
          ref: "fileInputRef",
          type: "file",
          accept: a.accept,
          onChange: t[0] || (t[0] = (...p) => a.handleChange && a.handleChange(...p))
        }, null, 40, Xi), [
          [vShow, false]
        ]),
        withDirectives(createBaseVNode("div", Ui, [
          createVNode(o, {
            round: "",
            text: a.computedTexts.chooseImage,
            onClick: a.showFileChooser
          }, null, 8, ["text", "onClick"]),
          createBaseVNode("p", null, toDisplayString(a.computedUploadTip), 1)
        ], 512), [
          [vShow, !a.isLoaded]
        ]),
        withDirectives(createBaseVNode("div", ji, [
          createBaseVNode("div", Vi, [
            createVNode(s, mergeProps({
              ref: "cropperRef",
              src: a.imgSrc,
              preview: ".preview",
              style: { height: a._cropperHeight }
            }, a._cropper), null, 16, ["src", "style"])
          ]),
          createBaseVNode("div", $i, [
            (openBlock(), createBlock(resolveDynamicComponent(a.ui.buttonGroup.name), null, {
              default: withCtx(() => [
                (openBlock(true), createElementBlock(Fragment, null, renderList(a.computedButtons, (p, l) => (openBlock(), createBlock(o, mergeProps({
                  key: l,
                  ref_for: true
                }, p), null, 16))), 128))
              ]),
              _: 1
            }))
          ])
        ], 512), [
          [vShow, a.isLoaded]
        ]),
        createBaseVNode("div", Fi, [
          createBaseVNode("span", Gi, toDisplayString(a.computedTexts.preview), 1),
          t[3] || (t[3] = createBaseVNode("div", { class: "fs-cropper-dialog__preview-120 preview" }, null, -1)),
          createBaseVNode("div", {
            class: normalizeClass(["fs-cropper-dialog__preview-65 preview", { round: a._cropper.aspectRatio === 1 }])
          }, null, 2)
        ])
      ])
    ]),
    _: 1
  }, 16);
}
var ea = he(Yi, [["render", Zi]]);
export {
  ea as default
};
/*! Bundled license information:

@fast-crud/fast-extends/dist/fs-cropper-c1cf1841.mjs:
  (*!
   * Cropper.js v1.6.2
   * https://fengyuanchen.github.io/cropperjs
   *
   * Copyright 2015-present Chen Fengyuan
   * Released under the MIT license
   *
   * Date: 2024-04-21T07:43:05.335Z
   *)
*/
//# sourceMappingURL=fs-cropper-c1cf1841-WTDECIRZ.js.map
