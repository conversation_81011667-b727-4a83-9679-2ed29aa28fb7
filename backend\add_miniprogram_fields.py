#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from django.db import connection

def add_miniprogram_fields():
    """添加小程序相关字段到用户表"""
    cursor = connection.cursor()
    
    # 检查字段是否已存在
    cursor.execute("DESCRIBE plant_system_users")
    existing_fields = [field[0] for field in cursor.fetchall()]
    
    # 要添加的字段
    fields_to_add = [
        ("openid", "VARCHAR(64) NULL UNIQUE COMMENT '微信openid'"),
        ("unionid", "VARCHAR(64) NULL COMMENT '微信unionid'"),
        ("session_key", "VARCHAR(64) NULL COMMENT '会话密钥'"),
        ("nickname", "VARCHAR(50) NULL COMMENT '微信昵称'"),
        ("avatar_url", "TEXT NULL COMMENT '微信头像'"),
        ("points", "INT DEFAULT 0 COMMENT '积分'"),
        ("level", "INT DEFAULT 1 COMMENT '等级'")
    ]
    
    for field_name, field_definition in fields_to_add:
        if field_name not in existing_fields:
            sql = f"ALTER TABLE plant_system_users ADD COLUMN {field_name} {field_definition}"
            try:
                cursor.execute(sql)
                print(f"✓ 成功添加字段: {field_name}")
            except Exception as e:
                print(f"✗ 添加字段失败 {field_name}: {e}")
        else:
            print(f"- 字段已存在: {field_name}")
    
    print("\n字段添加完成！")

if __name__ == "__main__":
    add_miniprogram_fields()
