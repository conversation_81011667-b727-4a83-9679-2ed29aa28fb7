/**
 * 植物相关状态管理
 */
import { defineStore } from 'pinia'
import api from '@/api/index.js'

export const usePlantStore = defineStore('plant', {
	state: () => ({
		// 植物列表
		plantList: [],
		// 推荐植物
		recommendPlants: [],
		// 植物分类
		categories: [],
		// 搜索历史
		searchHistory: [],
		// 当前查看的植物详情
		currentPlant: null,
		// 用户植物列表
		userPlants: [],
		// 识别历史
		identifyHistory: [],
		// 加载状态
		loading: {
			plantList: false,
			recommend: false,
			categories: false,
			userPlants: false,
			identify: false
		}
	}),
	
	getters: {
		// 获取热门植物分类
		popularCategories: (state) => {
			return state.categories.filter(cat => cat.is_popular)
		},
		
		// 获取最近搜索
		recentSearches: (state) => {
			return state.searchHistory.slice(0, 10)
		},
		
		// 获取用户植物数量
		userPlantsCount: (state) => {
			return state.userPlants.length
		},
		
		// 获取健康植物数量
		healthyPlantsCount: (state) => {
			return state.userPlants.filter(plant => plant.health_status === 1).length
		}
	},
	
	actions: {
		// 初始化植物数据
		async initPlantData() {
			try {
				await Promise.all([
					this.getCategories(),
					this.getRecommendPlants()
				])
			} catch (error) {
				console.error('初始化植物数据失败:', error)
			}
		},
		
		// 获取植物列表
		async getPlantList(params = {}) {
			this.loading.plantList = true
			try {
				const response = await api.plant.getList(params)
				
				if (params.page === 1) {
					this.plantList = response.results || response
				} else {
					this.plantList.push(...(response.results || response))
				}
				
				return response
			} catch (error) {
				console.error('获取植物列表失败:', error)
				throw error
			} finally {
				this.loading.plantList = false
			}
		},
		
		// 获取植物详情
		async getPlantDetail(id) {
			try {
				const plant = await api.plant.getDetail(id)
				this.currentPlant = plant
				return plant
			} catch (error) {
				console.error('获取植物详情失败:', error)
				throw error
			}
		},
		
		// 搜索植物
		async searchPlants(keyword, params = {}) {
			try {
				const searchParams = {
					q: keyword,
					...params
				}
				
				const response = await api.plant.search(searchParams)
				
				// 添加到搜索历史
				this.addSearchHistory(keyword)
				
				return response
			} catch (error) {
				console.error('搜索植物失败:', error)
				throw error
			}
		},
		
		// 获取推荐植物
		async getRecommendPlants(params = {}) {
			this.loading.recommend = true
			try {
				const plants = await api.plant.getRecommend(params)
				this.recommendPlants = plants
				return plants
			} catch (error) {
				console.error('获取推荐植物失败:', error)
				throw error
			} finally {
				this.loading.recommend = false
			}
		},
		
		// 获取植物分类
		async getCategories() {
			this.loading.categories = true
			try {
				const categories = await api.plant.getCategories()
				this.categories = categories
				return categories
			} catch (error) {
				console.error('获取植物分类失败:', error)
				throw error
			} finally {
				this.loading.categories = false
			}
		},
		
		// 获取用户植物列表
		async getUserPlants(params = {}) {
			this.loading.userPlants = true
			try {
				const response = await api.userPlant.getList(params)
				this.userPlants = response.results || response
				return response
			} catch (error) {
				console.error('获取用户植物失败:', error)
				throw error
			} finally {
				this.loading.userPlants = false
			}
		},
		
		// 创建用户植物
		async createUserPlant(data) {
			try {
				const plant = await api.userPlant.create(data)
				this.userPlants.unshift(plant)
				return plant
			} catch (error) {
				console.error('创建用户植物失败:', error)
				throw error
			}
		},
		
		// 更新用户植物
		async updateUserPlant(id, data) {
			try {
				const plant = await api.userPlant.update(id, data)
				const index = this.userPlants.findIndex(p => p.id === id)
				if (index !== -1) {
					this.userPlants[index] = plant
				}
				return plant
			} catch (error) {
				console.error('更新用户植物失败:', error)
				throw error
			}
		},
		
		// 删除用户植物
		async deleteUserPlant(id) {
			try {
				await api.userPlant.delete(id)
				const index = this.userPlants.findIndex(p => p.id === id)
				if (index !== -1) {
					this.userPlants.splice(index, 1)
				}
			} catch (error) {
				console.error('删除用户植物失败:', error)
				throw error
			}
		},
		
		// 植物识别
		async identifyPlant(filePath) {
			this.loading.identify = true
			try {
				const result = await api.identify.upload(filePath)
				
				// 添加到识别历史
				this.identifyHistory.unshift({
					id: result.id,
					image: filePath,
					result: result.plants || [],
					create_time: new Date()
				})
				
				return result
			} catch (error) {
				console.error('植物识别失败:', error)
				throw error
			} finally {
				this.loading.identify = false
			}
		},
		
		// 获取识别历史
		async getIdentifyHistory(params = {}) {
			try {
				const response = await api.identify.getHistory(params)
				this.identifyHistory = response.results || response
				return response
			} catch (error) {
				console.error('获取识别历史失败:', error)
				throw error
			}
		},
		
		// 添加搜索历史
		addSearchHistory(keyword) {
			if (!keyword || keyword.trim() === '') return
			
			// 去重并添加到开头
			const history = this.searchHistory.filter(item => item !== keyword)
			history.unshift(keyword)
			
			// 限制历史记录数量
			this.searchHistory = history.slice(0, 20)
			
			// 持久化存储
			uni.setStorageSync('searchHistory', this.searchHistory)
		},
		
		// 清除搜索历史
		clearSearchHistory() {
			this.searchHistory = []
			uni.removeStorageSync('searchHistory')
		},
		
		// 初始化搜索历史
		initSearchHistory() {
			try {
				const history = uni.getStorageSync('searchHistory')
				if (history) {
					this.searchHistory = history
				}
			} catch (error) {
				console.error('初始化搜索历史失败:', error)
			}
		},
		
		// 设置当前植物
		setCurrentPlant(plant) {
			this.currentPlant = plant
		},
		
		// 清除当前植物
		clearCurrentPlant() {
			this.currentPlant = null
		}
	}
})
