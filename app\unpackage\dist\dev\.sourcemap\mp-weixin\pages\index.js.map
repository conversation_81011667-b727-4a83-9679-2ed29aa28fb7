{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/index.vue?5e9e", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/index.vue?68fa", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/index.vue?1f56", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/index.vue?141b", "uni-app:///pages/index.vue", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/index.vue?934a", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/index.vue?53b4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "statusBarHeight", "navHeight", "banners", "plantCategories", "recommendPlants", "encyclopediaPlants", "onLoad", "methods", "getFullImageUrl", "loadData", "Promise", "console", "uni", "title", "icon", "loadBanners", "id", "image", "loadPlantCategories", "name", "loadRecommendPlants", "description", "loadEncyclopediaPlants", "handleCategoryClick", "handlePlantClick"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqL;AACrL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4rB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACmEhtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC,aACA,qBACA,6BACA,6BACA,+BACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA,kBACA;oBAAAC;oBAAAC;kBAAA,GACA;oBAAAD;oBAAAC;kBAAA,GACA;oBAAAD;oBAAAC;kBAAA,EACA;gBACA;kBACAN;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAO;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA,0BACA;oBAAAF;oBAAAG;oBAAAL;kBAAA,GACA;oBAAAE;oBAAAG;oBAAAL;kBAAA,GACA;oBAAAE;oBAAAG;oBAAAL;kBAAA,GACA;oBAAAE;oBAAAG;oBAAAL;kBAAA,GACA;oBAAAE;oBAAAG;oBAAAL;kBAAA,GACA;oBAAAE;oBAAAG;oBAAAL;kBAAA,EACA;gBACA;kBACAH;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAS;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA,0BACA;oBAAAJ;oBAAAG;oBAAAE;oBAAAJ;kBAAA,GACA;oBAAAD;oBAAAG;oBAAAE;oBAAAJ;kBAAA,GACA;oBAAAD;oBAAAG;oBAAAE;oBAAAJ;kBAAA,GACA;oBAAAD;oBAAAG;oBAAAE;oBAAAJ;kBAAA,EACA;gBACA;kBACAN;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAW;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA,6BACA;oBAAAN;oBAAAG;oBAAAE;oBAAAJ;kBAAA,GACA;oBAAAD;oBAAAG;oBAAAE;oBAAAJ;kBAAA,GACA;oBAAAD;oBAAAG;oBAAAE;oBAAAJ;kBAAA,GACA;oBAAAD;oBAAAG;oBAAAE;oBAAAJ;kBAAA,EACA;gBACA;kBACAN;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAY;MACAX;QACAC;QACAC;MACA;IACA;IAEA;IACAU;MACAZ;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChMA;AAAA;AAAA;AAAA;AAAu0C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACA31C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2a183b29&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2a183b29&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2a183b29\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2a183b29&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-nav\" :style=\"{paddingTop: statusBarHeight + 'px'}\">\r\n      <view class=\"nav-content\">\r\n        <view class=\"title-text\">\r\n          <text>植物之家</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 内容区域 -->\r\n    <scroll-view scroll-y class=\"content\" :style=\"{paddingTop: navHeight + 'px'}\">\r\n      <!-- 轮播图 -->\r\n      <swiper class=\"banner\" indicator-dots autoplay circular :interval=\"3000\" :duration=\"500\">\r\n        <swiper-item v-for=\"(item, index) in banners\" :key=\"index\">\r\n          <image :src=\"item.image\" mode=\"aspectFill\"></image>\r\n        </swiper-item>\r\n      </swiper>\r\n\r\n      <!-- 植物分类导航 -->\r\n      <view class=\"category-section\">\r\n        <view class=\"category-item\" v-for=\"(item, index) in plantCategories\" :key=\"index\" @click=\"handleCategoryClick(item)\">\r\n          <image\r\n            :src=\"item.icon\"\r\n            mode=\"aspectFit\"\r\n          ></image>\r\n          <text>{{item.name}}</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 推荐植物 -->\r\n      <view class=\"plant-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"title\">推荐植物</text>\r\n        </view>\r\n        <view class=\"plant-list\">\r\n          <view class=\"plant-item\" v-for=\"(item, index) in recommendPlants\" :key=\"index\" @click=\"handlePlantClick(item)\">\r\n            <image :src=\"item.image\" mode=\"aspectFill\"></image>\r\n            <view class=\"plant-info\">\r\n              <text class=\"plant-name\">{{item.name}}</text>\r\n              <text class=\"plant-desc\">{{item.description}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 植物百科 -->\r\n      <view class=\"plant-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"title\">植物百科</text>\r\n        </view>\r\n        <view class=\"plant-list\">\r\n          <view class=\"plant-item\" v-for=\"(item, index) in encyclopediaPlants\" :key=\"index\" @click=\"handlePlantClick(item)\">\r\n            <image :src=\"item.image\" mode=\"aspectFill\"></image>\r\n            <view class=\"plant-info\">\r\n              <text class=\"plant-name\">{{item.name}}</text>\r\n              <text class=\"plant-desc\">{{item.description}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport config from '@/config.js'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      statusBarHeight: 20,\r\n      navHeight: 64,\r\n      banners: [],\r\n      plantCategories: [],\r\n      recommendPlants: [],\r\n      encyclopediaPlants: []\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 获取状态栏高度\r\n    const systemInfo = uni.getSystemInfoSync()\r\n    this.statusBarHeight = systemInfo.statusBarHeight\r\n    this.navHeight = this.statusBarHeight + 44\r\n\r\n    // 加载数据\r\n    this.loadData()\r\n  },\r\n  methods: {\r\n    // 获取完整的图片URL\r\n    getFullImageUrl(imageUrl) {\r\n      if (!imageUrl) return '/static/images/default.png'\r\n      if (imageUrl.startsWith('http')) return imageUrl\r\n      return config.baseUrl + imageUrl\r\n    },\r\n\r\n    // 加载数据\r\n    async loadData() {\r\n      try {\r\n        await Promise.all([\r\n          this.loadBanners(),\r\n          this.loadPlantCategories(),\r\n          this.loadRecommendPlants(),\r\n          this.loadEncyclopediaPlants()\r\n        ])\r\n      } catch (error) {\r\n        console.error('加载数据失败:', error)\r\n        uni.showToast({\r\n          title: '加载失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 加载轮播图\r\n    async loadBanners() {\r\n      try {\r\n        // 使用植物相关的默认轮播图\r\n        this.banners = [\r\n          { id: 1, image: '/static/images/banner/plant01.jpg' },\r\n          { id: 2, image: '/static/images/banner/plant02.jpg' },\r\n          { id: 3, image: '/static/images/banner/plant03.jpg' }\r\n        ]\r\n      } catch (error) {\r\n        console.error('加载轮播图失败:', error)\r\n      }\r\n    },\r\n\r\n    // 加载植物分类\r\n    async loadPlantCategories() {\r\n      try {\r\n        // 使用植物相关的分类\r\n        this.plantCategories = [\r\n          { id: 1, name: '观叶植物', icon: '/static/images/plant/leaf.png' },\r\n          { id: 2, name: '观花植物', icon: '/static/images/plant/flower.png' },\r\n          { id: 3, name: '多肉植物', icon: '/static/images/plant/succulent.png' },\r\n          { id: 4, name: '水培植物', icon: '/static/images/plant/hydroponic.png' },\r\n          { id: 5, name: '盆景植物', icon: '/static/images/plant/bonsai.png' },\r\n          { id: 6, name: '药用植物', icon: '/static/images/plant/medicinal.png' }\r\n        ]\r\n      } catch (error) {\r\n        console.error('加载植物分类失败:', error)\r\n      }\r\n    },\r\n\r\n    // 加载推荐植物\r\n    async loadRecommendPlants() {\r\n      try {\r\n        // 使用植物相关的推荐数据\r\n        this.recommendPlants = [\r\n          { id: 1, name: '绿萝', description: '净化空气，易养护', image: '/static/images/plant/lvluo.jpg' },\r\n          { id: 2, name: '吊兰', description: '观叶植物，适合室内', image: '/static/images/plant/diaoLan.jpg' },\r\n          { id: 3, name: '虎皮兰', description: '耐旱易养，净化空气', image: '/static/images/plant/hupilan.jpg' },\r\n          { id: 4, name: '发财树', description: '寓意吉祥，观叶植物', image: '/static/images/plant/facaishu.jpg' }\r\n        ]\r\n      } catch (error) {\r\n        console.error('加载推荐植物失败:', error)\r\n      }\r\n    },\r\n\r\n    // 加载植物百科\r\n    async loadEncyclopediaPlants() {\r\n      try {\r\n        // 使用植物百科数据\r\n        this.encyclopediaPlants = [\r\n          { id: 5, name: '仙人掌', description: '多肉植物，耐旱性强', image: '/static/images/plant/xianrenzhang.jpg' },\r\n          { id: 6, name: '芦荟', description: '药用植物，美容护肤', image: '/static/images/plant/luhui.jpg' },\r\n          { id: 7, name: '薄荷', description: '香草植物，清香怡人', image: '/static/images/plant/bohe.jpg' },\r\n          { id: 8, name: '茉莉花', description: '观花植物，香气浓郁', image: '/static/images/plant/molihua.jpg' }\r\n        ]\r\n      } catch (error) {\r\n        console.error('加载植物百科失败:', error)\r\n      }\r\n    },\r\n\r\n    // 点击分类\r\n    handleCategoryClick(item) {\r\n      uni.showToast({\r\n        title: `查看${item.name}`,\r\n        icon: 'none'\r\n      })\r\n    },\r\n\r\n    // 点击植物\r\n    handlePlantClick(item) {\r\n      uni.showToast({\r\n        title: `查看${item.name}详情`,\r\n        icon: 'none'\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n  width: 100%;\r\n  height: 100vh;\r\n  background-color: #f5f5f7;\r\n  position: relative;\r\n}\r\n\r\n.custom-nav {\r\n  position: fixed;\r\n  width: 100%;\r\n  background-color: #3c96f3;\r\n  z-index: 999;\r\n  \r\n  .nav-content {\r\n    height: 44px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 0 15px;\r\n\r\n    .title-text {\r\n      text {\r\n        color: #fff;\r\n        font-size: 36rpx;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.content {\r\n  width: 100%;\r\n  height: 100%;\r\n  \r\n  .banner {\r\n    width: 100%;\r\n    height: 350rpx;\r\n    \r\n    image {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  \r\n  .category-section {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    padding: 20rpx 0;\r\n    background-color: #fff;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .category-item {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      padding: 10rpx;\r\n      border-radius: 12rpx;\r\n      transition: all 0.3s ease;\r\n\r\n      &:active {\r\n        background-color: #f5f5f5;\r\n        transform: scale(0.95);\r\n      }\r\n\r\n      image {\r\n        width: 80rpx;\r\n        height: 80rpx;\r\n        margin-bottom: 10rpx;\r\n        border-radius: 8rpx;\r\n      }\r\n\r\n      text {\r\n        font-size: 24rpx;\r\n        color: #333;\r\n        text-align: center;\r\n        line-height: 1.2;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .plant-section {\r\n    background-color: #fff;\r\n    margin-bottom: 20rpx;\r\n    padding: 20rpx;\r\n\r\n    .section-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 20rpx;\r\n\r\n      .title {\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n        color: #2d8659;\r\n      }\r\n    }\r\n\r\n    .plant-list {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      justify-content: space-between;\r\n\r\n      .plant-item {\r\n        width: 48%;\r\n        background-color: #f9f9f9;\r\n        border-radius: 10rpx;\r\n        overflow: hidden;\r\n        margin-bottom: 20rpx;\r\n\r\n        image {\r\n          width: 100%;\r\n          height: 320rpx;\r\n        }\r\n\r\n        .plant-info {\r\n          padding: 15rpx;\r\n\r\n          .plant-name {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n            font-weight: bold;\r\n            margin-bottom: 8rpx;\r\n          }\r\n\r\n          .plant-desc {\r\n            font-size: 24rpx;\r\n            color: #666;\r\n            line-height: 1.4;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2a183b29&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2a183b29&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751643652895\n      var cssReload = require(\"D:/System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}