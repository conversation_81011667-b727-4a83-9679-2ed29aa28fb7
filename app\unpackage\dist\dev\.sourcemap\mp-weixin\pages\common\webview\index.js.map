{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/common/webview/index.vue?537b", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/common/webview/index.vue?d22a", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/common/webview/index.vue?7d15", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/common/webview/index.vue?edf9", "uni-app:///pages/common/webview/index.vue", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/common/webview/index.vue?9391", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/common/webview/index.vue?812f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "url", "htmlContent", "type", "onLoad", "console", "uni", "title", "methods", "loadContentByType", "generateUserAgreement", "generatePrivacyPolicy"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;eCU9uB;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;;IAEA;IACA;MACAC;QACAC;MACA;IACA;;IAEA;IACA;MACA;MACA;IACA;MACA;MACAF;;MAEA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAG;IACA;IACAC;MACAJ;MAEA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAK;MACAL;MACA,y2NAqCA;IACA;IAEA;IACAM;MACAN;MACA,whOA+CA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxJA;AAAA;AAAA;AAAA;AAA63C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/common/webview/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/common/webview/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4475da11&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4475da11&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4475da11\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/common/webview/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4475da11&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"webview-container\">\r\n    <!-- 使用rich-text组件直接显示HTML内容 -->\r\n    <scroll-view scroll-y class=\"content-container\">\r\n      <rich-text :nodes=\"htmlContent\"></rich-text>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      url: '',\r\n      htmlContent: '',\r\n      type: ''\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    console.log('webview onLoad options:', options)\r\n    \r\n    // 设置页面标题\r\n    if (options.title) {\r\n      uni.setNavigationBarTitle({\r\n        title: decodeURIComponent(options.title)\r\n      })\r\n    }\r\n    \r\n    // 根据类型或URL加载内容\r\n    if (options.type) {\r\n      this.type = decodeURIComponent(options.type)\r\n      this.loadContentByType(this.type)\r\n    } else if (options.url) {\r\n      this.url = decodeURIComponent(options.url)\r\n      console.log('加载URL:', this.url)\r\n      \r\n      // 根据URL判断加载哪种内容\r\n      if (this.url.includes('protocol.html')) {\r\n        this.loadContentByType('agreement')\r\n      } else if (this.url.includes('privacy.html')) {\r\n        this.loadContentByType('privacy')\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 根据类型加载内容\r\n    loadContentByType(type) {\r\n      console.log('loadContentByType:', type)\r\n      \r\n      if (type === 'agreement') {\r\n        this.generateUserAgreement()\r\n      } else if (type === 'privacy') {\r\n        this.generatePrivacyPolicy()\r\n      }\r\n    },\r\n    \r\n    // 生成用户协议内容\r\n    generateUserAgreement() {\r\n      console.log('生成用户协议内容')\r\n      this.htmlContent = `\r\n        <div style=\"padding: 20px;\">\r\n          <h2 style=\"text-align: center; font-size: 18px; margin-bottom: 20px;\">用户协议</h2>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">欢迎使用我们的电商平台服务！</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">本协议是您与电商平台之间关于使用电商平台服务所订立的契约。请您仔细阅读本协议，如果您不同意本协议的任何条款，请不要注册或使用电商平台服务。如您点击\"同意\"并进行下一步操作，或您继续使用电商平台服务，均视为您已仔细阅读并完全同意本协议的全部条款。</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">1. 服务内容</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">电商平台是一个线上交易平台，为用户提供商品浏览、购买、支付、评价等服务。</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">2. 账户注册与安全</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">2.1 您在使用本服务前需要注册账号。您承诺提供真实、准确、完整的注册信息。</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">2.2 您应当妥善保管账号及密码信息，因您保管不善可能导致的任何损失由您自行承担。</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">3. 用户行为规范</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">3.1 您应当遵守中华人民共和国法律法规、社会公德，不得利用本服务从事任何违法或不当的活动。</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">3.2 您不得利用本服务发布、传播或存储任何违反法律法规的内容。</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">4. 知识产权</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">4.1 电商平台所包含的全部智力成果，包括但不限于商标、专利、著作权等，均归电商平台所有。</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">4.2 未经电商平台许可，任何人不得擅自使用、修改、复制、公开传播、改变、散布、发行或公开发表电商平台的程序或内容。</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">5. 服务变更、中断或终止</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">5.1 电商平台保留随时修改或中断服务而不需知会用户的权利。</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">5.2 电商平台有权基于单方判断，包括但不限于电商平台认为您违反本协议的条款，终止您的账号或本服务的使用。</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">6. 免责声明</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">6.1 电商平台不对本服务的及时性、安全性、准确性做出任何承诺和保证。</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">6.2 对于因不可抗力或电商平台不能控制的原因造成的服务中断或其它缺陷，电商平台不承担任何责任。</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">7. 协议修改</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">7.1 电商平台有权在必要时修改本协议条款，协议条款一旦发生变动，将会在相关页面上公布修改后的协议条款。</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">7.2 如果不同意电商平台对本协议相关条款所做的修改，您有权停止使用本服务。如果您继续使用本服务，则视为您接受协议条款的修改。</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">8. 法律适用与争议解决</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">8.1 本协议适用中华人民共和国法律。</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">8.2 因本协议引起的或与本协议有关的任何争议，应提交至电商平台所在地有管辖权的人民法院诉讼解决。</p>\r\n        </div>\r\n      `\r\n    },\r\n    \r\n    // 生成隐私政策内容\r\n    generatePrivacyPolicy() {\r\n      console.log('生成隐私政策内容')\r\n      this.htmlContent = `\r\n        <div style=\"padding: 20px;\">\r\n          <h2 style=\"text-align: center; font-size: 18px; margin-bottom: 20px;\">隐私政策</h2>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">本隐私政策描述了电商平台如何收集、使用和保护您的个人信息。我们重视您的隐私，并致力于保护您的个人信息安全。</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">1. 信息收集</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">1.1 我们可能收集的个人信息包括：</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 5px;\">- 注册信息：如姓名、手机号码、电子邮件地址等</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 5px;\">- 交易信息：如订单记录、支付信息、收货地址等</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 5px;\">- 使用记录：如浏览历史、搜索记录、点击行为等</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 5px;\">- 设备信息：如设备型号、操作系统、唯一设备标识符等</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">- 位置信息：如IP地址、GPS位置（经您授权后）等</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">2. 信息使用</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">2.1 我们可能将收集的信息用于以下目的：</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 5px;\">- 提供、维护和改进我们的服务</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 5px;\">- 处理您的订单和支付</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 5px;\">- 向您推送个性化内容和广告</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 5px;\">- 进行数据分析和研究</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 5px;\">- 与您沟通并回应您的请求</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">- 检测和预防欺诈行为</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">3. 信息共享</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">3.1 除以下情况外，我们不会与第三方共享您的个人信息：</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 5px;\">- 经您明确同意</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 5px;\">- 与我们的关联公司共享</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 5px;\">- 与合作伙伴共享（如物流公司）以完成您要求的服务</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">- 遵循法律法规的规定或政府主管部门的强制性要求</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">4. 信息安全</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">4.1 我们采取各种安全技术和程序，以防信息的丢失、不当使用、未经授权阅览或披露。</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">4.2 但请理解，互联网环境并非百分之百安全，我们将尽力确保您的信息安全，但无法承诺信息绝对安全。</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">5. Cookie的使用</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">5.1 我们使用Cookie和类似技术来提升您的使用体验。</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">5.2 您可以通过浏览器设置拒绝Cookie，但这可能影响您使用我们服务的某些功能。</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">6. 未成年人保护</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">6.1 我们重视对未成年人个人信息的保护。如您是未满18周岁的未成年人，请在法定监护人的陪同下阅读本隐私政策，并在法定监护人的同意下使用我们的服务。</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">7. 隐私政策更新</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">7.1 我们可能适时修订本隐私政策，并在修订后发布。</p>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">7.2 如果相关更新造成您权利的实质减少，我们将在更新前通过显著方式通知您。</p>\r\n          \r\n          <h3 style=\"font-size: 16px; margin: 15px 0 10px 0;\">8. 联系我们</h3>\r\n          <p style=\"font-size: 14px; line-height: 1.5; margin-bottom: 10px;\">8.1 如果您对本隐私政策有任何疑问，请联系我们的客服。</p>\r\n        </div>\r\n      `\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.webview-container {\r\n  width: 100%;\r\n  height: 100vh;\r\n  background-color: #fff;\r\n}\r\n\r\n.content-container {\r\n  padding: 20rpx;\r\n  height: 100vh;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4475da11&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4475da11&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751643652855\n      var cssReload = require(\"D:/System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}